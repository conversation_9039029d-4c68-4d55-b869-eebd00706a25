{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/lib/errors.ts"], "sourcesContent": ["// 错误处理工具类\n\nexport class AppError extends Error {\n  public readonly statusCode: number;\n  public readonly isOperational: boolean;\n\n  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {\n    super(message);\n    this.statusCode = statusCode;\n    this.isOperational = isOperational;\n\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nexport class ValidationError extends AppError {\n  constructor(message: string) {\n    super(message, 400);\n  }\n}\n\nexport class NotFoundError extends AppError {\n  constructor(resource: string = 'Resource') {\n    super(`${resource} not found`, 404);\n  }\n}\n\nexport class ConflictError extends AppError {\n  constructor(message: string) {\n    super(message, 409);\n  }\n}\n\n// 错误处理中间件\nexport function handleApiError(error: unknown): Response {\n  console.error('API Error:', error);\n\n  if (error instanceof AppError) {\n    return Response.json(\n      { error: error.message },\n      { status: error.statusCode }\n    );\n  }\n\n  if (error instanceof Error) {\n    return Response.json(\n      { error: error.message },\n      { status: 500 }\n    );\n  }\n\n  return Response.json(\n    { error: 'Internal server error' },\n    { status: 500 }\n  );\n}\n\n// 异步错误包装器\nexport function asyncHandler<T extends any[], R>(\n  fn: (...args: T) => Promise<R>\n) {\n  return async (...args: T): Promise<R> => {\n    try {\n      return await fn(...args);\n    } catch (error) {\n      throw error;\n    }\n  };\n}\n\n// 客户端错误处理\nexport function handleClientError(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return '发生未知错误';\n}\n\n// 表单验证错误\nexport interface ValidationErrors {\n  [key: string]: string[];\n}\n\nexport function formatValidationErrors(errors: ValidationErrors): string {\n  const messages: string[] = [];\n  for (const [field, fieldErrors] of Object.entries(errors)) {\n    messages.push(`${field}: ${fieldErrors.join(', ')}`);\n  }\n  return messages.join('; ');\n}\n"], "names": [], "mappings": "AAAA,UAAU;;;;;;;;;;;AAEH,MAAM,iBAAiB;IACZ,WAAmB;IACnB,cAAuB;IAEvC,YAAY,OAAe,EAAE,aAAqB,GAAG,EAAE,gBAAyB,IAAI,CAAE;QACpF,KAAK,CAAC;QACN,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,aAAa,GAAG;QAErB,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;IAChD;AACF;AAEO,MAAM,wBAAwB;IACnC,YAAY,OAAe,CAAE;QAC3B,KAAK,CAAC,SAAS;IACjB;AACF;AAEO,MAAM,sBAAsB;IACjC,YAAY,WAAmB,UAAU,CAAE;QACzC,KAAK,CAAC,GAAG,SAAS,UAAU,CAAC,EAAE;IACjC;AACF;AAEO,MAAM,sBAAsB;IACjC,YAAY,OAAe,CAAE;QAC3B,KAAK,CAAC,SAAS;IACjB;AACF;AAGO,SAAS,eAAe,KAAc;IAC3C,QAAQ,KAAK,CAAC,cAAc;IAE5B,IAAI,iBAAiB,UAAU;QAC7B,OAAO,SAAS,IAAI,CAClB;YAAE,OAAO,MAAM,OAAO;QAAC,GACvB;YAAE,QAAQ,MAAM,UAAU;QAAC;IAE/B;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,SAAS,IAAI,CAClB;YAAE,OAAO,MAAM,OAAO;QAAC,GACvB;YAAE,QAAQ;QAAI;IAElB;IAEA,OAAO,SAAS,IAAI,CAClB;QAAE,OAAO;IAAwB,GACjC;QAAE,QAAQ;IAAI;AAElB;AAGO,SAAS,aACd,EAA8B;IAE9B,OAAO,OAAO,GAAG;QACf,IAAI;YACF,OAAO,MAAM,MAAM;QACrB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;AAGO,SAAS,kBAAkB,KAAc;IAC9C,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,OAAO;AACT;AAOO,SAAS,uBAAuB,MAAwB;IAC7D,MAAM,WAAqB,EAAE;IAC7B,KAAK,MAAM,CAAC,OAAO,YAAY,IAAI,OAAO,OAAO,CAAC,QAAS;QACzD,SAAS,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,YAAY,IAAI,CAAC,OAAO;IACrD;IACA,OAAO,SAAS,IAAI,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/lib/validation.ts"], "sourcesContent": ["// 数据验证工具\n\nimport { z } from 'zod';\nimport { ValidationError } from './errors';\n\n// 基础验证规则\nexport const baseValidation = {\n  id: z.string().min(1, 'ID不能为空'),\n  name: z.string().min(1, '名称不能为空').max(100, '名称不能超过100个字符'),\n  description: z.string().max(500, '描述不能超过500个字符').optional(),\n  price: z.number().positive('价格必须大于0'),\n  phone: z.string().regex(/^1[3-9]\\d{9}$/, '请输入有效的手机号码').optional(),\n  email: z.string().email('请输入有效的邮箱地址').optional(),\n  url: z.string().url('请输入有效的URL').optional(),\n};\n\n// 产品验证模式\nexport const ProductValidationSchema = z.object({\n  id: baseValidation.id.optional(),\n  name: baseValidation.name,\n  description: baseValidation.description,\n  price: baseValidation.price,\n  productTypeId: baseValidation.id,\n  image: baseValidation.url.optional(),\n  ingredients: z.array(z.string()).default([]),\n  spicyLevel: z.number().int().min(0).max(5).default(0),\n  available: z.boolean().default(true),\n  stock: z.number().int().min(0).default(0),\n  minStock: z.number().int().min(0).default(5),\n  isActive: z.boolean().default(true),\n  publishedAt: z.string().datetime().optional().nullable(),\n});\n\n// 产品类型验证模式\nexport const ProductTypeValidationSchema = z.object({\n  id: baseValidation.id.optional(),\n  name: baseValidation.name,\n  description: baseValidation.description,\n  displayOrder: z.number().int().min(0).default(0),\n  isActive: z.boolean().default(true),\n});\n\n// 门店验证模式\nexport const StoreValidationSchema = z.object({\n  id: baseValidation.id.optional(),\n  name: baseValidation.name,\n  address: z.string().min(1, '地址不能为空').max(200, '地址不能超过200个字符'),\n  phone: baseValidation.phone,\n  latitude: z.number().min(-90).max(90).optional().nullable(),\n  longitude: z.number().min(-180).max(180).optional().nullable(),\n  openTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式(HH:MM)').optional(),\n  closeTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式(HH:MM)').optional(),\n  isOpen: z.boolean().default(true),\n  image: baseValidation.url.optional(),\n});\n\n// 搜索参数验证模式\nexport const SearchParamsSchema = z.object({\n  q: z.string().max(100, '搜索关键词不能超过100个字符').optional(),\n  category: z.string().optional(),\n  sortBy: z.enum(['createdAt', 'publishedAt', 'name', 'price']).default('createdAt'),\n  sortOrder: z.enum(['asc', 'desc']).default('desc'),\n  page: z.number().int().min(1).default(1),\n  limit: z.number().int().min(1).max(100).default(12),\n});\n\n// 验证函数\nexport function validateData<T>(schema: z.ZodSchema<T>, data: unknown): T {\n  try {\n    return schema.parse(data);\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      const messages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);\n      throw new ValidationError(messages.join(', '));\n    }\n    throw error;\n  }\n}\n\n// 安全解析（不抛出错误）\nexport function safeValidateData<T>(schema: z.ZodSchema<T>, data: unknown): {\n  success: boolean;\n  data?: T;\n  errors?: string[];\n} {\n  try {\n    const result = schema.safeParse(data);\n    if (result.success) {\n      return { success: true, data: result.data };\n    } else {\n      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`);\n      return { success: false, errors };\n    }\n  } catch (error) {\n    return { success: false, errors: ['验证过程中发生错误'] };\n  }\n}\n\n// 部分验证（用于更新操作）\nexport function validatePartialData<T>(schema: z.ZodSchema<T>, data: unknown): Partial<T> {\n  const partialSchema = schema.partial();\n  return validateData(partialSchema, data);\n}\n\n// 自定义验证规则\nexport const customValidators = {\n  // 验证营业时间\n  validateBusinessHours: (openTime?: string, closeTime?: string) => {\n    if (!openTime || !closeTime) return true;\n    \n    const open = new Date(`2000-01-01 ${openTime}`);\n    const close = new Date(`2000-01-01 ${closeTime}`);\n    \n    return open < close;\n  },\n\n  // 验证库存逻辑\n  validateStock: (stock: number, minStock: number) => {\n    return stock >= 0 && minStock >= 0 && minStock <= stock;\n  },\n\n  // 验证价格范围\n  validatePriceRange: (price: number, min: number = 0, max: number = 10000) => {\n    return price >= min && price <= max;\n  },\n\n  // 验证坐标\n  validateCoordinates: (latitude?: number, longitude?: number) => {\n    if (latitude === undefined || longitude === undefined) return true;\n    return latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180;\n  },\n};\n"], "names": [], "mappings": "AAAA,SAAS;;;;;;;;;;;;AAET;AACA;;;AAGO,MAAM,iBAAiB;IAC5B,IAAI,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACtB,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC3C,aAAa,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,gBAAgB,QAAQ;IACzD,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC3B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,iBAAiB,cAAc,QAAQ;IAC/D,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ;IAC9C,KAAK,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,aAAa,QAAQ;AAC3C;AAGO,MAAM,0BAA0B,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,IAAI,eAAe,EAAE,CAAC,QAAQ;IAC9B,MAAM,eAAe,IAAI;IACzB,aAAa,eAAe,WAAW;IACvC,OAAO,eAAe,KAAK;IAC3B,eAAe,eAAe,EAAE;IAChC,OAAO,eAAe,GAAG,CAAC,QAAQ;IAClC,aAAa,6KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6KAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IAC3C,YAAY,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACnD,WAAW,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACvC,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAC1C,UAAU,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,aAAa,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ;AACxD;AAGO,MAAM,8BAA8B,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClD,IAAI,eAAe,EAAE,CAAC,QAAQ;IAC9B,MAAM,eAAe,IAAI;IACzB,aAAa,eAAe,WAAW;IACvC,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAC9C,UAAU,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAGO,MAAM,wBAAwB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,IAAI,eAAe,EAAE,CAAC,QAAQ;IAC9B,MAAM,eAAe,IAAI;IACzB,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC9C,OAAO,eAAe,KAAK;IAC3B,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,GAAG,QAAQ;IACzD,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,KAAK,QAAQ,GAAG,QAAQ;IAC5D,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,oCAAoC,qBAAqB,QAAQ;IAC5F,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,oCAAoC,qBAAqB,QAAQ;IAC7F,QAAQ,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC5B,OAAO,eAAe,GAAG,CAAC,QAAQ;AACpC;AAGO,MAAM,qBAAqB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,GAAG,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,mBAAmB,QAAQ;IAClD,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,QAAQ,6KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAa;QAAe;QAAQ;KAAQ,EAAE,OAAO,CAAC;IACtE,WAAW,6KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;KAAO,EAAE,OAAO,CAAC;IAC3C,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACtC,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,OAAO,CAAC;AAClD;AAGO,SAAS,aAAgB,MAAsB,EAAE,IAAa;IACnE,IAAI;QACF,OAAO,OAAO,KAAK,CAAC;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,6KAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,MAAM,WAAW,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE;YAChF,MAAM,IAAI,oHAAA,CAAA,kBAAe,CAAC,SAAS,IAAI,CAAC;QAC1C;QACA,MAAM;IACR;AACF;AAGO,SAAS,iBAAoB,MAAsB,EAAE,IAAa;IAKvE,IAAI;QACF,MAAM,SAAS,OAAO,SAAS,CAAC;QAChC,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO;gBAAE,SAAS;gBAAM,MAAM,OAAO,IAAI;YAAC;QAC5C,OAAO;YACL,MAAM,SAAS,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE;YACrF,OAAO;gBAAE,SAAS;gBAAO;YAAO;QAClC;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,SAAS;YAAO,QAAQ;gBAAC;aAAY;QAAC;IACjD;AACF;AAGO,SAAS,oBAAuB,MAAsB,EAAE,IAAa;IAC1E,MAAM,gBAAgB,OAAO,OAAO;IACpC,OAAO,aAAa,eAAe;AACrC;AAGO,MAAM,mBAAmB;IAC9B,SAAS;IACT,uBAAuB,CAAC,UAAmB;QACzC,IAAI,CAAC,YAAY,CAAC,WAAW,OAAO;QAEpC,MAAM,OAAO,IAAI,KAAK,CAAC,WAAW,EAAE,UAAU;QAC9C,MAAM,QAAQ,IAAI,KAAK,CAAC,WAAW,EAAE,WAAW;QAEhD,OAAO,OAAO;IAChB;IAEA,SAAS;IACT,eAAe,CAAC,OAAe;QAC7B,OAAO,SAAS,KAAK,YAAY,KAAK,YAAY;IACpD;IAEA,SAAS;IACT,oBAAoB,CAAC,OAAe,MAAc,CAAC,EAAE,MAAc,KAAK;QACtE,OAAO,SAAS,OAAO,SAAS;IAClC;IAEA,OAAO;IACP,qBAAqB,CAAC,UAAmB;QACvC,IAAI,aAAa,aAAa,cAAc,WAAW,OAAO;QAC9D,OAAO,YAAY,CAAC,MAAM,YAAY,MAAM,aAAa,CAAC,OAAO,aAAa;IAChF;AACF", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/lib/database.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\nimport { z } from 'zod';\nimport { NotFoundError, ValidationError } from './errors';\nimport { validateData } from './validation';\nimport {\n  ProductValidationSchema,\n  ProductTypeValidationSchema,\n  StoreValidationSchema\n} from './validation';\n\n// 全局Prisma客户端实例\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient({\n  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],\n});\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Zod验证模式\nexport const ProductTypeSchema = z.object({\n  id: z.string().min(1),\n  name: z.string().min(1),\n  description: z.string().optional(),\n  displayOrder: z.number().int().default(0),\n  isActive: z.boolean().default(true),\n});\n\nexport const ProductSchema = z.object({\n  id: z.string().min(1),\n  name: z.string().min(1),\n  description: z.string().optional(),\n  price: z.number().positive(),\n  productTypeId: z.string().min(1),\n  image: z.string().optional(),\n  ingredients: z.array(z.string()).optional(),\n  spicyLevel: z.number().int().min(0).max(5).default(0),\n  available: z.boolean().default(true),\n  stock: z.number().int().min(0).default(0),\n  minStock: z.number().int().min(0).default(5),\n  isActive: z.boolean().default(true),\n});\n\nexport const StoreSchema = z.object({\n  id: z.string().min(1),\n  name: z.string().min(1),\n  address: z.string().min(1),\n  phone: z.string().optional(),\n  latitude: z.number().optional(),\n  longitude: z.number().optional(),\n  openTime: z.string().optional(),\n  closeTime: z.string().optional(),\n  isOpen: z.boolean().default(true),\n  image: z.string().optional(),\n});\n\n// 产品类型相关操作\nexport async function getProductTypes() {\n  return await prisma.productType.findMany({\n    where: { isActive: true },\n    orderBy: { displayOrder: 'asc' },\n  });\n}\n\nexport async function getProductTypeById(id: string) {\n  return await prisma.productType.findUnique({\n    where: { id },\n  });\n}\n\nexport async function createProductType(data: z.infer<typeof ProductTypeSchema>) {\n  const validatedData = ProductTypeSchema.parse(data);\n  return await prisma.productType.create({\n    data: validatedData,\n  });\n}\n\nexport async function updateProductType(id: string, data: Partial<z.infer<typeof ProductTypeSchema>>) {\n  return await prisma.productType.update({\n    where: { id },\n    data,\n  });\n}\n\nexport async function deleteProductType(id: string) {\n  // 检查是否有关联的产品\n  const productCount = await prisma.product.count({\n    where: { productTypeId: id },\n  });\n  \n  if (productCount > 0) {\n    throw new Error('无法删除：该产品类型下还有产品');\n  }\n  \n  return await prisma.productType.delete({\n    where: { id },\n  });\n}\n\n// 产品相关操作\nexport async function getProducts() {\n  return await prisma.product.findMany({\n    include: {\n      productType: true,\n    },\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\nexport async function getProductById(id: string) {\n  return await prisma.product.findUnique({\n    where: { id },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function getProductsByCategory(productTypeId: string) {\n  return await prisma.product.findMany({\n    where: { productTypeId },\n    include: {\n      productType: true,\n    },\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\nexport async function createProduct(data: Omit<z.infer<typeof ProductValidationSchema>, 'id'>) {\n  const validatedData = validateData(ProductValidationSchema.omit({ id: true }), data);\n\n  // 检查产品类型是否存在\n  const productType = await prisma.productType.findUnique({\n    where: { id: validatedData.productTypeId },\n  });\n\n  if (!productType) {\n    throw new NotFoundError('产品类型');\n  }\n\n  // 生成新ID\n  const newId = Date.now().toString();\n\n  return await prisma.product.create({\n    data: {\n      ...validatedData,\n      id: newId,\n      ingredients: validatedData.ingredients ? JSON.stringify(validatedData.ingredients) : null,\n      publishedAt: validatedData.isActive ? new Date() : null,\n    },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function updateProduct(id: string, data: Partial<Omit<z.infer<typeof ProductSchema>, 'id'>>) {\n  const updateData: any = { ...data };\n  \n  if (data.ingredients) {\n    updateData.ingredients = JSON.stringify(data.ingredients);\n  }\n  \n  return await prisma.product.update({\n    where: { id },\n    data: updateData,\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function deleteProduct(id: string) {\n  return await prisma.product.delete({\n    where: { id },\n  });\n}\n\n// 门店相关操作\nexport async function getStores() {\n  return await prisma.store.findMany({\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\nexport async function getStoreById(id: string) {\n  return await prisma.store.findUnique({\n    where: { id },\n  });\n}\n\nexport async function createStore(data: Omit<z.infer<typeof StoreSchema>, 'id'>) {\n  const validatedData = StoreSchema.omit({ id: true }).parse(data);\n  \n  // 生成新ID\n  const newId = Date.now().toString();\n  \n  return await prisma.store.create({\n    data: {\n      ...validatedData,\n      id: newId,\n    },\n  });\n}\n\nexport async function updateStore(id: string, data: Partial<Omit<z.infer<typeof StoreSchema>, 'id'>>) {\n  return await prisma.store.update({\n    where: { id },\n    data,\n  });\n}\n\nexport async function deleteStore(id: string) {\n  return await prisma.store.delete({\n    where: { id },\n  });\n}\n\n// 库存管理相关操作\nexport async function updateProductStock(id: string, quantity: number) {\n  const product = await prisma.product.findUnique({ where: { id } });\n  if (!product) throw new Error('产品不存在');\n\n  const newStock = Math.max(0, product.stock + quantity);\n\n  return await prisma.product.update({\n    where: { id },\n    data: {\n      stock: newStock,\n      available: newStock > 0, // 自动更新可用状态\n    },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function toggleProductStatus(id: string) {\n  const product = await prisma.product.findUnique({ where: { id } });\n  if (!product) throw new Error('产品不存在');\n\n  return await prisma.product.update({\n    where: { id },\n    data: { isActive: !product.isActive },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function getLowStockProducts() {\n  return await prisma.product.findMany({\n    where: {\n      OR: [\n        { stock: { lte: prisma.product.fields.minStock } },\n        { stock: 0 },\n      ],\n    },\n    include: {\n      productType: true,\n    },\n    orderBy: { stock: 'asc' },\n  });\n}\n\nexport async function getActiveProducts() {\n  return await prisma.product.findMany({\n    where: {\n      isActive: true,\n      available: true,\n    },\n    include: {\n      productType: true,\n    },\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\n// 工具函数：解析ingredients JSON字符串\nexport function parseIngredients(ingredients: string | null): string[] {\n  if (!ingredients) return [];\n  try {\n    return JSON.parse(ingredients);\n  } catch {\n    return [];\n  }\n}\n\n// 工具函数：获取库存状态\nexport function getStockStatus(stock: number, minStock: number) {\n  if (stock === 0) return { status: 'out_of_stock', label: '缺货', color: 'red' };\n  if (stock <= minStock) return { status: 'low_stock', label: '库存不足', color: 'yellow' };\n  return { status: 'in_stock', label: '库存充足', color: 'green' };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAOA,gBAAgB;AAChB,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY,CAAC;IAC/D,KAAK,uCAAyC;QAAC;QAAS;QAAS;KAAO,GAAG;AAC7E;AAEA,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,MAAM,oBAAoB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,IAAI,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;IACvC,UAAU,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAEO,MAAM,gBAAgB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,eAAe,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC9B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,aAAa,6KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6KAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IACzC,YAAY,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACnD,WAAW,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACvC,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAC1C,UAAU,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAEO,MAAM,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,IAAI,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACxB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,QAAQ,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC5B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AAGO,eAAe;IACpB,OAAO,MAAM,OAAO,WAAW,CAAC,QAAQ,CAAC;QACvC,OAAO;YAAE,UAAU;QAAK;QACxB,SAAS;YAAE,cAAc;QAAM;IACjC;AACF;AAEO,eAAe,mBAAmB,EAAU;IACjD,OAAO,MAAM,OAAO,WAAW,CAAC,UAAU,CAAC;QACzC,OAAO;YAAE;QAAG;IACd;AACF;AAEO,eAAe,kBAAkB,IAAuC;IAC7E,MAAM,gBAAgB,kBAAkB,KAAK,CAAC;IAC9C,OAAO,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;QACrC,MAAM;IACR;AACF;AAEO,eAAe,kBAAkB,EAAU,EAAE,IAAgD;IAClG,OAAO,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;QACrC,OAAO;YAAE;QAAG;QACZ;IACF;AACF;AAEO,eAAe,kBAAkB,EAAU;IAChD,aAAa;IACb,MAAM,eAAe,MAAM,OAAO,OAAO,CAAC,KAAK,CAAC;QAC9C,OAAO;YAAE,eAAe;QAAG;IAC7B;IAEA,IAAI,eAAe,GAAG;QACpB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;QACrC,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe;IACpB,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEO,eAAe,eAAe,EAAU;IAC7C,OAAO,MAAM,OAAO,OAAO,CAAC,UAAU,CAAC;QACrC,OAAO;YAAE;QAAG;QACZ,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,sBAAsB,aAAqB;IAC/D,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,OAAO;YAAE;QAAc;QACvB,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEO,eAAe,cAAc,IAAyD;IAC3F,MAAM,gBAAgB,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,wHAAA,CAAA,0BAAuB,CAAC,IAAI,CAAC;QAAE,IAAI;IAAK,IAAI;IAE/E,aAAa;IACb,MAAM,cAAc,MAAM,OAAO,WAAW,CAAC,UAAU,CAAC;QACtD,OAAO;YAAE,IAAI,cAAc,aAAa;QAAC;IAC3C;IAEA,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,oHAAA,CAAA,gBAAa,CAAC;IAC1B;IAEA,QAAQ;IACR,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;IAEjC,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,MAAM;YACJ,GAAG,aAAa;YAChB,IAAI;YACJ,aAAa,cAAc,WAAW,GAAG,KAAK,SAAS,CAAC,cAAc,WAAW,IAAI;YACrF,aAAa,cAAc,QAAQ,GAAG,IAAI,SAAS;QACrD;QACA,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,cAAc,EAAU,EAAE,IAAwD;IACtG,MAAM,aAAkB;QAAE,GAAG,IAAI;IAAC;IAElC,IAAI,KAAK,WAAW,EAAE;QACpB,WAAW,WAAW,GAAG,KAAK,SAAS,CAAC,KAAK,WAAW;IAC1D;IAEA,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;QACZ,MAAM;QACN,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,cAAc,EAAU;IAC5C,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe;IACpB,OAAO,MAAM,OAAO,KAAK,CAAC,QAAQ,CAAC;QACjC,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEO,eAAe,aAAa,EAAU;IAC3C,OAAO,MAAM,OAAO,KAAK,CAAC,UAAU,CAAC;QACnC,OAAO;YAAE;QAAG;IACd;AACF;AAEO,eAAe,YAAY,IAA6C;IAC7E,MAAM,gBAAgB,YAAY,IAAI,CAAC;QAAE,IAAI;IAAK,GAAG,KAAK,CAAC;IAE3D,QAAQ;IACR,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;IAEjC,OAAO,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;QAC/B,MAAM;YACJ,GAAG,aAAa;YAChB,IAAI;QACN;IACF;AACF;AAEO,eAAe,YAAY,EAAU,EAAE,IAAsD;IAClG,OAAO,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;QAC/B,OAAO;YAAE;QAAG;QACZ;IACF;AACF;AAEO,eAAe,YAAY,EAAU;IAC1C,OAAO,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;QAC/B,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe,mBAAmB,EAAU,EAAE,QAAgB;IACnE,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC,UAAU,CAAC;QAAE,OAAO;YAAE;QAAG;IAAE;IAChE,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,QAAQ,KAAK,GAAG;IAE7C,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;QACZ,MAAM;YACJ,OAAO;YACP,WAAW,WAAW;QACxB;QACA,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,oBAAoB,EAAU;IAClD,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC,UAAU,CAAC;QAAE,OAAO;YAAE;QAAG;IAAE;IAChE,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;QACZ,MAAM;YAAE,UAAU,CAAC,QAAQ,QAAQ;QAAC;QACpC,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe;IACpB,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,OAAO;YACL,IAAI;gBACF;oBAAE,OAAO;wBAAE,KAAK,OAAO,OAAO,CAAC,MAAM,CAAC,QAAQ;oBAAC;gBAAE;gBACjD;oBAAE,OAAO;gBAAE;aACZ;QACH;QACA,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,OAAO;QAAM;IAC1B;AACF;AAEO,eAAe;IACpB,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,OAAO;YACL,UAAU;YACV,WAAW;QACb;QACA,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAGO,SAAS,iBAAiB,WAA0B;IACzD,IAAI,CAAC,aAAa,OAAO,EAAE;IAC3B,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO,EAAE;IACX;AACF;AAGO,SAAS,eAAe,KAAa,EAAE,QAAgB;IAC5D,IAAI,UAAU,GAAG,OAAO;QAAE,QAAQ;QAAgB,OAAO;QAAM,OAAO;IAAM;IAC5E,IAAI,SAAS,UAAU,OAAO;QAAE,QAAQ;QAAa,OAAO;QAAQ,OAAO;IAAS;IACpF,OAAO;QAAE,QAAQ;QAAY,OAAO;QAAQ,OAAO;IAAQ;AAC7D", "debugId": null}}, {"offset": {"line": 616, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/WeatherWidget.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/WeatherWidget.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/WeatherWidget.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 628, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/WeatherWidget.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/WeatherWidget.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/WeatherWidget.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 640, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/products/%5Bid%5D/page.tsx"], "sourcesContent": ["import { getProductById, getProducts, parseIngredients } from '@/lib/database';\nimport WeatherWidget from '@/components/WeatherWidget';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { notFound } from 'next/navigation';\n\ninterface ProductDetailPageProps {\n  params: Promise<{ id: string }>;\n}\n\nexport default async function ProductDetailPage({ params }: ProductDetailPageProps) {\n  const { id } = await params;\n  const rawProduct = await getProductById(id);\n\n  if (!rawProduct) {\n    notFound();\n  }\n\n  // 转换产品数据格式以保持兼容性\n  const product = {\n    ...rawProduct,\n    category: rawProduct.productTypeId,\n    description: rawProduct.description || '',\n    ingredients: parseIngredients(rawProduct.ingredients),\n    image: rawProduct.image || '',\n    stock: rawProduct.stock || 0,\n    minStock: rawProduct.minStock || 5,\n    isActive: rawProduct.isActive !== false,\n    publishedAt: rawProduct.publishedAt?.toISOString() || null,\n    createdAt: rawProduct.createdAt.toISOString(),\n    updatedAt: rawProduct.updatedAt.toISOString(),\n  };\n\n  const spicyIcons = '🌶️'.repeat(product.spicyLevel);\n\n  // 使用产品类型名称而不是硬编码的类别\n  const categoryText = rawProduct.productType?.name || '未分类';\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/\" className=\"text-2xl font-bold text-orange-600 hover:text-orange-700\">\n                香香面条店\n              </Link>\n              <span className=\"text-gray-600\">|</span>\n              <Link href=\"/products\" className=\"text-gray-600 hover:text-orange-600\">\n                菜品列表\n              </Link>\n            </div>\n            <WeatherWidget />\n          </div>\n        </div>\n      </header>\n\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2\">\n            <div className=\"relative h-96 lg:h-auto\">\n              <Image\n                src={product.image}\n                alt={product.name}\n                fill\n                className=\"object-cover\"\n              />\n            </div>\n            \n            <div className=\"p-8\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h1 className=\"text-3xl font-bold text-gray-800\">{product.name}</h1>\n                <span className=\"bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm\">\n                  {categoryText}\n                </span>\n              </div>\n              \n              <p className=\"text-gray-600 text-lg mb-6\">{product.description}</p>\n              \n              <div className=\"grid grid-cols-2 gap-6 mb-6\">\n                <div>\n                  <h3 className=\"text-lg font-semibold mb-2\">价格</h3>\n                  <span className=\"text-3xl font-bold text-orange-600\">¥{product.price}</span>\n                </div>\n                \n                <div>\n                  <h3 className=\"text-lg font-semibold mb-2\">辣度</h3>\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-2xl\">{spicyIcons || '不辣'}</span>\n                    <span className=\"text-gray-600\">({product.spicyLevel}/5)</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"mb-6\">\n                <h3 className=\"text-lg font-semibold mb-3\">主要配料</h3>\n                <div className=\"flex flex-wrap gap-2\">\n                  {product.ingredients.map((ingredient, index) => (\n                    <span\n                      key={index}\n                      className=\"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\"\n                    >\n                      {ingredient}\n                    </span>\n                  ))}\n                </div>\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6\">\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <h4 className=\"font-semibold text-gray-800 mb-2\">库存状态</h4>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-600\">当前库存:</span>\n                      <span className={`font-bold ${\n                        product.stock === 0 ? 'text-red-600' :\n                        product.stock <= product.minStock ? 'text-yellow-600' : 'text-green-600'\n                      }`}>\n                        {product.stock} 件\n                      </span>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-600\">最低库存:</span>\n                      <span className=\"text-gray-700\">{product.minStock} 件</span>\n                    </div>\n                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${\n                      product.available\n                        ? 'bg-green-100 text-green-800'\n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      {product.available ? '✓ 有货' : '✗ 暂缺'}\n                    </span>\n                  </div>\n                </div>\n\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <h4 className=\"font-semibold text-gray-800 mb-2\">产品类型</h4>\n                  <span className=\"text-orange-600 font-medium\">{categoryText}</span>\n                </div>\n\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <h4 className=\"font-semibold text-gray-800 mb-2\">销售状态</h4>\n                  <div className=\"space-y-2\">\n                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${\n                      product.isActive\n                        ? 'bg-blue-100 text-blue-800'\n                        : 'bg-gray-100 text-gray-800'\n                    }`}>\n                      {product.isActive ? '✓ 已上架' : '✗ 已下架'}\n                    </span>\n                    {!product.isActive && (\n                      <p className=\"text-sm text-gray-500\">该产品暂时下架，不可购买</p>\n                    )}\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-blue-50 p-4 rounded-lg mb-6\">\n                <h4 className=\"font-semibold text-blue-800 mb-2\">💡 制作建议</h4>\n                <p className=\"text-blue-700 text-sm\">\n                  {product.spicyLevel > 0\n                    ? `这是一道${product.spicyLevel}级辣度的美食，建议搭配清淡汤品食用。`\n                    : '口味清淡，适合全家老少享用。'}\n                  新鲜制作，建议尽快食用以保持最佳口感。\n                </p>\n              </div>\n\n              <div className=\"border-t pt-6 mb-6\">\n                <h4 className=\"font-semibold text-gray-800 mb-3\">产品信息</h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  {product.publishedAt && (\n                    <div>\n                      <span className=\"text-gray-500\">发布时间：</span>\n                      <span className=\"text-gray-700 font-medium\">{new Date(product.publishedAt).toLocaleDateString('zh-CN')}</span>\n                    </div>\n                  )}\n                  <div>\n                    <span className=\"text-gray-500\">创建时间：</span>\n                    <span className=\"text-gray-700\">{new Date(product.createdAt).toLocaleDateString('zh-CN')}</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-500\">更新时间：</span>\n                    <span className=\"text-gray-700\">{new Date(product.updatedAt).toLocaleDateString('zh-CN')}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"flex space-x-4\">\n                <button\n                  className={`flex-1 py-3 px-6 rounded-lg font-semibold transition-colors ${\n                    product.available\n                      ? 'bg-orange-500 text-white hover:bg-orange-600'\n                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                  }`}\n                  disabled={!product.available}\n                >\n                  {product.available ? '立即订购' : '暂时缺货'}\n                </button>\n                \n                <Link\n                  href=\"/products\"\n                  className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:border-orange-500 hover:text-orange-600 transition-colors\"\n                >\n                  返回列表\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n\nexport async function generateStaticParams() {\n  const products = await getProducts();\n  return products.map((product) => ({\n    id: product.id,\n  }));\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;;AAMe,eAAe,kBAAkB,EAAE,MAAM,EAA0B;IAChF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IACrB,MAAM,aAAa,MAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;IAExC,IAAI,CAAC,YAAY;QACf,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,iBAAiB;IACjB,MAAM,UAAU;QACd,GAAG,UAAU;QACb,UAAU,WAAW,aAAa;QAClC,aAAa,WAAW,WAAW,IAAI;QACvC,aAAa,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,WAAW;QACpD,OAAO,WAAW,KAAK,IAAI;QAC3B,OAAO,WAAW,KAAK,IAAI;QAC3B,UAAU,WAAW,QAAQ,IAAI;QACjC,UAAU,WAAW,QAAQ,KAAK;QAClC,aAAa,WAAW,WAAW,EAAE,iBAAiB;QACtD,WAAW,WAAW,SAAS,CAAC,WAAW;QAC3C,WAAW,WAAW,SAAS,CAAC,WAAW;IAC7C;IAEA,MAAM,aAAa,MAAM,MAAM,CAAC,QAAQ,UAAU;IAElD,oBAAoB;IACpB,MAAM,eAAe,WAAW,WAAW,EAAE,QAAQ;IAErD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAA2D;;;;;;kDAGpF,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAsC;;;;;;;;;;;;0CAIzE,8OAAC,mIAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;0BAKpB,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,QAAQ,KAAK;oCAClB,KAAK,QAAQ,IAAI;oCACjB,IAAI;oCACJ,WAAU;;;;;;;;;;;0CAId,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC,QAAQ,IAAI;;;;;;0DAC9D,8OAAC;gDAAK,WAAU;0DACb;;;;;;;;;;;;kDAIL,8OAAC;wCAAE,WAAU;kDAA8B,QAAQ,WAAW;;;;;;kDAE9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,8OAAC;wDAAK,WAAU;;4DAAqC;4DAAE,QAAQ,KAAK;;;;;;;;;;;;;0DAGtE,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAY,cAAc;;;;;;0EAC1C,8OAAC;gEAAK,WAAU;;oEAAgB;oEAAE,QAAQ,UAAU;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;kDAK3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBACpC,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;kDASb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAW,CAAC,UAAU,EAC1B,QAAQ,KAAK,KAAK,IAAI,iBACtB,QAAQ,KAAK,IAAI,QAAQ,QAAQ,GAAG,oBAAoB,kBACxD;;4EACC,QAAQ,KAAK;4EAAC;;;;;;;;;;;;;0EAGnB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;;4EAAiB,QAAQ,QAAQ;4EAAC;;;;;;;;;;;;;0EAEpD,8OAAC;gEAAK,WAAW,CAAC,oEAAoE,EACpF,QAAQ,SAAS,GACb,gCACA,2BACJ;0EACC,QAAQ,SAAS,GAAG,SAAS;;;;;;;;;;;;;;;;;;0DAKpC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;;0DAGjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,oEAAoE,EACpF,QAAQ,QAAQ,GACZ,8BACA,6BACJ;0EACC,QAAQ,QAAQ,GAAG,UAAU;;;;;;4DAE/B,CAAC,QAAQ,QAAQ,kBAChB,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;kDAM7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDACV,QAAQ,UAAU,GAAG,IAClB,CAAC,IAAI,EAAE,QAAQ,UAAU,CAAC,kBAAkB,CAAC,GAC7C;oDAAiB;;;;;;;;;;;;;kDAKzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAI,WAAU;;oDACZ,QAAQ,WAAW,kBAClB,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAA6B,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;kEAGlG,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAiB,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;kEAElF,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAiB,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kDAKtF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAW,CAAC,4DAA4D,EACtE,QAAQ,SAAS,GACb,iDACA,gDACJ;gDACF,UAAU,CAAC,QAAQ,SAAS;0DAE3B,QAAQ,SAAS,GAAG,SAAS;;;;;;0DAGhC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;AAEO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IACjC,OAAO,SAAS,GAAG,CAAC,CAAC,UAAY,CAAC;YAChC,IAAI,QAAQ,EAAE;QAChB,CAAC;AACH", "debugId": null}}]}