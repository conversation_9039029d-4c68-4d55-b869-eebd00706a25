import { Product, Store } from '@/types';
import productsData from '@/data/products.json';
import storesData from '@/data/stores.json';
import fs from 'fs/promises';
import path from 'path';

const PRODUCTS_FILE = path.join(process.cwd(), 'src/data/products.json');
const STORES_FILE = path.join(process.cwd(), 'src/data/stores.json');

export async function getProducts(): Promise<Product[]> {
  return productsData as Product[];
}

export async function getProductById(id: string): Promise<Product | null> {
  const products = await getProducts();
  return products.find(product => product.id === id) || null;
}

export async function getProductsByCategory(category: string): Promise<Product[]> {
  const products = await getProducts();
  return products.filter(product => product.category === category);
}

export async function getStores(): Promise<Store[]> {
  return storesData as Store[];
}

export async function getStoreById(id: string): Promise<Store | null> {
  const stores = await getStores();
  return stores.find(store => store.id === id) || null;
}

export async function saveProducts(products: Product[]): Promise<void> {
  await fs.writeFile(PRODUCTS_FILE, JSON.stringify(products, null, 2));
}

export async function saveStores(stores: Store[]): Promise<void> {
  await fs.writeFile(STORES_FILE, JSON.stringify(stores, null, 2));
}

export async function addProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> {
  const products = await getProducts();
  const newProduct: Product = {
    ...product,
    id: Date.now().toString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  products.push(newProduct);
  await saveProducts(products);
  return newProduct;
}

export async function updateProduct(id: string, updates: Partial<Product>): Promise<Product | null> {
  const products = await getProducts();
  const index = products.findIndex(product => product.id === id);
  if (index === -1) return null;
  
  products[index] = {
    ...products[index],
    ...updates,
    updatedAt: new Date().toISOString(),
  };
  await saveProducts(products);
  return products[index];
}

export async function deleteProduct(id: string): Promise<boolean> {
  const products = await getProducts();
  const index = products.findIndex(product => product.id === id);
  if (index === -1) return false;
  
  products.splice(index, 1);
  await saveProducts(products);
  return true;
}

export async function addStore(store: Omit<Store, 'id' | 'createdAt' | 'updatedAt'>): Promise<Store> {
  const stores = await getStores();
  const newStore: Store = {
    ...store,
    id: Date.now().toString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  stores.push(newStore);
  await saveStores(stores);
  return newStore;
}

export async function updateStore(id: string, updates: Partial<Store>): Promise<Store | null> {
  const stores = await getStores();
  const index = stores.findIndex(store => store.id === id);
  if (index === -1) return null;
  
  stores[index] = {
    ...stores[index],
    ...updates,
    updatedAt: new Date().toISOString(),
  };
  await saveStores(stores);
  return stores[index];
}

export async function deleteStore(id: string): Promise<boolean> {
  const stores = await getStores();
  const index = stores.findIndex(store => store.id === id);
  if (index === -1) return false;
  
  stores.splice(index, 1);
  await saveStores(stores);
  return true;
}