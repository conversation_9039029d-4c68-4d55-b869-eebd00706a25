'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function AdminLogin() {
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const router = useRouter();

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 简单的密码验证
    if (password === 'noodle2024') {
      localStorage.setItem('adminAuth', 'true');
      router.push('/admin');
    } else {
      setError('密码错误，请重试');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-orange-600">🍜</h1>
          <h2 className="text-2xl font-bold text-gray-800 mt-2">管理后台登录</h2>
          <p className="text-gray-600 mt-2">香香面条店管理系统</p>
        </div>

        <form onSubmit={handleLogin} className="space-y-6">
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              管理员密码
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="请输入管理员密码"
              required
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm text-center bg-red-50 p-2 rounded">
              {error}
            </div>
          )}

          <button
            type="submit"
            className="w-full bg-orange-500 text-white py-2 px-4 rounded-md hover:bg-orange-600 transition-colors font-medium"
          >
            登录管理后台
          </button>
        </form>

        <div className="mt-6 text-center text-sm text-gray-500">
          <p>默认密码：noodle2024</p>
          <p className="mt-1">
            <Link href="/" className="text-orange-600 hover:text-orange-700">
              返回首页
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}