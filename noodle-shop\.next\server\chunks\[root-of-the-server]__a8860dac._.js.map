{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/lib/data.ts"], "sourcesContent": ["import { Product, Store } from '@/types';\nimport productsData from '@/data/products.json';\nimport storesData from '@/data/stores.json';\nimport fs from 'fs/promises';\nimport path from 'path';\n\nconst PRODUCTS_FILE = path.join(process.cwd(), 'src/data/products.json');\nconst STORES_FILE = path.join(process.cwd(), 'src/data/stores.json');\n\nexport async function getProducts(): Promise<Product[]> {\n  return productsData as Product[];\n}\n\nexport async function getProductById(id: string): Promise<Product | null> {\n  const products = await getProducts();\n  return products.find(product => product.id === id) || null;\n}\n\nexport async function getProductsByCategory(category: string): Promise<Product[]> {\n  const products = await getProducts();\n  return products.filter(product => product.category === category);\n}\n\nexport async function getStores(): Promise<Store[]> {\n  return storesData as Store[];\n}\n\nexport async function getStoreById(id: string): Promise<Store | null> {\n  const stores = await getStores();\n  return stores.find(store => store.id === id) || null;\n}\n\nexport async function saveProducts(products: Product[]): Promise<void> {\n  await fs.writeFile(PRODUCTS_FILE, JSON.stringify(products, null, 2));\n}\n\nexport async function saveStores(stores: Store[]): Promise<void> {\n  await fs.writeFile(STORES_FILE, JSON.stringify(stores, null, 2));\n}\n\nexport async function addProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> {\n  const products = await getProducts();\n  const newProduct: Product = {\n    ...product,\n    id: Date.now().toString(),\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  };\n  products.push(newProduct);\n  await saveProducts(products);\n  return newProduct;\n}\n\nexport async function updateProduct(id: string, updates: Partial<Product>): Promise<Product | null> {\n  const products = await getProducts();\n  const index = products.findIndex(product => product.id === id);\n  if (index === -1) return null;\n  \n  products[index] = {\n    ...products[index],\n    ...updates,\n    updatedAt: new Date().toISOString(),\n  };\n  await saveProducts(products);\n  return products[index];\n}\n\nexport async function deleteProduct(id: string): Promise<boolean> {\n  const products = await getProducts();\n  const index = products.findIndex(product => product.id === id);\n  if (index === -1) return false;\n  \n  products.splice(index, 1);\n  await saveProducts(products);\n  return true;\n}\n\nexport async function addStore(store: Omit<Store, 'id' | 'createdAt' | 'updatedAt'>): Promise<Store> {\n  const stores = await getStores();\n  const newStore: Store = {\n    ...store,\n    id: Date.now().toString(),\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  };\n  stores.push(newStore);\n  await saveStores(stores);\n  return newStore;\n}\n\nexport async function updateStore(id: string, updates: Partial<Store>): Promise<Store | null> {\n  const stores = await getStores();\n  const index = stores.findIndex(store => store.id === id);\n  if (index === -1) return null;\n  \n  stores[index] = {\n    ...stores[index],\n    ...updates,\n    updatedAt: new Date().toISOString(),\n  };\n  await saveStores(stores);\n  return stores[index];\n}\n\nexport async function deleteStore(id: string): Promise<boolean> {\n  const stores = await getStores();\n  const index = stores.findIndex(store => store.id === id);\n  if (index === -1) return false;\n  \n  stores.splice(index, 1);\n  await saveStores(stores);\n  return true;\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC/C,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAEtC,eAAe;IACpB,OAAO,+FAAA,CAAA,UAAY;AACrB;AAEO,eAAe,eAAe,EAAU;IAC7C,MAAM,WAAW,MAAM;IACvB,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK,OAAO;AACxD;AAEO,eAAe,sBAAsB,QAAgB;IAC1D,MAAM,WAAW,MAAM;IACvB,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACzD;AAEO,eAAe;IACpB,OAAO,6FAAA,CAAA,UAAU;AACnB;AAEO,eAAe,aAAa,EAAU;IAC3C,MAAM,SAAS,MAAM;IACrB,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,OAAO;AAClD;AAEO,eAAe,aAAa,QAAmB;IACpD,MAAM,qHAAA,CAAA,UAAE,CAAC,SAAS,CAAC,eAAe,KAAK,SAAS,CAAC,UAAU,MAAM;AACnE;AAEO,eAAe,WAAW,MAAe;IAC9C,MAAM,qHAAA,CAAA,UAAE,CAAC,SAAS,CAAC,aAAa,KAAK,SAAS,CAAC,QAAQ,MAAM;AAC/D;AAEO,eAAe,WAAW,OAAwD;IACvF,MAAM,WAAW,MAAM;IACvB,MAAM,aAAsB;QAC1B,GAAG,OAAO;QACV,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,SAAS,IAAI,CAAC;IACd,MAAM,aAAa;IACnB,OAAO;AACT;AAEO,eAAe,cAAc,EAAU,EAAE,OAAyB;IACvE,MAAM,WAAW,MAAM;IACvB,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,QAAQ,CAAC,MAAM,GAAG;QAChB,GAAG,QAAQ,CAAC,MAAM;QAClB,GAAG,OAAO;QACV,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,MAAM,aAAa;IACnB,OAAO,QAAQ,CAAC,MAAM;AACxB;AAEO,eAAe,cAAc,EAAU;IAC5C,MAAM,WAAW,MAAM;IACvB,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,SAAS,MAAM,CAAC,OAAO;IACvB,MAAM,aAAa;IACnB,OAAO;AACT;AAEO,eAAe,SAAS,KAAoD;IACjF,MAAM,SAAS,MAAM;IACrB,MAAM,WAAkB;QACtB,GAAG,KAAK;QACR,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,OAAO,IAAI,CAAC;IACZ,MAAM,WAAW;IACjB,OAAO;AACT;AAEO,eAAe,YAAY,EAAU,EAAE,OAAuB;IACnE,MAAM,SAAS,MAAM;IACrB,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACrD,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,MAAM,CAAC,MAAM,GAAG;QACd,GAAG,MAAM,CAAC,MAAM;QAChB,GAAG,OAAO;QACV,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,MAAM,WAAW;IACjB,OAAO,MAAM,CAAC,MAAM;AACtB;AAEO,eAAe,YAAY,EAAU;IAC1C,MAAM,SAAS,MAAM;IACrB,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACrD,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,OAAO,MAAM,CAAC,OAAO;IACrB,MAAM,WAAW;IACjB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/api/stores/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\nimport { getStores, addStore } from '@/lib/data';\n\nexport async function GET() {\n  try {\n    const stores = await getStores();\n    return Response.json(stores);\n  } catch (error) {\n    console.error('Failed to fetch stores:', error);\n    return Response.json({ error: 'Failed to fetch stores' }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const store = await addStore(body);\n    return Response.json(store, { status: 201 });\n  } catch (error) {\n    console.error('Failed to create store:', error);\n    return Response.json({ error: 'Failed to create store' }, { status: 500 });\n  }\n}"], "names": [], "mappings": ";;;;AACA;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD;QAC7B,OAAO,SAAS,IAAI,CAAC;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,SAAS,IAAI,CAAC;YAAE,OAAO;QAAyB,GAAG;YAAE,QAAQ;QAAI;IAC1E;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,QAAQ,MAAM,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE;QAC7B,OAAO,SAAS,IAAI,CAAC,OAAO;YAAE,QAAQ;QAAI;IAC5C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,SAAS,IAAI,CAAC;YAAE,OAAO;QAAyB,GAAG;YAAE,QAAQ;QAAI;IAC1E;AACF", "debugId": null}}]}