import { Product } from '@/types';
import Image from 'next/image';
import Link from 'next/link';

interface ProductCardProps {
  product: Product;
}

export default function ProductCard({ product }: ProductCardProps) {
  const spicyIcons = '🌶️'.repeat(product.spicyLevel);
  const categoryText = {
    noodles: '面条',
    pasta: '面片',
    soup: '汤面'
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      <div className="relative h-48">
        <Image
          src={product.image}
          alt={product.name}
          fill
          className="object-cover"
        />
        <div className="absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded-full text-xs">
          {categoryText[product.category]}
        </div>
      </div>
      <div className="p-4">
        <h3 className="text-xl font-semibold text-gray-800 mb-2">{product.name}</h3>
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{product.description}</p>
        
        <div className="flex items-center justify-between mb-3">
          <span className="text-2xl font-bold text-orange-600">¥{product.price}</span>
          <div className="flex items-center space-x-1">
            <span className="text-gray-500 text-sm">辣度:</span>
            <span className="text-sm">{spicyIcons || '不辣'}</span>
          </div>
        </div>

        <div className="mb-3">
          <div className="flex flex-wrap gap-1">
            {product.ingredients.slice(0, 3).map((ingredient, index) => (
              <span
                key={index}
                className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs"
              >
                {ingredient}
              </span>
            ))}
            {product.ingredients.length > 3 && (
              <span className="text-gray-500 text-xs">...</span>
            )}
          </div>
        </div>

        <div className="flex justify-between items-center">
          <Link
            href={`/products/${product.id}`}
            className="text-orange-500 hover:text-orange-600 text-sm font-medium"
          >
            查看详情 →
          </Link>
          <span className={`text-sm ${product.available ? 'text-green-600' : 'text-red-600'}`}>
            {product.available ? '有货' : '暂缺'}
          </span>
        </div>
      </div>
    </div>
  );
}