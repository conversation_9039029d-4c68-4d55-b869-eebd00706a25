import { Product } from '@/types';
import Image from 'next/image';
import Link from 'next/link';

interface ProductCardProps {
  product: Product;
}

export default function ProductCard({ product }: ProductCardProps) {
  const spicyIcons = '🌶️'.repeat(product.spicyLevel);

  // 获取类别显示文本，优先使用productType名称
  const getCategoryText = () => {
    if ('productType' in product && product.productType && typeof product.productType === 'object' && 'name' in product.productType) {
      return (product.productType as any).name;
    }

    // 回退到硬编码的类别映射
    const categoryText: Record<string, string> = {
      'fresh-noodles': '手工鲜面条',
      'dumpling-wrappers': '饺子皮',
      'noodle-sheets': '面皮',
      'shaobing': '烧饼',
      'wonton-wrappers': '馄饨皮',
      'steamed-buns': '馒头花卷',
      noodles: '面条',
      pasta: '面片',
      soup: '汤面'
    };

    return categoryText[product.category] || product.category || '未分类';
  };

  // 获取库存状态
  const getStockStatus = () => {
    const stock = (product as any).stock || 0;
    const minStock = (product as any).minStock || 5;

    if (stock === 0) return { status: 'out_of_stock', label: '缺货', color: 'bg-red-100 text-red-800' };
    if (stock <= minStock) return { status: 'low_stock', label: '库存不足', color: 'bg-yellow-100 text-yellow-800' };
    return { status: 'in_stock', label: '库存充足', color: 'bg-green-100 text-green-800' };
  };

  const stockStatus = getStockStatus();
  const isActive = (product as any).isActive !== false;

  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow ${!isActive ? 'opacity-60' : ''}`}>
      <div className="relative h-48">
        <Image
          src={product.image}
          alt={product.name}
          fill
          className="object-cover"
        />
        <div className="absolute top-2 left-2 flex flex-col gap-1">
          {spicyIcons && (
            <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">
              {spicyIcons}
            </span>
          )}
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${stockStatus.color}`}>
            {stockStatus.label}
          </span>
        </div>
        <div className="absolute top-2 right-2 flex flex-col gap-1">
          <span className="bg-orange-500 text-white px-2 py-1 rounded-full text-xs">
            {getCategoryText()}
          </span>
          {!isActive && (
            <span className="bg-gray-500 text-white px-2 py-1 rounded-full text-xs">
              已下架
            </span>
          )}
        </div>
      </div>
      <div className="p-4">
        <h3 className="text-xl font-semibold text-gray-800 mb-2">{product.name}</h3>
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{product.description}</p>
        
        <div className="flex items-center justify-between mb-3">
          <span className="text-2xl font-bold text-orange-600">¥{product.price}</span>
          <div className="flex items-center space-x-1">
            <span className="text-gray-500 text-sm">辣度:</span>
            <span className="text-sm">{spicyIcons || '不辣'}</span>
          </div>
        </div>

        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <span className="text-gray-500 text-sm">库存:</span>
            <span className={`text-sm font-medium ${
              (product as any).stock === 0 ? 'text-red-600' :
              (product as any).stock <= (product as any).minStock ? 'text-yellow-600' : 'text-green-600'
            }`}>
              {(product as any).stock || 0} 件
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <span className={`px-2 py-1 rounded-full text-xs ${
              product.available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {product.available ? '有货' : '缺货'}
            </span>
          </div>
        </div>

        {/* 发布日期 */}
        {(product as any).publishedAt && (
          <div className="flex items-center justify-between mb-3 text-xs text-gray-500">
            <span>发布时间:</span>
            <span>{new Date((product as any).publishedAt).toLocaleDateString('zh-CN')}</span>
          </div>
        )}

        <div className="mb-3">
          <div className="flex flex-wrap gap-1">
            {product.ingredients.slice(0, 3).map((ingredient, index) => (
              <span
                key={index}
                className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs"
              >
                {ingredient}
              </span>
            ))}
            {product.ingredients.length > 3 && (
              <span className="text-gray-500 text-xs">...</span>
            )}
          </div>
        </div>

        <div className="flex justify-between items-center">
          <Link
            href={`/products/${product.id}`}
            className="text-orange-500 hover:text-orange-600 text-sm font-medium"
          >
            查看详情 →
          </Link>
          <span className={`text-sm ${product.available ? 'text-green-600' : 'text-red-600'}`}>
            {product.available ? '有货' : '暂缺'}
          </span>
        </div>
      </div>
    </div>
  );
}