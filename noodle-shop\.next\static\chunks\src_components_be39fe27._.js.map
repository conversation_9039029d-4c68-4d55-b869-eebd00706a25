{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/ImageUpload.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, DragEvent } from 'react';\r\nimport Image from 'next/image';\r\n\r\ninterface ImageUploadProps {\r\n  value?: string;\r\n  onChange: (url: string) => void;\r\n  placeholder?: string;\r\n  className?: string;\r\n}\r\n\r\nexport default function ImageUpload({\r\n  value,\r\n  onChange,\r\n  placeholder = '点击或拖拽图片到此处上传',\r\n  className = ''\r\n}: ImageUploadProps) {\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [preview, setPreview] = useState<string>(value || '');\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(true);\r\n  };\r\n\r\n  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n  };\r\n\r\n  const handleDrop = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n\r\n    const files = e.dataTransfer.files;\r\n    if (files && files[0]) {\r\n      handleFile(files[0]);\r\n    }\r\n  };\r\n\r\n  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = e.target.files;\r\n    if (files && files[0]) {\r\n      handleFile(files[0]);\r\n    }\r\n  };\r\n\r\n  const handleFile = async (file: File) => {\r\n    // 验证文件类型\r\n    if (!file.type.startsWith('image/')) {\r\n      alert('请上传图片文件');\r\n      return;\r\n    }\r\n\r\n    // 验证文件大小（最大 5MB）\r\n    if (file.size > 5 * 1024 * 1024) {\r\n      alert('图片大小不能超过 5MB');\r\n      return;\r\n    }\r\n\r\n    setIsUploading(true);\r\n\r\n    try {\r\n      // 创建本地预览\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        const result = e.target?.result as string;\r\n        setPreview(result);\r\n      };\r\n      reader.readAsDataURL(file);\r\n\r\n      // 模拟上传到图床\r\n      // 实际项目中，这里应该调用真实的图床 API\r\n      const formData = new FormData();\r\n      formData.append('image', file);\r\n\r\n      // 模拟上传延迟\r\n      await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n      // 模拟返回的图片 URL\r\n      // 实际项目中，这里应该是从图床 API 返回的 URL\r\n      const mockImageUrl = URL.createObjectURL(file);\r\n      \r\n      onChange(mockImageUrl);\r\n      alert('图片上传成功！（注：这是模拟上传，实际项目需要配置图床服务）');\r\n    } catch (error) {\r\n      console.error('Upload failed:', error);\r\n      alert('图片上传失败，请重试');\r\n    } finally {\r\n      setIsUploading(false);\r\n    }\r\n  };\r\n\r\n  const handleRemove = () => {\r\n    setPreview('');\r\n    onChange('');\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <div\r\n        className={`relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all ${\r\n          isDragging\r\n            ? 'border-orange-500 bg-orange-50'\r\n            : 'border-gray-300 hover:border-gray-400'\r\n        }`}\r\n        onDragOver={handleDragOver}\r\n        onDragLeave={handleDragLeave}\r\n        onDrop={handleDrop}\r\n        onClick={() => fileInputRef.current?.click()}\r\n      >\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={handleFileSelect}\r\n          className=\"hidden\"\r\n        />\r\n\r\n        {isUploading ? (\r\n          <div className=\"py-8\">\r\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4\"></div>\r\n            <p className=\"text-gray-600\">上传中...</p>\r\n          </div>\r\n        ) : preview ? (\r\n          <div className=\"relative\">\r\n            <div className=\"relative w-full h-48 mb-4\">\r\n              <Image\r\n                src={preview}\r\n                alt=\"Preview\"\r\n                fill\r\n                className=\"object-contain rounded\"\r\n              />\r\n            </div>\r\n            <button\r\n              type=\"button\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                handleRemove();\r\n              }}\r\n              className=\"absolute top-2 right-2 bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors\"\r\n            >\r\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </button>\r\n            <p className=\"text-sm text-gray-600\">点击重新上传</p>\r\n          </div>\r\n        ) : (\r\n          <div className=\"py-8\">\r\n            <svg\r\n              className=\"mx-auto h-12 w-12 text-gray-400\"\r\n              stroke=\"currentColor\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 48 48\"\r\n            >\r\n              <path\r\n                d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\"\r\n                strokeWidth={2}\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n              />\r\n            </svg>\r\n            <p className=\"mt-2 text-sm text-gray-600\">{placeholder}</p>\r\n            <p className=\"mt-1 text-xs text-gray-500\">支持 JPG、PNG、GIF 格式，最大 5MB</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"mt-2 text-xs text-gray-500\">\r\n        <p>提示：实际项目中需要配置图床服务（如七牛云、阿里云OSS等）</p>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAYe,SAAS,YAAY,KAKjB;QALiB,EAClC,KAAK,EACL,QAAQ,EACR,cAAc,cAAc,EAC5B,YAAY,EAAE,EACG,GALiB;;IAMlC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,SAAS;IACxD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QAEd,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;YACrB,WAAW,KAAK,CAAC,EAAE;QACrB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;YACrB,WAAW,KAAK,CAAC,EAAE;QACrB;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,SAAS;QACT,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,MAAM;YACN;QACF;QAEA,iBAAiB;QACjB,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,MAAM;YACN;QACF;QAEA,eAAe;QAEf,IAAI;YACF,SAAS;YACT,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;oBACA;gBAAf,MAAM,UAAS,YAAA,EAAE,MAAM,cAAR,gCAAA,UAAU,MAAM;gBAC/B,WAAW;YACb;YACA,OAAO,aAAa,CAAC;YAErB,UAAU;YACV,wBAAwB;YACxB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS;YAEzB,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,cAAc;YACd,6BAA6B;YAC7B,MAAM,eAAe,IAAI,eAAe,CAAC;YAEzC,SAAS;YACT,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,WAAW;QACX,SAAS;QACT,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW;;0BACd,6LAAC;gBACC,WAAW,AAAC,4FAIX,OAHC,aACI,mCACA;gBAEN,YAAY;gBACZ,aAAa;gBACb,QAAQ;gBACR,SAAS;wBAAM;4BAAA,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,KAAK;;;kCAE1C,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAO;wBACP,UAAU;wBACV,WAAU;;;;;;oBAGX,4BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;+BAE7B,wBACF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK;oCACL,KAAI;oCACJ,IAAI;oCACJ,WAAU;;;;;;;;;;;0CAGd,6LAAC;gCACC,MAAK;gCACL,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;6CAGvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,QAAO;gCACP,MAAK;gCACL,SAAQ;0CAER,cAAA,6LAAC;oCACC,GAAE;oCACF,aAAa;oCACb,eAAc;oCACd,gBAAe;;;;;;;;;;;0CAGnB,6LAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAC3C,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;0BAKhD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;8BAAE;;;;;;;;;;;;;;;;;AAIX;GAxKwB;KAAA", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/ProductForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Product, ProductType } from '@/types';\nimport { useRouter } from 'next/navigation';\nimport ImageUpload from '@/components/ImageUpload';\n\ninterface ProductFormProps {\n  product?: Product;\n  isEdit?: boolean;\n}\n\nexport default function ProductForm({ product, isEdit = false }: ProductFormProps) {\n  const router = useRouter();\n  const [loading, setLoading] = useState(false);\n  const [productTypes, setProductTypes] = useState<ProductType[]>([]);\n  const [formData, setFormData] = useState({\n    name: product?.name || '',\n    description: product?.description || '',\n    price: product?.price || 0,\n    category: product?.category || '',\n    productTypeId: product?.productTypeId || '',\n    image: product?.image || '',\n    ingredients: product?.ingredients?.join(', ') || '',\n    spicyLevel: product?.spicyLevel || 0,\n    available: product?.available ?? true,\n  });\n\n  useEffect(() => {\n    fetchProductTypes();\n  }, []);\n\n  const fetchProductTypes = async () => {\n    try {\n      const response = await fetch('/api/product-types');\n      const data = await response.json();\n      setProductTypes(data);\n      \n      // 如果没有设置产品类型，设置第一个为默认值\n      if (!formData.productTypeId && data.length > 0) {\n        setFormData(prev => ({\n          ...prev,\n          productTypeId: data[0].id,\n          category: data[0].id\n        }));\n      }\n    } catch (error) {\n      console.error('Failed to fetch product types:', error);\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      const ingredientsArray = formData.ingredients\n        .split(',')\n        .map(item => item.trim())\n        .filter(item => item.length > 0);\n\n      const productData = {\n        ...formData,\n        price: Number(formData.price),\n        spicyLevel: Number(formData.spicyLevel),\n        ingredients: ingredientsArray,\n        category: formData.productTypeId, // 保持兼容性\n        productTypeId: formData.productTypeId,\n        image: formData.image || `/images/default-product.svg`,\n      };\n\n      const url = isEdit ? `/api/products/${product?.id}` : '/api/products';\n      const method = isEdit ? 'PUT' : 'POST';\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(productData),\n      });\n\n      if (response.ok) {\n        router.push('/admin/products');\n      } else {\n        alert(isEdit ? '更新失败' : '添加失败');\n      }\n    } catch (error) {\n      console.error('Failed to save product:', error);\n      alert(isEdit ? '更新失败' : '添加失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,\n    }));\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <h1 className=\"text-2xl font-bold text-orange-600\">\n            {isEdit ? '编辑菜品' : '添加新菜品'}\n          </h1>\n        </div>\n      </header>\n\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6\">\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                菜品名称 *\n              </label>\n              <input\n                type=\"text\"\n                name=\"name\"\n                value={formData.name}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                描述 *\n              </label>\n              <textarea\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                required\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  价格 (¥) *\n                </label>\n                <input\n                  type=\"number\"\n                  name=\"price\"\n                  value={formData.price}\n                  onChange={handleInputChange}\n                  required\n                  min=\"0\"\n                  step=\"0.01\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  产品类型 *\n                </label>\n                <select\n                  name=\"productTypeId\"\n                  value={formData.productTypeId}\n                  onChange={(e) => {\n                    const value = e.target.value;\n                    setFormData(prev => ({\n                      ...prev,\n                      productTypeId: value,\n                      category: value // 保持兼容性\n                    }));\n                  }}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n                >\n                  {productTypes.length === 0 ? (\n                    <option value=\"\">加载中...</option>\n                  ) : (\n                    productTypes.map(type => (\n                      <option key={type.id} value={type.id}>\n                        {type.name}\n                      </option>\n                    ))\n                  )}\n                </select>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                产品图片\n              </label>\n              <ImageUpload\n                value={formData.image}\n                onChange={(url) => setFormData({ ...formData, image: url })}\n                placeholder=\"点击或拖拽图片上传\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                配料 (用逗号分隔) *\n              </label>\n              <input\n                type=\"text\"\n                name=\"ingredients\"\n                value={formData.ingredients}\n                onChange={handleInputChange}\n                required\n                placeholder=\"例如: 牛肉, 拉面, 白萝卜\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                辣度等级 (0-5)\n              </label>\n              <select\n                name=\"spicyLevel\"\n                value={formData.spicyLevel}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n              >\n                <option value={0}>0 - 不辣</option>\n                <option value={1}>1 - 微辣</option>\n                <option value={2}>2 - 轻辣</option>\n                <option value={3}>3 - 中辣</option>\n                <option value={4}>4 - 很辣</option>\n                <option value={5}>5 - 超辣</option>\n              </select>\n            </div>\n\n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                name=\"available\"\n                checked={formData.available}\n                onChange={handleInputChange}\n                className=\"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded\"\n              />\n              <label className=\"ml-2 block text-sm text-gray-700\">\n                现在有货\n              </label>\n            </div>\n\n            <div className=\"flex justify-end space-x-4\">\n              <button\n                type=\"button\"\n                onClick={() => router.push('/admin/products')}\n                className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors\"\n              >\n                取消\n              </button>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors disabled:opacity-50\"\n              >\n                {loading ? '保存中...' : (isEdit ? '更新' : '添加')}\n              </button>\n            </div>\n          </form>\n        </div>\n      </main>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;;;AALA;;;;AAYe,SAAS,YAAY,KAA6C;QAA7C,EAAE,OAAO,EAAE,SAAS,KAAK,EAAoB,GAA7C;QAWnB;;IAVf,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;QAUrD;IATb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM,CAAA,oBAAA,8BAAA,QAAS,IAAI,KAAI;QACvB,aAAa,CAAA,oBAAA,8BAAA,QAAS,WAAW,KAAI;QACrC,OAAO,CAAA,oBAAA,8BAAA,QAAS,KAAK,KAAI;QACzB,UAAU,CAAA,oBAAA,8BAAA,QAAS,QAAQ,KAAI;QAC/B,eAAe,CAAA,oBAAA,8BAAA,QAAS,aAAa,KAAI;QACzC,OAAO,CAAA,oBAAA,8BAAA,QAAS,KAAK,KAAI;QACzB,aAAa,CAAA,oBAAA,+BAAA,uBAAA,QAAS,WAAW,cAApB,2CAAA,qBAAsB,IAAI,CAAC,UAAS;QACjD,YAAY,CAAA,oBAAA,8BAAA,QAAS,UAAU,KAAI;QACnC,WAAW,CAAA,qBAAA,oBAAA,8BAAA,QAAS,SAAS,cAAlB,gCAAA,qBAAsB;IACnC;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,gBAAgB;YAEhB,uBAAuB;YACvB,IAAI,CAAC,SAAS,aAAa,IAAI,KAAK,MAAM,GAAG,GAAG;gBAC9C,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,eAAe,IAAI,CAAC,EAAE,CAAC,EAAE;wBACzB,UAAU,IAAI,CAAC,EAAE,CAAC,EAAE;oBACtB,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,mBAAmB,SAAS,WAAW,CAC1C,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IACrB,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;YAEhC,MAAM,cAAc;gBAClB,GAAG,QAAQ;gBACX,OAAO,OAAO,SAAS,KAAK;gBAC5B,YAAY,OAAO,SAAS,UAAU;gBACtC,aAAa;gBACb,UAAU,SAAS,aAAa;gBAChC,eAAe,SAAS,aAAa;gBACrC,OAAO,SAAS,KAAK,IAAK;YAC5B;YAEA,MAAM,MAAM,SAAS,AAAC,iBAA4B,OAAZ,oBAAA,8BAAA,QAAS,EAAE,IAAK;YACtD,MAAM,SAAS,SAAS,QAAQ;YAEhC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,SAAS,SAAS;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,SAAS,SAAS;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;YACzE,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCACX,SAAS,SAAS;;;;;;;;;;;;;;;;0BAKzB,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,MAAK;wCACL,OAAO,SAAS,IAAI;wCACpB,UAAU;wCACV,QAAQ;wCACR,WAAU;;;;;;;;;;;;0CAId,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,WAAW;wCAC3B,UAAU;wCACV,QAAQ;wCACR,MAAM;wCACN,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,QAAQ;gDACR,KAAI;gDACJ,MAAK;gDACL,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC;oDACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAC5B,YAAY,CAAA,OAAQ,CAAC;4DACnB,GAAG,IAAI;4DACP,eAAe;4DACf,UAAU,MAAM,QAAQ;wDAC1B,CAAC;gDACH;gDACA,QAAQ;gDACR,WAAU;0DAET,aAAa,MAAM,KAAK,kBACvB,6LAAC;oDAAO,OAAM;8DAAG;;;;;2DAEjB,aAAa,GAAG,CAAC,CAAA,qBACf,6LAAC;wDAAqB,OAAO,KAAK,EAAE;kEACjC,KAAK,IAAI;uDADC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;0CAS9B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC,oIAAA,CAAA,UAAW;wCACV,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,MAAQ,YAAY;gDAAE,GAAG,QAAQ;gDAAE,OAAO;4CAAI;wCACzD,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,MAAK;wCACL,OAAO,SAAS,WAAW;wCAC3B,UAAU;wCACV,QAAQ;wCACR,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAId,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,UAAU;wCAC1B,UAAU;wCACV,WAAU;;0DAEV,6LAAC;gDAAO,OAAO;0DAAG;;;;;;0DAClB,6LAAC;gDAAO,OAAO;0DAAG;;;;;;0DAClB,6LAAC;gDAAO,OAAO;0DAAG;;;;;;0DAClB,6LAAC;gDAAO,OAAO;0DAAG;;;;;;0DAClB,6LAAC;gDAAO,OAAO;0DAAG;;;;;;0DAClB,6LAAC;gDAAO,OAAO;0DAAG;;;;;;;;;;;;;;;;;;0CAItB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,MAAK;wCACL,SAAS,SAAS,SAAS;wCAC3B,UAAU;wCACV,WAAU;;;;;;kDAEZ,6LAAC;wCAAM,WAAU;kDAAmC;;;;;;;;;;;;0CAKtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,UAAU,WAAY,SAAS,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;GAnQwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}