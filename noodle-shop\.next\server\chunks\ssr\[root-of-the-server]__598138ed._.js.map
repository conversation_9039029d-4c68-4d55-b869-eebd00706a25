{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/lib/data.ts"], "sourcesContent": ["import { Product, Store } from '@/types';\nimport productsData from '@/data/products.json';\nimport storesData from '@/data/stores.json';\nimport fs from 'fs/promises';\nimport path from 'path';\n\nconst PRODUCTS_FILE = path.join(process.cwd(), 'src/data/products.json');\nconst STORES_FILE = path.join(process.cwd(), 'src/data/stores.json');\n\nexport async function getProducts(): Promise<Product[]> {\n  return productsData as Product[];\n}\n\nexport async function getProductById(id: string): Promise<Product | null> {\n  const products = await getProducts();\n  return products.find(product => product.id === id) || null;\n}\n\nexport async function getProductsByCategory(category: string): Promise<Product[]> {\n  const products = await getProducts();\n  return products.filter(product => product.category === category);\n}\n\nexport async function getStores(): Promise<Store[]> {\n  return storesData as Store[];\n}\n\nexport async function getStoreById(id: string): Promise<Store | null> {\n  const stores = await getStores();\n  return stores.find(store => store.id === id) || null;\n}\n\nexport async function saveProducts(products: Product[]): Promise<void> {\n  await fs.writeFile(PRODUCTS_FILE, JSON.stringify(products, null, 2));\n}\n\nexport async function saveStores(stores: Store[]): Promise<void> {\n  await fs.writeFile(STORES_FILE, JSON.stringify(stores, null, 2));\n}\n\nexport async function addProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> {\n  const products = await getProducts();\n  const newProduct: Product = {\n    ...product,\n    id: Date.now().toString(),\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  };\n  products.push(newProduct);\n  await saveProducts(products);\n  return newProduct;\n}\n\nexport async function updateProduct(id: string, updates: Partial<Product>): Promise<Product | null> {\n  const products = await getProducts();\n  const index = products.findIndex(product => product.id === id);\n  if (index === -1) return null;\n  \n  products[index] = {\n    ...products[index],\n    ...updates,\n    updatedAt: new Date().toISOString(),\n  };\n  await saveProducts(products);\n  return products[index];\n}\n\nexport async function deleteProduct(id: string): Promise<boolean> {\n  const products = await getProducts();\n  const index = products.findIndex(product => product.id === id);\n  if (index === -1) return false;\n  \n  products.splice(index, 1);\n  await saveProducts(products);\n  return true;\n}\n\nexport async function addStore(store: Omit<Store, 'id' | 'createdAt' | 'updatedAt'>): Promise<Store> {\n  const stores = await getStores();\n  const newStore: Store = {\n    ...store,\n    id: Date.now().toString(),\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  };\n  stores.push(newStore);\n  await saveStores(stores);\n  return newStore;\n}\n\nexport async function updateStore(id: string, updates: Partial<Store>): Promise<Store | null> {\n  const stores = await getStores();\n  const index = stores.findIndex(store => store.id === id);\n  if (index === -1) return null;\n  \n  stores[index] = {\n    ...stores[index],\n    ...updates,\n    updatedAt: new Date().toISOString(),\n  };\n  await saveStores(stores);\n  return stores[index];\n}\n\nexport async function deleteStore(id: string): Promise<boolean> {\n  const stores = await getStores();\n  const index = stores.findIndex(store => store.id === id);\n  if (index === -1) return false;\n  \n  stores.splice(index, 1);\n  await saveStores(stores);\n  return true;\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC/C,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAEtC,eAAe;IACpB,OAAO,+FAAA,CAAA,UAAY;AACrB;AAEO,eAAe,eAAe,EAAU;IAC7C,MAAM,WAAW,MAAM;IACvB,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK,OAAO;AACxD;AAEO,eAAe,sBAAsB,QAAgB;IAC1D,MAAM,WAAW,MAAM;IACvB,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACzD;AAEO,eAAe;IACpB,OAAO,6FAAA,CAAA,UAAU;AACnB;AAEO,eAAe,aAAa,EAAU;IAC3C,MAAM,SAAS,MAAM;IACrB,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,OAAO;AAClD;AAEO,eAAe,aAAa,QAAmB;IACpD,MAAM,qHAAA,CAAA,UAAE,CAAC,SAAS,CAAC,eAAe,KAAK,SAAS,CAAC,UAAU,MAAM;AACnE;AAEO,eAAe,WAAW,MAAe;IAC9C,MAAM,qHAAA,CAAA,UAAE,CAAC,SAAS,CAAC,aAAa,KAAK,SAAS,CAAC,QAAQ,MAAM;AAC/D;AAEO,eAAe,WAAW,OAAwD;IACvF,MAAM,WAAW,MAAM;IACvB,MAAM,aAAsB;QAC1B,GAAG,OAAO;QACV,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,SAAS,IAAI,CAAC;IACd,MAAM,aAAa;IACnB,OAAO;AACT;AAEO,eAAe,cAAc,EAAU,EAAE,OAAyB;IACvE,MAAM,WAAW,MAAM;IACvB,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,QAAQ,CAAC,MAAM,GAAG;QAChB,GAAG,QAAQ,CAAC,MAAM;QAClB,GAAG,OAAO;QACV,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,MAAM,aAAa;IACnB,OAAO,QAAQ,CAAC,MAAM;AACxB;AAEO,eAAe,cAAc,EAAU;IAC5C,MAAM,WAAW,MAAM;IACvB,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,SAAS,MAAM,CAAC,OAAO;IACvB,MAAM,aAAa;IACnB,OAAO;AACT;AAEO,eAAe,SAAS,KAAoD;IACjF,MAAM,SAAS,MAAM;IACrB,MAAM,WAAkB;QACtB,GAAG,KAAK;QACR,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,OAAO,IAAI,CAAC;IACZ,MAAM,WAAW;IACjB,OAAO;AACT;AAEO,eAAe,YAAY,EAAU,EAAE,OAAuB;IACnE,MAAM,SAAS,MAAM;IACrB,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACrD,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,MAAM,CAAC,MAAM,GAAG;QACd,GAAG,MAAM,CAAC,MAAM;QAChB,GAAG,OAAO;QACV,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,MAAM,WAAW;IACjB,OAAO,MAAM,CAAC,MAAM;AACtB;AAEO,eAAe,YAAY,EAAU;IAC1C,MAAM,SAAS,MAAM;IACrB,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACrD,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,OAAO,MAAM,CAAC,OAAO;IACrB,MAAM,WAAW;IACjB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/ProductCard.tsx"], "sourcesContent": ["import { Product } from '@/types';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nexport default function ProductCard({ product }: ProductCardProps) {\n  const spicyIcons = '🌶️'.repeat(product.spicyLevel);\n  const categoryText = {\n    noodles: '面条',\n    pasta: '面片',\n    soup: '汤面'\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n      <div className=\"relative h-48\">\n        <Image\n          src={product.image}\n          alt={product.name}\n          fill\n          className=\"object-cover\"\n        />\n        <div className=\"absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded-full text-xs\">\n          {categoryText[product.category]}\n        </div>\n      </div>\n      <div className=\"p-4\">\n        <h3 className=\"text-xl font-semibold text-gray-800 mb-2\">{product.name}</h3>\n        <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">{product.description}</p>\n        \n        <div className=\"flex items-center justify-between mb-3\">\n          <span className=\"text-2xl font-bold text-orange-600\">¥{product.price}</span>\n          <div className=\"flex items-center space-x-1\">\n            <span className=\"text-gray-500 text-sm\">辣度:</span>\n            <span className=\"text-sm\">{spicyIcons || '不辣'}</span>\n          </div>\n        </div>\n\n        <div className=\"mb-3\">\n          <div className=\"flex flex-wrap gap-1\">\n            {product.ingredients.slice(0, 3).map((ingredient, index) => (\n              <span\n                key={index}\n                className=\"bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs\"\n              >\n                {ingredient}\n              </span>\n            ))}\n            {product.ingredients.length > 3 && (\n              <span className=\"text-gray-500 text-xs\">...</span>\n            )}\n          </div>\n        </div>\n\n        <div className=\"flex justify-between items-center\">\n          <Link\n            href={`/products/${product.id}`}\n            className=\"text-orange-500 hover:text-orange-600 text-sm font-medium\"\n          >\n            查看详情 →\n          </Link>\n          <span className={`text-sm ${product.available ? 'text-green-600' : 'text-red-600'}`}>\n            {product.available ? '有货' : '暂缺'}\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAMe,SAAS,YAAY,EAAE,OAAO,EAAoB;IAC/D,MAAM,aAAa,MAAM,MAAM,CAAC,QAAQ,UAAU;IAClD,MAAM,eAAe;QACnB,SAAS;QACT,OAAO;QACP,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,KAAK;wBAClB,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;kCACZ,YAAY,CAAC,QAAQ,QAAQ,CAAC;;;;;;;;;;;;0BAGnC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4C,QAAQ,IAAI;;;;;;kCACtE,8OAAC;wBAAE,WAAU;kCAA2C,QAAQ,WAAW;;;;;;kCAE3E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;;oCAAqC;oCAAE,QAAQ,KAAK;;;;;;;0CACpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;kDAAW,cAAc;;;;;;;;;;;;;;;;;;kCAI7C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,QAAQ,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,YAAY,sBAChD,8OAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;gCAMR,QAAQ,WAAW,CAAC,MAAM,GAAG,mBAC5B,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAK9C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gCAC/B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAK,WAAW,CAAC,QAAQ,EAAE,QAAQ,SAAS,GAAG,mBAAmB,gBAAgB;0CAChF,QAAQ,SAAS,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/WeatherWidget.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/WeatherWidget.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/WeatherWidget.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/WeatherWidget.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/WeatherWidget.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/WeatherWidget.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/products/page.tsx"], "sourcesContent": ["import { getProducts } from '@/lib/data';\nimport ProductCard from '@/components/ProductCard';\nimport WeatherWidget from '@/components/WeatherWidget';\nimport Link from 'next/link';\n\nexport default async function ProductsPage() {\n  const products = await getProducts();\n  const categories = {\n    all: '全部',\n    noodles: '面条',\n    pasta: '面片',\n    soup: '汤面'\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/\" className=\"text-2xl font-bold text-orange-600 hover:text-orange-700\">\n                香香面条店\n              </Link>\n              <span className=\"text-gray-600\">菜品列表</span>\n            </div>\n            <WeatherWidget />\n          </div>\n        </div>\n      </header>\n\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-800 mb-4\">所有菜品</h1>\n          <p className=\"text-gray-600\">精选传统面食，每一道都是用心之作</p>\n        </div>\n\n        <div className=\"mb-6\">\n          <div className=\"flex flex-wrap gap-4\">\n            {Object.entries(categories).map(([key, label]) => (\n              <button\n                key={key}\n                className=\"px-4 py-2 bg-white border border-gray-200 rounded-lg hover:border-orange-500 hover:text-orange-600 transition-colors\"\n              >\n                {label}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {products.map((product) => (\n            <ProductCard key={product.id} product={product} />\n          ))}\n        </div>\n\n        {products.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">🍜</div>\n            <h2 className=\"text-xl text-gray-600\">暂无菜品</h2>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,eAAe;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,kHAAA,CAAA,cAAW,AAAD;IACjC,MAAM,aAAa;QACjB,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAA2D;;;;;;kDAGpF,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC,mIAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;0BAKpB,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAG/B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC3C,8OAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;;;;;;;;;;;kCASb,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,iIAAA,CAAA,UAAW;gCAAkB,SAAS;+BAArB,QAAQ,EAAE;;;;;;;;;;oBAI/B,SAAS,MAAM,KAAK,mBACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;AAMlD", "debugId": null}}]}