import { NextRequest } from 'next/server';
import { updateProductStock } from '@/lib/database';

interface RouteContext {
  params: Promise<{ id: string }>;
}

// PUT - 更新产品库存
export async function PUT(request: NextRequest, context: RouteContext) {
  try {
    const { id } = await context.params;
    const body = await request.json();
    
    const { quantity } = body;
    
    if (typeof quantity !== 'number') {
      return Response.json({ error: '库存数量必须是数字' }, { status: 400 });
    }
    
    const product = await updateProductStock(id, quantity);
    
    return Response.json({
      ...product,
      category: product.productTypeId,
      ingredients: product.ingredients ? JSON.parse(product.ingredients) : [],
    });
  } catch (error: any) {
    console.error('Failed to update product stock:', error);
    if (error?.message === '产品不存在') {
      return Response.json({ error: 'Product not found' }, { status: 404 });
    }
    return Response.json({ error: 'Failed to update product stock' }, { status: 500 });
  }
}
