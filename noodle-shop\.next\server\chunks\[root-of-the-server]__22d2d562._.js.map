{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/lib/database.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\nimport { z } from 'zod';\n\n// 全局Prisma客户端实例\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Zod验证模式\nexport const ProductTypeSchema = z.object({\n  id: z.string().min(1),\n  name: z.string().min(1),\n  description: z.string().optional(),\n  displayOrder: z.number().int().default(0),\n  isActive: z.boolean().default(true),\n});\n\nexport const ProductSchema = z.object({\n  id: z.string().min(1),\n  name: z.string().min(1),\n  description: z.string().optional(),\n  price: z.number().positive(),\n  productTypeId: z.string().min(1),\n  image: z.string().optional(),\n  ingredients: z.array(z.string()).optional(),\n  spicyLevel: z.number().int().min(0).max(5).default(0),\n  available: z.boolean().default(true),\n  stock: z.number().int().min(0).default(0),\n  minStock: z.number().int().min(0).default(5),\n  isActive: z.boolean().default(true),\n});\n\nexport const StoreSchema = z.object({\n  id: z.string().min(1),\n  name: z.string().min(1),\n  address: z.string().min(1),\n  phone: z.string().optional(),\n  latitude: z.number().optional(),\n  longitude: z.number().optional(),\n  openTime: z.string().optional(),\n  closeTime: z.string().optional(),\n  isOpen: z.boolean().default(true),\n  image: z.string().optional(),\n});\n\n// 产品类型相关操作\nexport async function getProductTypes() {\n  return await prisma.productType.findMany({\n    where: { isActive: true },\n    orderBy: { displayOrder: 'asc' },\n  });\n}\n\nexport async function getProductTypeById(id: string) {\n  return await prisma.productType.findUnique({\n    where: { id },\n  });\n}\n\nexport async function createProductType(data: z.infer<typeof ProductTypeSchema>) {\n  const validatedData = ProductTypeSchema.parse(data);\n  return await prisma.productType.create({\n    data: validatedData,\n  });\n}\n\nexport async function updateProductType(id: string, data: Partial<z.infer<typeof ProductTypeSchema>>) {\n  return await prisma.productType.update({\n    where: { id },\n    data,\n  });\n}\n\nexport async function deleteProductType(id: string) {\n  // 检查是否有关联的产品\n  const productCount = await prisma.product.count({\n    where: { productTypeId: id },\n  });\n  \n  if (productCount > 0) {\n    throw new Error('无法删除：该产品类型下还有产品');\n  }\n  \n  return await prisma.productType.delete({\n    where: { id },\n  });\n}\n\n// 产品相关操作\nexport async function getProducts() {\n  return await prisma.product.findMany({\n    include: {\n      productType: true,\n    },\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\nexport async function getProductById(id: string) {\n  return await prisma.product.findUnique({\n    where: { id },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function getProductsByCategory(productTypeId: string) {\n  return await prisma.product.findMany({\n    where: { productTypeId },\n    include: {\n      productType: true,\n    },\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\nexport async function createProduct(data: Omit<z.infer<typeof ProductSchema>, 'id'>) {\n  const validatedData = ProductSchema.omit({ id: true }).parse(data);\n  \n  // 生成新ID\n  const newId = Date.now().toString();\n  \n  return await prisma.product.create({\n    data: {\n      ...validatedData,\n      id: newId,\n      ingredients: validatedData.ingredients ? JSON.stringify(validatedData.ingredients) : null,\n    },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function updateProduct(id: string, data: Partial<Omit<z.infer<typeof ProductSchema>, 'id'>>) {\n  const updateData: any = { ...data };\n  \n  if (data.ingredients) {\n    updateData.ingredients = JSON.stringify(data.ingredients);\n  }\n  \n  return await prisma.product.update({\n    where: { id },\n    data: updateData,\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function deleteProduct(id: string) {\n  return await prisma.product.delete({\n    where: { id },\n  });\n}\n\n// 门店相关操作\nexport async function getStores() {\n  return await prisma.store.findMany({\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\nexport async function getStoreById(id: string) {\n  return await prisma.store.findUnique({\n    where: { id },\n  });\n}\n\nexport async function createStore(data: Omit<z.infer<typeof StoreSchema>, 'id'>) {\n  const validatedData = StoreSchema.omit({ id: true }).parse(data);\n  \n  // 生成新ID\n  const newId = Date.now().toString();\n  \n  return await prisma.store.create({\n    data: {\n      ...validatedData,\n      id: newId,\n    },\n  });\n}\n\nexport async function updateStore(id: string, data: Partial<Omit<z.infer<typeof StoreSchema>, 'id'>>) {\n  return await prisma.store.update({\n    where: { id },\n    data,\n  });\n}\n\nexport async function deleteStore(id: string) {\n  return await prisma.store.delete({\n    where: { id },\n  });\n}\n\n// 库存管理相关操作\nexport async function updateProductStock(id: string, quantity: number) {\n  const product = await prisma.product.findUnique({ where: { id } });\n  if (!product) throw new Error('产品不存在');\n\n  const newStock = Math.max(0, product.stock + quantity);\n\n  return await prisma.product.update({\n    where: { id },\n    data: {\n      stock: newStock,\n      available: newStock > 0, // 自动更新可用状态\n    },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function toggleProductStatus(id: string) {\n  const product = await prisma.product.findUnique({ where: { id } });\n  if (!product) throw new Error('产品不存在');\n\n  return await prisma.product.update({\n    where: { id },\n    data: { isActive: !product.isActive },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function getLowStockProducts() {\n  return await prisma.product.findMany({\n    where: {\n      OR: [\n        { stock: { lte: prisma.product.fields.minStock } },\n        { stock: 0 },\n      ],\n    },\n    include: {\n      productType: true,\n    },\n    orderBy: { stock: 'asc' },\n  });\n}\n\nexport async function getActiveProducts() {\n  return await prisma.product.findMany({\n    where: {\n      isActive: true,\n      available: true,\n    },\n    include: {\n      productType: true,\n    },\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\n// 工具函数：解析ingredients JSON字符串\nexport function parseIngredients(ingredients: string | null): string[] {\n  if (!ingredients) return [];\n  try {\n    return JSON.parse(ingredients);\n  } catch {\n    return [];\n  }\n}\n\n// 工具函数：获取库存状态\nexport function getStockStatus(stock: number, minStock: number) {\n  if (stock === 0) return { status: 'out_of_stock', label: '缺货', color: 'red' };\n  if (stock <= minStock) return { status: 'low_stock', label: '库存不足', color: 'yellow' };\n  return { status: 'in_stock', label: '库存充足', color: 'green' };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEA,gBAAgB;AAChB,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,MAAM,oBAAoB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;IACvC,UAAU,+KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAEO,MAAM,gBAAgB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,eAAe,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC9B,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,aAAa,+KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,+KAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IACzC,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACnD,WAAW,+KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACvC,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAC1C,UAAU,+KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAEO,MAAM,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACxB,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,QAAQ,+KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC5B,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AAGO,eAAe;IACpB,OAAO,MAAM,OAAO,WAAW,CAAC,QAAQ,CAAC;QACvC,OAAO;YAAE,UAAU;QAAK;QACxB,SAAS;YAAE,cAAc;QAAM;IACjC;AACF;AAEO,eAAe,mBAAmB,EAAU;IACjD,OAAO,MAAM,OAAO,WAAW,CAAC,UAAU,CAAC;QACzC,OAAO;YAAE;QAAG;IACd;AACF;AAEO,eAAe,kBAAkB,IAAuC;IAC7E,MAAM,gBAAgB,kBAAkB,KAAK,CAAC;IAC9C,OAAO,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;QACrC,MAAM;IACR;AACF;AAEO,eAAe,kBAAkB,EAAU,EAAE,IAAgD;IAClG,OAAO,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;QACrC,OAAO;YAAE;QAAG;QACZ;IACF;AACF;AAEO,eAAe,kBAAkB,EAAU;IAChD,aAAa;IACb,MAAM,eAAe,MAAM,OAAO,OAAO,CAAC,KAAK,CAAC;QAC9C,OAAO;YAAE,eAAe;QAAG;IAC7B;IAEA,IAAI,eAAe,GAAG;QACpB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;QACrC,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe;IACpB,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEO,eAAe,eAAe,EAAU;IAC7C,OAAO,MAAM,OAAO,OAAO,CAAC,UAAU,CAAC;QACrC,OAAO;YAAE;QAAG;QACZ,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,sBAAsB,aAAqB;IAC/D,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,OAAO;YAAE;QAAc;QACvB,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEO,eAAe,cAAc,IAA+C;IACjF,MAAM,gBAAgB,cAAc,IAAI,CAAC;QAAE,IAAI;IAAK,GAAG,KAAK,CAAC;IAE7D,QAAQ;IACR,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;IAEjC,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,MAAM;YACJ,GAAG,aAAa;YAChB,IAAI;YACJ,aAAa,cAAc,WAAW,GAAG,KAAK,SAAS,CAAC,cAAc,WAAW,IAAI;QACvF;QACA,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,cAAc,EAAU,EAAE,IAAwD;IACtG,MAAM,aAAkB;QAAE,GAAG,IAAI;IAAC;IAElC,IAAI,KAAK,WAAW,EAAE;QACpB,WAAW,WAAW,GAAG,KAAK,SAAS,CAAC,KAAK,WAAW;IAC1D;IAEA,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;QACZ,MAAM;QACN,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,cAAc,EAAU;IAC5C,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe;IACpB,OAAO,MAAM,OAAO,KAAK,CAAC,QAAQ,CAAC;QACjC,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEO,eAAe,aAAa,EAAU;IAC3C,OAAO,MAAM,OAAO,KAAK,CAAC,UAAU,CAAC;QACnC,OAAO;YAAE;QAAG;IACd;AACF;AAEO,eAAe,YAAY,IAA6C;IAC7E,MAAM,gBAAgB,YAAY,IAAI,CAAC;QAAE,IAAI;IAAK,GAAG,KAAK,CAAC;IAE3D,QAAQ;IACR,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;IAEjC,OAAO,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;QAC/B,MAAM;YACJ,GAAG,aAAa;YAChB,IAAI;QACN;IACF;AACF;AAEO,eAAe,YAAY,EAAU,EAAE,IAAsD;IAClG,OAAO,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;QAC/B,OAAO;YAAE;QAAG;QACZ;IACF;AACF;AAEO,eAAe,YAAY,EAAU;IAC1C,OAAO,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;QAC/B,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe,mBAAmB,EAAU,EAAE,QAAgB;IACnE,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC,UAAU,CAAC;QAAE,OAAO;YAAE;QAAG;IAAE;IAChE,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,QAAQ,KAAK,GAAG;IAE7C,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;QACZ,MAAM;YACJ,OAAO;YACP,WAAW,WAAW;QACxB;QACA,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,oBAAoB,EAAU;IAClD,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC,UAAU,CAAC;QAAE,OAAO;YAAE;QAAG;IAAE;IAChE,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;QACZ,MAAM;YAAE,UAAU,CAAC,QAAQ,QAAQ;QAAC;QACpC,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe;IACpB,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,OAAO;YACL,IAAI;gBACF;oBAAE,OAAO;wBAAE,KAAK,OAAO,OAAO,CAAC,MAAM,CAAC,QAAQ;oBAAC;gBAAE;gBACjD;oBAAE,OAAO;gBAAE;aACZ;QACH;QACA,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,OAAO;QAAM;IAC1B;AACF;AAEO,eAAe;IACpB,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,OAAO;YACL,UAAU;YACV,WAAW;QACb;QACA,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAGO,SAAS,iBAAiB,WAA0B;IACzD,IAAI,CAAC,aAAa,OAAO,EAAE;IAC3B,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO,EAAE;IACX;AACF;AAGO,SAAS,eAAe,KAAa,EAAE,QAAgB;IAC5D,IAAI,UAAU,GAAG,OAAO;QAAE,QAAQ;QAAgB,OAAO;QAAM,OAAO;IAAM;IAC5E,IAAI,SAAS,UAAU,OAAO;QAAE,QAAQ;QAAa,OAAO;QAAQ,OAAO;IAAS;IACpF,OAAO;QAAE,QAAQ;QAAY,OAAO;QAAQ,OAAO;IAAQ;AAC7D", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/api/product-types/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { getProductTypes, createProductType } from '@/lib/database';\r\n\r\n// GET - 获取所有产品类型\r\nexport async function GET() {\r\n  try {\r\n    const productTypes = await getProductTypes();\r\n    return NextResponse.json(productTypes);\r\n  } catch (error) {\r\n    console.error('Failed to fetch product types:', error);\r\n    return NextResponse.json(\r\n      { error: '获取产品类型失败' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// POST - 创建新产品类型\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n\r\n    // 验证必填字段\r\n    if (!body.name) {\r\n      return NextResponse.json(\r\n        { error: '名称为必填项' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // 准备数据\r\n    const productTypeData = {\r\n      id: `type-${Date.now()}`,\r\n      name: body.name,\r\n      description: body.description || '',\r\n      displayOrder: body.displayOrder || 0,\r\n      isActive: body.isActive !== false,\r\n    };\r\n\r\n    const newProductType = await createProductType(productTypeData);\r\n\r\n    return NextResponse.json(newProductType, { status: 201 });\r\n  } catch (error) {\r\n    console.error('Failed to create product type:', error);\r\n    return NextResponse.json(\r\n      { error: '创建产品类型失败' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,eAAe,MAAM,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,SAAS;QACT,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAS,GAClB;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,kBAAkB;YACtB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACxB,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW,IAAI;YACjC,cAAc,KAAK,YAAY,IAAI;YACnC,UAAU,KAAK,QAAQ,KAAK;QAC9B;QAEA,MAAM,iBAAiB,MAAM,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD,EAAE;QAE/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,gBAAgB;YAAE,QAAQ;QAAI;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}