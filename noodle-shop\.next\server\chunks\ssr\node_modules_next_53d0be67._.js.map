{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/image.js"], "sourcesContent": ["module.exports = require('./dist/shared/lib/image-external')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}