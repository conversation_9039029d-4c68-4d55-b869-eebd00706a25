(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/data/products.json (json, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_data_products_json_d0be56aa._.js",
  "static/chunks/src_data_products_json_e4e8a4f2._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/data/products.json (json)");
    });
});
}),
"[project]/src/data/stores.json (json, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_data_stores_json_09c3f796._.js",
  "static/chunks/src_data_stores_json_e4e8a4f2._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/data/stores.json (json)");
    });
});
}),
}]);