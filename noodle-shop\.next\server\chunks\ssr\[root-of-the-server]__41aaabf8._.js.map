{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/WeatherWidget.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { WeatherInfo, LocationInfo } from '@/types';\n\nexport default function WeatherWidget() {\n  const [weather, setWeather] = useState<WeatherInfo | null>(null);\n  const [location, setLocation] = useState<LocationInfo | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchLocationAndWeather = async () => {\n      try {\n        const locationResponse = await fetch('/api/location');\n        const locationData = await locationResponse.json();\n        setLocation(locationData);\n\n        const weatherResponse = await fetch(`/api/weather?lat=${locationData.latitude}&lon=${locationData.longitude}`);\n        const weatherData = await weatherResponse.json();\n        setWeather(weatherData);\n      } catch (error) {\n        console.error('Failed to fetch location or weather:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchLocationAndWeather();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center space-x-2 text-gray-500\">\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600\"></div>\n        <span>加载中...</span>\n      </div>\n    );\n  }\n\n  if (!weather || !location) {\n    return (\n      <div className=\"text-gray-500 text-sm\">\n        天气信息暂不可用\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex items-center space-x-4 text-sm\">\n      <div className=\"flex items-center space-x-1\">\n        <span className=\"text-gray-600\">IP:</span>\n        <span className=\"font-mono text-orange-600\">{location.ip}</span>\n      </div>\n      <div className=\"flex items-center space-x-2 bg-blue-50 px-3 py-1 rounded-full\">\n        <span className=\"text-blue-600\">{location.city}</span>\n        <span className=\"text-gray-500\">|</span>\n        <span className=\"text-blue-600\">{weather.temperature}°C</span>\n        <span className=\"text-gray-600\">{weather.description}</span>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,0BAA0B;YAC9B,IAAI;gBACF,MAAM,mBAAmB,MAAM,MAAM;gBACrC,MAAM,eAAe,MAAM,iBAAiB,IAAI;gBAChD,YAAY;gBAEZ,MAAM,kBAAkB,MAAM,MAAM,CAAC,iBAAiB,EAAE,aAAa,QAAQ,CAAC,KAAK,EAAE,aAAa,SAAS,EAAE;gBAC7G,MAAM,cAAc,MAAM,gBAAgB,IAAI;gBAC9C,WAAW;YACb,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;YACxD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;8BAAK;;;;;;;;;;;;IAGZ;IAEA,IAAI,CAAC,WAAW,CAAC,UAAU;QACzB,qBACE,8OAAC;YAAI,WAAU;sBAAwB;;;;;;IAI3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAgB;;;;;;kCAChC,8OAAC;wBAAK,WAAU;kCAA6B,SAAS,EAAE;;;;;;;;;;;;0BAE1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAiB,SAAS,IAAI;;;;;;kCAC9C,8OAAC;wBAAK,WAAU;kCAAgB;;;;;;kCAChC,8OAAC;wBAAK,WAAU;;4BAAiB,QAAQ,WAAW;4BAAC;;;;;;;kCACrD,8OAAC;wBAAK,WAAU;kCAAiB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;AAI5D", "debugId": null}}]}