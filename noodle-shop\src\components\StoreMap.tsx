'use client';

import { useEffect, useRef, useState } from 'react';
import { Store } from '@/types';

interface StoreMapProps {
  stores: Store[];
  selectedStore?: Store | null;
  onStoreSelect?: (store: Store) => void;
  height?: string;
  enableSearch?: boolean;
  showCurrentLocation?: boolean;
}

declare global {
  interface Window {
    BMap: any;
    BMAP_STATUS_SUCCESS: any;
    initBaiduMap?: () => void;
  }
}

export default function StoreMap({
  stores,
  selectedStore,
  onStoreSelect,
  height = '400px',
  enableSearch = false,
  showCurrentLocation = true
}: StoreMapProps) {
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<any>(null);
  const markersRef = useRef<any[]>([]);
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);

  // 加载百度地图脚本
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.BMap) {
      const script = document.createElement('script');
      script.src = `//api.map.baidu.com/api?v=3.0&ak=YOUR_BAIDU_MAP_KEY&callback=initBaiduMap`;
      script.async = true;
      
      window.initBaiduMap = () => {
        setIsMapLoaded(true);
      };
      
      document.head.appendChild(script);
      
      return () => {
        delete window.initBaiduMap;
        document.head.removeChild(script);
      };
    } else if (window.BMap) {
      setIsMapLoaded(true);
    }
  }, []);

  // 初始化地图
  useEffect(() => {
    if (!isMapLoaded || !mapContainerRef.current || !window.BMap) return;

    const map = new window.BMap.Map(mapContainerRef.current);
    const defaultCenter = new window.BMap.Point(116.404, 39.915); // 北京天安门
    map.centerAndZoom(defaultCenter, 12);
    map.enableScrollWheelZoom(true);
    
    // 添加地图控件
    map.addControl(new window.BMap.NavigationControl());
    map.addControl(new window.BMap.ScaleControl());
    
    mapRef.current = map;

    // 获取用户当前位置
    if (showCurrentLocation && navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setUserLocation({ lat: latitude, lng: longitude });
          
          const userPoint = new window.BMap.Point(longitude, latitude);
          map.centerAndZoom(userPoint, 14);
          
          // 添加用户位置标记
          const userMarker = new window.BMap.Marker(userPoint, {
            icon: new window.BMap.Icon(
              '/images/user-location.png',
              new window.BMap.Size(30, 30)
            )
          });
          map.addOverlay(userMarker);
        },
        (error) => {
          console.error('获取位置失败:', error);
        }
      );
    }
  }, [isMapLoaded, showCurrentLocation]);

  // 添加店铺标记
  useEffect(() => {
    if (!mapRef.current || !window.BMap) return;

    // 清除旧标记
    markersRef.current.forEach(marker => {
      mapRef.current.removeOverlay(marker);
    });
    markersRef.current = [];

    // 添加新标记
    stores.forEach((store, index) => {
      const point = new window.BMap.Point(store.longitude, store.latitude);
      const marker = new window.BMap.Marker(point);
      
      // 自定义标记图标
      const icon = new window.BMap.Icon(
        store.isOpen ? '/images/store-open.png' : '/images/store-closed.png',
        new window.BMap.Size(40, 40)
      );
      marker.setIcon(icon);
      
      // 添加标记点击事件
      marker.addEventListener('click', () => {
        if (onStoreSelect) {
          onStoreSelect(store);
        }
        
        // 显示信息窗口
        const infoWindow = new window.BMap.InfoWindow(`
          <div style="padding: 10px;">
            <h3 style="margin: 0 0 10px 0;">${store.name}</h3>
            <p style="margin: 5px 0;">地址：${store.address}</p>
            <p style="margin: 5px 0;">电话：${store.phone}</p>
            <p style="margin: 5px 0;">营业时间：${store.openTime} - ${store.closeTime}</p>
            <p style="margin: 5px 0;">状态：${store.isOpen ? '营业中' : '已打烊'}</p>
          </div>
        `);
        mapRef.current.openInfoWindow(infoWindow, point);
      });
      
      mapRef.current.addOverlay(marker);
      markersRef.current.push(marker);
      
      // 如果是第一个店铺，设置为地图中心
      if (index === 0 && !userLocation) {
        mapRef.current.centerAndZoom(point, 14);
      }
    });

    // 如果有选中的店铺，居中显示
    if (selectedStore) {
      const point = new window.BMap.Point(selectedStore.longitude, selectedStore.latitude);
      mapRef.current.centerAndZoom(point, 16);
    }
  }, [stores, selectedStore, onStoreSelect, userLocation]);

  // 搜索店铺
  const handleSearch = () => {
    if (!searchKeyword.trim()) return;
    
    const foundStore = stores.find(store => 
      store.name.includes(searchKeyword) || 
      store.address.includes(searchKeyword)
    );
    
    if (foundStore && onStoreSelect) {
      onStoreSelect(foundStore);
    }
  };

  // 查找最近的店铺
  const findNearestStore = () => {
    if (!userLocation || stores.length === 0) return;
    
    let nearestStore = stores[0];
    let minDistance = Number.MAX_VALUE;
    
    stores.forEach(store => {
      const distance = Math.sqrt(
        Math.pow(store.latitude - userLocation.lat, 2) + 
        Math.pow(store.longitude - userLocation.lng, 2)
      );
      
      if (distance < minDistance) {
        minDistance = distance;
        nearestStore = store;
      }
    });
    
    if (onStoreSelect) {
      onStoreSelect(nearestStore);
    }
  };

  return (
    <div className="relative">
      {enableSearch && (
        <div className="absolute top-4 left-4 z-10 bg-white rounded-lg shadow-md p-3">
          <div className="flex gap-2">
            <input
              type="text"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              placeholder="搜索店铺名称或地址"
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
            <button
              onClick={handleSearch}
              className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors"
            >
              搜索
            </button>
            {showCurrentLocation && userLocation && (
              <button
                onClick={findNearestStore}
                className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
              >
                最近店铺
              </button>
            )}
          </div>
        </div>
      )}
      
      <div 
        ref={mapContainerRef} 
        style={{ height, width: '100%' }}
        className="rounded-lg overflow-hidden"
      />
      
      {!isMapLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
            <p className="text-gray-600">地图加载中...</p>
          </div>
        </div>
      )}
      
      <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-sm text-yellow-800">
          <strong>提示：</strong>请在实际部署时替换百度地图 API Key。
          当前使用的是演示地图，某些功能可能无法正常工作。
        </p>
      </div>
    </div>
  );
}