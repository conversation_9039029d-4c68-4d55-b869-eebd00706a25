import { NextRequest } from 'next/server';
import { prisma, parseIngredients } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const category = searchParams.get('category') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    
    const skip = (page - 1) * limit;
    
    // 构建搜索条件
    const where: any = {
      isActive: true, // 只搜索上架的产品
    };
    
    // 添加搜索关键词条件
    if (query) {
      where.OR = [
        { name: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
        { ingredients: { contains: query, mode: 'insensitive' } },
      ];
    }
    
    // 添加分类筛选
    if (category && category !== 'all') {
      where.productTypeId = category;
    }
    
    // 构建排序条件
    const orderBy: any = {};
    if (sortBy === 'price') {
      orderBy.price = sortOrder;
    } else if (sortBy === 'name') {
      orderBy.name = sortOrder;
    } else if (sortBy === 'publishedAt') {
      orderBy.publishedAt = sortOrder;
    } else {
      orderBy.createdAt = sortOrder;
    }
    
    // 执行搜索
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          productType: true,
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.product.count({ where }),
    ]);
    
    // 转换数据格式
    const formattedProducts = products.map(product => ({
      ...product,
      category: product.productTypeId,
      ingredients: parseIngredients(product.ingredients),
      stock: product.stock || 0,
      minStock: product.minStock || 5,
      isActive: product.isActive !== false,
      publishedAt: product.publishedAt?.toISOString() || null,
    }));
    
    return Response.json({
      products: formattedProducts,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('Failed to search products:', error);
    return Response.json({ error: 'Failed to search products' }, { status: 500 });
  }
}
