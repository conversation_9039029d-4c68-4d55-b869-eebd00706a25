// 缓存工具

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class MemoryCache {
  private cache = new Map<string, CacheItem<any>>();
  private maxSize: number;

  constructor(maxSize: number = 100) {
    this.maxSize = maxSize;
  }

  set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): void {
    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  // 清理过期项
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// 全局缓存实例
export const memoryCache = new MemoryCache();

// 定期清理过期缓存
if (typeof window !== 'undefined') {
  setInterval(() => {
    memoryCache.cleanup();
  }, 60000); // 每分钟清理一次
}

// 缓存装饰器函数
export function cached<T extends (...args: any[]) => any>(
  fn: T,
  options: {
    ttl?: number;
    keyGenerator?: (...args: Parameters<T>) => string;
  } = {}
): T {
  const { ttl = 5 * 60 * 1000, keyGenerator } = options;

  return ((...args: Parameters<T>) => {
    const key = keyGenerator 
      ? keyGenerator(...args)
      : `${fn.name}_${JSON.stringify(args)}`;

    // 尝试从缓存获取
    const cached = memoryCache.get(key);
    if (cached !== null) {
      return cached;
    }

    // 执行函数并缓存结果
    const result = fn(...args);
    
    // 如果是Promise，缓存resolved值
    if (result instanceof Promise) {
      return result.then(data => {
        memoryCache.set(key, data, ttl);
        return data;
      });
    } else {
      memoryCache.set(key, result, ttl);
      return result;
    }
  }) as T;
}

// 浏览器存储缓存
export class BrowserCache {
  private storage: Storage;
  private prefix: string;

  constructor(storage: Storage = localStorage, prefix: string = 'app_cache_') {
    this.storage = storage;
    this.prefix = prefix;
  }

  private getKey(key: string): string {
    return `${this.prefix}${key}`;
  }

  set<T>(key: string, data: T, ttl: number = 24 * 60 * 60 * 1000): void {
    try {
      const item: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        ttl,
      };
      this.storage.setItem(this.getKey(key), JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to set cache item:', error);
    }
  }

  get<T>(key: string): T | null {
    try {
      const itemStr = this.storage.getItem(this.getKey(key));
      if (!itemStr) return null;

      const item: CacheItem<T> = JSON.parse(itemStr);
      
      // 检查是否过期
      if (Date.now() - item.timestamp > item.ttl) {
        this.delete(key);
        return null;
      }

      return item.data;
    } catch (error) {
      console.warn('Failed to get cache item:', error);
      return null;
    }
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): void {
    this.storage.removeItem(this.getKey(key));
  }

  clear(): void {
    const keys = Object.keys(this.storage);
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        this.storage.removeItem(key);
      }
    });
  }

  // 清理过期项
  cleanup(): void {
    const keys = Object.keys(this.storage);
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        const originalKey = key.replace(this.prefix, '');
        this.get(originalKey); // 这会自动删除过期项
      }
    });
  }
}

// 全局浏览器缓存实例
export const browserCache = typeof window !== 'undefined' 
  ? new BrowserCache() 
  : null;

// React Hook for cached data
export function useCachedData<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: {
    ttl?: number;
    useMemoryCache?: boolean;
    useBrowserCache?: boolean;
  } = {}
) {
  const {
    ttl = 5 * 60 * 1000,
    useMemoryCache = true,
    useBrowserCache = false,
  } = options;

  // 尝试从缓存获取数据
  const getCachedData = (): T | null => {
    if (useMemoryCache) {
      const memoryData = memoryCache.get<T>(key);
      if (memoryData !== null) return memoryData;
    }

    if (useBrowserCache && browserCache) {
      const browserData = browserCache.get<T>(key);
      if (browserData !== null) {
        // 同时更新内存缓存
        if (useMemoryCache) {
          memoryCache.set(key, browserData, ttl);
        }
        return browserData;
      }
    }

    return null;
  };

  // 设置缓存数据
  const setCachedData = (data: T): void => {
    if (useMemoryCache) {
      memoryCache.set(key, data, ttl);
    }
    if (useBrowserCache && browserCache) {
      browserCache.set(key, data, ttl);
    }
  };

  return {
    getCachedData,
    setCachedData,
    fetcher: async (): Promise<T> => {
      const data = await fetcher();
      setCachedData(data);
      return data;
    },
  };
}
