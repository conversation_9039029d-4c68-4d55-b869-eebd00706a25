{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/lib/errors.ts"], "sourcesContent": ["// 错误处理工具类\n\nexport class AppError extends Error {\n  public readonly statusCode: number;\n  public readonly isOperational: boolean;\n\n  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {\n    super(message);\n    this.statusCode = statusCode;\n    this.isOperational = isOperational;\n\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nexport class ValidationError extends AppError {\n  constructor(message: string) {\n    super(message, 400);\n  }\n}\n\nexport class NotFoundError extends AppError {\n  constructor(resource: string = 'Resource') {\n    super(`${resource} not found`, 404);\n  }\n}\n\nexport class ConflictError extends AppError {\n  constructor(message: string) {\n    super(message, 409);\n  }\n}\n\n// 错误处理中间件\nexport function handleApiError(error: unknown): Response {\n  console.error('API Error:', error);\n\n  if (error instanceof AppError) {\n    return Response.json(\n      { error: error.message },\n      { status: error.statusCode }\n    );\n  }\n\n  if (error instanceof Error) {\n    return Response.json(\n      { error: error.message },\n      { status: 500 }\n    );\n  }\n\n  return Response.json(\n    { error: 'Internal server error' },\n    { status: 500 }\n  );\n}\n\n// 异步错误包装器\nexport function asyncHandler<T extends any[], R>(\n  fn: (...args: T) => Promise<R>\n) {\n  return async (...args: T): Promise<R> => {\n    try {\n      return await fn(...args);\n    } catch (error) {\n      throw error;\n    }\n  };\n}\n\n// 客户端错误处理\nexport function handleClientError(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return '发生未知错误';\n}\n\n// 表单验证错误\nexport interface ValidationErrors {\n  [key: string]: string[];\n}\n\nexport function formatValidationErrors(errors: ValidationErrors): string {\n  const messages: string[] = [];\n  for (const [field, fieldErrors] of Object.entries(errors)) {\n    messages.push(`${field}: ${fieldErrors.join(', ')}`);\n  }\n  return messages.join('; ');\n}\n"], "names": [], "mappings": "AAAA,UAAU;;;;;;;;;;;AAEH,MAAM,iBAAiB;IACZ,WAAmB;IACnB,cAAuB;IAEvC,YAAY,OAAe,EAAE,aAAqB,GAAG,EAAE,gBAAyB,IAAI,CAAE;QACpF,KAAK,CAAC;QACN,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,aAAa,GAAG;QAErB,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;IAChD;AACF;AAEO,MAAM,wBAAwB;IACnC,YAAY,OAAe,CAAE;QAC3B,KAAK,CAAC,SAAS;IACjB;AACF;AAEO,MAAM,sBAAsB;IACjC,YAAY,WAAmB,UAAU,CAAE;QACzC,KAAK,CAAC,GAAG,SAAS,UAAU,CAAC,EAAE;IACjC;AACF;AAEO,MAAM,sBAAsB;IACjC,YAAY,OAAe,CAAE;QAC3B,KAAK,CAAC,SAAS;IACjB;AACF;AAGO,SAAS,eAAe,KAAc;IAC3C,QAAQ,KAAK,CAAC,cAAc;IAE5B,IAAI,iBAAiB,UAAU;QAC7B,OAAO,SAAS,IAAI,CAClB;YAAE,OAAO,MAAM,OAAO;QAAC,GACvB;YAAE,QAAQ,MAAM,UAAU;QAAC;IAE/B;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,SAAS,IAAI,CAClB;YAAE,OAAO,MAAM,OAAO;QAAC,GACvB;YAAE,QAAQ;QAAI;IAElB;IAEA,OAAO,SAAS,IAAI,CAClB;QAAE,OAAO;IAAwB,GACjC;QAAE,QAAQ;IAAI;AAElB;AAGO,SAAS,aACd,EAA8B;IAE9B,OAAO,OAAO,GAAG;QACf,IAAI;YACF,OAAO,MAAM,MAAM;QACrB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;AAGO,SAAS,kBAAkB,KAAc;IAC9C,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,OAAO;AACT;AAOO,SAAS,uBAAuB,MAAwB;IAC7D,MAAM,WAAqB,EAAE;IAC7B,KAAK,MAAM,CAAC,OAAO,YAAY,IAAI,OAAO,OAAO,CAAC,QAAS;QACzD,SAAS,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,YAAY,IAAI,CAAC,OAAO;IACrD;IACA,OAAO,SAAS,IAAI,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/lib/validation.ts"], "sourcesContent": ["// 数据验证工具\n\nimport { z } from 'zod';\nimport { ValidationError } from './errors';\n\n// 基础验证规则\nexport const baseValidation = {\n  id: z.string().min(1, 'ID不能为空'),\n  name: z.string().min(1, '名称不能为空').max(100, '名称不能超过100个字符'),\n  description: z.string().max(500, '描述不能超过500个字符').optional(),\n  price: z.number().positive('价格必须大于0'),\n  phone: z.string().regex(/^1[3-9]\\d{9}$/, '请输入有效的手机号码').optional(),\n  email: z.string().email('请输入有效的邮箱地址').optional(),\n  url: z.string().url('请输入有效的URL').optional(),\n};\n\n// 产品验证模式\nexport const ProductValidationSchema = z.object({\n  id: baseValidation.id.optional(),\n  name: baseValidation.name,\n  description: baseValidation.description,\n  price: baseValidation.price,\n  productTypeId: baseValidation.id,\n  image: baseValidation.url.optional(),\n  ingredients: z.array(z.string()).default([]),\n  spicyLevel: z.number().int().min(0).max(5).default(0),\n  available: z.boolean().default(true),\n  stock: z.number().int().min(0).default(0),\n  minStock: z.number().int().min(0).default(5),\n  isActive: z.boolean().default(true),\n  publishedAt: z.string().datetime().optional().nullable(),\n});\n\n// 产品类型验证模式\nexport const ProductTypeValidationSchema = z.object({\n  id: baseValidation.id.optional(),\n  name: baseValidation.name,\n  description: baseValidation.description,\n  displayOrder: z.number().int().min(0).default(0),\n  isActive: z.boolean().default(true),\n});\n\n// 门店验证模式\nexport const StoreValidationSchema = z.object({\n  id: baseValidation.id.optional(),\n  name: baseValidation.name,\n  address: z.string().min(1, '地址不能为空').max(200, '地址不能超过200个字符'),\n  phone: baseValidation.phone,\n  latitude: z.number().min(-90).max(90).optional().nullable(),\n  longitude: z.number().min(-180).max(180).optional().nullable(),\n  openTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式(HH:MM)').optional(),\n  closeTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式(HH:MM)').optional(),\n  isOpen: z.boolean().default(true),\n  image: baseValidation.url.optional(),\n});\n\n// 搜索参数验证模式\nexport const SearchParamsSchema = z.object({\n  q: z.string().max(100, '搜索关键词不能超过100个字符').optional(),\n  category: z.string().optional(),\n  sortBy: z.enum(['createdAt', 'publishedAt', 'name', 'price']).default('createdAt'),\n  sortOrder: z.enum(['asc', 'desc']).default('desc'),\n  page: z.number().int().min(1).default(1),\n  limit: z.number().int().min(1).max(100).default(12),\n});\n\n// 验证函数\nexport function validateData<T>(schema: z.ZodSchema<T>, data: unknown): T {\n  try {\n    return schema.parse(data);\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      const messages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);\n      throw new ValidationError(messages.join(', '));\n    }\n    throw error;\n  }\n}\n\n// 安全解析（不抛出错误）\nexport function safeValidateData<T>(schema: z.ZodSchema<T>, data: unknown): {\n  success: boolean;\n  data?: T;\n  errors?: string[];\n} {\n  try {\n    const result = schema.safeParse(data);\n    if (result.success) {\n      return { success: true, data: result.data };\n    } else {\n      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`);\n      return { success: false, errors };\n    }\n  } catch (error) {\n    return { success: false, errors: ['验证过程中发生错误'] };\n  }\n}\n\n// 部分验证（用于更新操作）\nexport function validatePartialData<T>(schema: z.ZodSchema<T>, data: unknown): Partial<T> {\n  const partialSchema = schema.partial();\n  return validateData(partialSchema, data);\n}\n\n// 自定义验证规则\nexport const customValidators = {\n  // 验证营业时间\n  validateBusinessHours: (openTime?: string, closeTime?: string) => {\n    if (!openTime || !closeTime) return true;\n    \n    const open = new Date(`2000-01-01 ${openTime}`);\n    const close = new Date(`2000-01-01 ${closeTime}`);\n    \n    return open < close;\n  },\n\n  // 验证库存逻辑\n  validateStock: (stock: number, minStock: number) => {\n    return stock >= 0 && minStock >= 0 && minStock <= stock;\n  },\n\n  // 验证价格范围\n  validatePriceRange: (price: number, min: number = 0, max: number = 10000) => {\n    return price >= min && price <= max;\n  },\n\n  // 验证坐标\n  validateCoordinates: (latitude?: number, longitude?: number) => {\n    if (latitude === undefined || longitude === undefined) return true;\n    return latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180;\n  },\n};\n"], "names": [], "mappings": "AAAA,SAAS;;;;;;;;;;;;AAET;AACA;;;AAGO,MAAM,iBAAiB;IAC5B,IAAI,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACtB,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC3C,aAAa,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,gBAAgB,QAAQ;IACzD,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC3B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,iBAAiB,cAAc,QAAQ;IAC/D,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ;IAC9C,KAAK,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,aAAa,QAAQ;AAC3C;AAGO,MAAM,0BAA0B,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,IAAI,eAAe,EAAE,CAAC,QAAQ;IAC9B,MAAM,eAAe,IAAI;IACzB,aAAa,eAAe,WAAW;IACvC,OAAO,eAAe,KAAK;IAC3B,eAAe,eAAe,EAAE;IAChC,OAAO,eAAe,GAAG,CAAC,QAAQ;IAClC,aAAa,6KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6KAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IAC3C,YAAY,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACnD,WAAW,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACvC,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAC1C,UAAU,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,aAAa,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ;AACxD;AAGO,MAAM,8BAA8B,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClD,IAAI,eAAe,EAAE,CAAC,QAAQ;IAC9B,MAAM,eAAe,IAAI;IACzB,aAAa,eAAe,WAAW;IACvC,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAC9C,UAAU,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAGO,MAAM,wBAAwB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,IAAI,eAAe,EAAE,CAAC,QAAQ;IAC9B,MAAM,eAAe,IAAI;IACzB,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC9C,OAAO,eAAe,KAAK;IAC3B,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,GAAG,QAAQ;IACzD,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,KAAK,QAAQ,GAAG,QAAQ;IAC5D,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,oCAAoC,qBAAqB,QAAQ;IAC5F,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,oCAAoC,qBAAqB,QAAQ;IAC7F,QAAQ,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC5B,OAAO,eAAe,GAAG,CAAC,QAAQ;AACpC;AAGO,MAAM,qBAAqB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,GAAG,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,mBAAmB,QAAQ;IAClD,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,QAAQ,6KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAa;QAAe;QAAQ;KAAQ,EAAE,OAAO,CAAC;IACtE,WAAW,6KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;KAAO,EAAE,OAAO,CAAC;IAC3C,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACtC,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,OAAO,CAAC;AAClD;AAGO,SAAS,aAAgB,MAAsB,EAAE,IAAa;IACnE,IAAI;QACF,OAAO,OAAO,KAAK,CAAC;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,6KAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,MAAM,WAAW,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE;YAChF,MAAM,IAAI,oHAAA,CAAA,kBAAe,CAAC,SAAS,IAAI,CAAC;QAC1C;QACA,MAAM;IACR;AACF;AAGO,SAAS,iBAAoB,MAAsB,EAAE,IAAa;IAKvE,IAAI;QACF,MAAM,SAAS,OAAO,SAAS,CAAC;QAChC,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO;gBAAE,SAAS;gBAAM,MAAM,OAAO,IAAI;YAAC;QAC5C,OAAO;YACL,MAAM,SAAS,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE;YACrF,OAAO;gBAAE,SAAS;gBAAO;YAAO;QAClC;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,SAAS;YAAO,QAAQ;gBAAC;aAAY;QAAC;IACjD;AACF;AAGO,SAAS,oBAAuB,MAAsB,EAAE,IAAa;IAC1E,MAAM,gBAAgB,OAAO,OAAO;IACpC,OAAO,aAAa,eAAe;AACrC;AAGO,MAAM,mBAAmB;IAC9B,SAAS;IACT,uBAAuB,CAAC,UAAmB;QACzC,IAAI,CAAC,YAAY,CAAC,WAAW,OAAO;QAEpC,MAAM,OAAO,IAAI,KAAK,CAAC,WAAW,EAAE,UAAU;QAC9C,MAAM,QAAQ,IAAI,KAAK,CAAC,WAAW,EAAE,WAAW;QAEhD,OAAO,OAAO;IAChB;IAEA,SAAS;IACT,eAAe,CAAC,OAAe;QAC7B,OAAO,SAAS,KAAK,YAAY,KAAK,YAAY;IACpD;IAEA,SAAS;IACT,oBAAoB,CAAC,OAAe,MAAc,CAAC,EAAE,MAAc,KAAK;QACtE,OAAO,SAAS,OAAO,SAAS;IAClC;IAEA,OAAO;IACP,qBAAqB,CAAC,UAAmB;QACvC,IAAI,aAAa,aAAa,cAAc,WAAW,OAAO;QAC9D,OAAO,YAAY,CAAC,MAAM,YAAY,MAAM,aAAa,CAAC,OAAO,aAAa;IAChF;AACF", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/lib/database.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\nimport { z } from 'zod';\nimport { NotFoundError, ValidationError } from './errors';\nimport { validateData } from './validation';\nimport {\n  ProductValidationSchema,\n  ProductTypeValidationSchema,\n  StoreValidationSchema\n} from './validation';\n\n// 全局Prisma客户端实例\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient({\n  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],\n});\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Zod验证模式\nexport const ProductTypeSchema = z.object({\n  id: z.string().min(1),\n  name: z.string().min(1),\n  description: z.string().optional(),\n  displayOrder: z.number().int().default(0),\n  isActive: z.boolean().default(true),\n});\n\nexport const ProductSchema = z.object({\n  id: z.string().min(1),\n  name: z.string().min(1),\n  description: z.string().optional(),\n  price: z.number().positive(),\n  productTypeId: z.string().min(1),\n  image: z.string().optional(),\n  ingredients: z.array(z.string()).optional(),\n  spicyLevel: z.number().int().min(0).max(5).default(0),\n  available: z.boolean().default(true),\n  stock: z.number().int().min(0).default(0),\n  minStock: z.number().int().min(0).default(5),\n  isActive: z.boolean().default(true),\n});\n\nexport const StoreSchema = z.object({\n  id: z.string().min(1),\n  name: z.string().min(1),\n  address: z.string().min(1),\n  phone: z.string().optional(),\n  latitude: z.number().optional(),\n  longitude: z.number().optional(),\n  openTime: z.string().optional(),\n  closeTime: z.string().optional(),\n  isOpen: z.boolean().default(true),\n  image: z.string().optional(),\n});\n\n// 产品类型相关操作\nexport async function getProductTypes() {\n  return await prisma.productType.findMany({\n    where: { isActive: true },\n    orderBy: { displayOrder: 'asc' },\n  });\n}\n\nexport async function getProductTypeById(id: string) {\n  return await prisma.productType.findUnique({\n    where: { id },\n  });\n}\n\nexport async function createProductType(data: z.infer<typeof ProductTypeSchema>) {\n  const validatedData = ProductTypeSchema.parse(data);\n  return await prisma.productType.create({\n    data: validatedData,\n  });\n}\n\nexport async function updateProductType(id: string, data: Partial<z.infer<typeof ProductTypeSchema>>) {\n  return await prisma.productType.update({\n    where: { id },\n    data,\n  });\n}\n\nexport async function deleteProductType(id: string) {\n  // 检查是否有关联的产品\n  const productCount = await prisma.product.count({\n    where: { productTypeId: id },\n  });\n  \n  if (productCount > 0) {\n    throw new Error('无法删除：该产品类型下还有产品');\n  }\n  \n  return await prisma.productType.delete({\n    where: { id },\n  });\n}\n\n// 产品相关操作\nexport async function getProducts() {\n  return await prisma.product.findMany({\n    include: {\n      productType: true,\n    },\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\nexport async function getProductById(id: string) {\n  return await prisma.product.findUnique({\n    where: { id },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function getProductsByCategory(productTypeId: string) {\n  return await prisma.product.findMany({\n    where: { productTypeId },\n    include: {\n      productType: true,\n    },\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\nexport async function createProduct(data: Omit<z.infer<typeof ProductValidationSchema>, 'id'>) {\n  const validatedData = validateData(ProductValidationSchema.omit({ id: true }), data);\n\n  // 检查产品类型是否存在\n  const productType = await prisma.productType.findUnique({\n    where: { id: validatedData.productTypeId },\n  });\n\n  if (!productType) {\n    throw new NotFoundError('产品类型');\n  }\n\n  // 生成新ID\n  const newId = Date.now().toString();\n\n  return await prisma.product.create({\n    data: {\n      ...validatedData,\n      id: newId,\n      ingredients: validatedData.ingredients ? JSON.stringify(validatedData.ingredients) : null,\n      publishedAt: validatedData.isActive ? new Date() : null,\n    },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function updateProduct(id: string, data: Partial<Omit<z.infer<typeof ProductSchema>, 'id'>>) {\n  const updateData: any = { ...data };\n  \n  if (data.ingredients) {\n    updateData.ingredients = JSON.stringify(data.ingredients);\n  }\n  \n  return await prisma.product.update({\n    where: { id },\n    data: updateData,\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function deleteProduct(id: string) {\n  return await prisma.product.delete({\n    where: { id },\n  });\n}\n\n// 门店相关操作\nexport async function getStores() {\n  return await prisma.store.findMany({\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\nexport async function getStoreById(id: string) {\n  return await prisma.store.findUnique({\n    where: { id },\n  });\n}\n\nexport async function createStore(data: Omit<z.infer<typeof StoreSchema>, 'id'>) {\n  const validatedData = StoreSchema.omit({ id: true }).parse(data);\n  \n  // 生成新ID\n  const newId = Date.now().toString();\n  \n  return await prisma.store.create({\n    data: {\n      ...validatedData,\n      id: newId,\n    },\n  });\n}\n\nexport async function updateStore(id: string, data: Partial<Omit<z.infer<typeof StoreSchema>, 'id'>>) {\n  return await prisma.store.update({\n    where: { id },\n    data,\n  });\n}\n\nexport async function deleteStore(id: string) {\n  return await prisma.store.delete({\n    where: { id },\n  });\n}\n\n// 库存管理相关操作\nexport async function updateProductStock(id: string, quantity: number) {\n  const product = await prisma.product.findUnique({ where: { id } });\n  if (!product) throw new Error('产品不存在');\n\n  const newStock = Math.max(0, product.stock + quantity);\n\n  return await prisma.product.update({\n    where: { id },\n    data: {\n      stock: newStock,\n      available: newStock > 0, // 自动更新可用状态\n    },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function toggleProductStatus(id: string) {\n  const product = await prisma.product.findUnique({ where: { id } });\n  if (!product) throw new Error('产品不存在');\n\n  return await prisma.product.update({\n    where: { id },\n    data: { isActive: !product.isActive },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function getLowStockProducts() {\n  return await prisma.product.findMany({\n    where: {\n      OR: [\n        { stock: { lte: prisma.product.fields.minStock } },\n        { stock: 0 },\n      ],\n    },\n    include: {\n      productType: true,\n    },\n    orderBy: { stock: 'asc' },\n  });\n}\n\nexport async function getActiveProducts() {\n  return await prisma.product.findMany({\n    where: {\n      isActive: true,\n      available: true,\n    },\n    include: {\n      productType: true,\n    },\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\n// 工具函数：解析ingredients JSON字符串\nexport function parseIngredients(ingredients: string | null): string[] {\n  if (!ingredients) return [];\n  try {\n    return JSON.parse(ingredients);\n  } catch {\n    return [];\n  }\n}\n\n// 工具函数：获取库存状态\nexport function getStockStatus(stock: number, minStock: number) {\n  if (stock === 0) return { status: 'out_of_stock', label: '缺货', color: 'red' };\n  if (stock <= minStock) return { status: 'low_stock', label: '库存不足', color: 'yellow' };\n  return { status: 'in_stock', label: '库存充足', color: 'green' };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAOA,gBAAgB;AAChB,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY,CAAC;IAC/D,KAAK,uCAAyC;QAAC;QAAS;QAAS;KAAO,GAAG;AAC7E;AAEA,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,MAAM,oBAAoB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,IAAI,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;IACvC,UAAU,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAEO,MAAM,gBAAgB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,eAAe,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC9B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,aAAa,6KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6KAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IACzC,YAAY,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACnD,WAAW,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACvC,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAC1C,UAAU,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAEO,MAAM,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,IAAI,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACxB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,QAAQ,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC5B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AAGO,eAAe;IACpB,OAAO,MAAM,OAAO,WAAW,CAAC,QAAQ,CAAC;QACvC,OAAO;YAAE,UAAU;QAAK;QACxB,SAAS;YAAE,cAAc;QAAM;IACjC;AACF;AAEO,eAAe,mBAAmB,EAAU;IACjD,OAAO,MAAM,OAAO,WAAW,CAAC,UAAU,CAAC;QACzC,OAAO;YAAE;QAAG;IACd;AACF;AAEO,eAAe,kBAAkB,IAAuC;IAC7E,MAAM,gBAAgB,kBAAkB,KAAK,CAAC;IAC9C,OAAO,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;QACrC,MAAM;IACR;AACF;AAEO,eAAe,kBAAkB,EAAU,EAAE,IAAgD;IAClG,OAAO,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;QACrC,OAAO;YAAE;QAAG;QACZ;IACF;AACF;AAEO,eAAe,kBAAkB,EAAU;IAChD,aAAa;IACb,MAAM,eAAe,MAAM,OAAO,OAAO,CAAC,KAAK,CAAC;QAC9C,OAAO;YAAE,eAAe;QAAG;IAC7B;IAEA,IAAI,eAAe,GAAG;QACpB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;QACrC,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe;IACpB,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEO,eAAe,eAAe,EAAU;IAC7C,OAAO,MAAM,OAAO,OAAO,CAAC,UAAU,CAAC;QACrC,OAAO;YAAE;QAAG;QACZ,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,sBAAsB,aAAqB;IAC/D,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,OAAO;YAAE;QAAc;QACvB,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEO,eAAe,cAAc,IAAyD;IAC3F,MAAM,gBAAgB,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,wHAAA,CAAA,0BAAuB,CAAC,IAAI,CAAC;QAAE,IAAI;IAAK,IAAI;IAE/E,aAAa;IACb,MAAM,cAAc,MAAM,OAAO,WAAW,CAAC,UAAU,CAAC;QACtD,OAAO;YAAE,IAAI,cAAc,aAAa;QAAC;IAC3C;IAEA,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,oHAAA,CAAA,gBAAa,CAAC;IAC1B;IAEA,QAAQ;IACR,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;IAEjC,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,MAAM;YACJ,GAAG,aAAa;YAChB,IAAI;YACJ,aAAa,cAAc,WAAW,GAAG,KAAK,SAAS,CAAC,cAAc,WAAW,IAAI;YACrF,aAAa,cAAc,QAAQ,GAAG,IAAI,SAAS;QACrD;QACA,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,cAAc,EAAU,EAAE,IAAwD;IACtG,MAAM,aAAkB;QAAE,GAAG,IAAI;IAAC;IAElC,IAAI,KAAK,WAAW,EAAE;QACpB,WAAW,WAAW,GAAG,KAAK,SAAS,CAAC,KAAK,WAAW;IAC1D;IAEA,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;QACZ,MAAM;QACN,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,cAAc,EAAU;IAC5C,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe;IACpB,OAAO,MAAM,OAAO,KAAK,CAAC,QAAQ,CAAC;QACjC,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEO,eAAe,aAAa,EAAU;IAC3C,OAAO,MAAM,OAAO,KAAK,CAAC,UAAU,CAAC;QACnC,OAAO;YAAE;QAAG;IACd;AACF;AAEO,eAAe,YAAY,IAA6C;IAC7E,MAAM,gBAAgB,YAAY,IAAI,CAAC;QAAE,IAAI;IAAK,GAAG,KAAK,CAAC;IAE3D,QAAQ;IACR,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;IAEjC,OAAO,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;QAC/B,MAAM;YACJ,GAAG,aAAa;YAChB,IAAI;QACN;IACF;AACF;AAEO,eAAe,YAAY,EAAU,EAAE,IAAsD;IAClG,OAAO,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;QAC/B,OAAO;YAAE;QAAG;QACZ;IACF;AACF;AAEO,eAAe,YAAY,EAAU;IAC1C,OAAO,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;QAC/B,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe,mBAAmB,EAAU,EAAE,QAAgB;IACnE,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC,UAAU,CAAC;QAAE,OAAO;YAAE;QAAG;IAAE;IAChE,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,QAAQ,KAAK,GAAG;IAE7C,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;QACZ,MAAM;YACJ,OAAO;YACP,WAAW,WAAW;QACxB;QACA,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,oBAAoB,EAAU;IAClD,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC,UAAU,CAAC;QAAE,OAAO;YAAE;QAAG;IAAE;IAChE,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;QACZ,MAAM;YAAE,UAAU,CAAC,QAAQ,QAAQ;QAAC;QACpC,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe;IACpB,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,OAAO;YACL,IAAI;gBACF;oBAAE,OAAO;wBAAE,KAAK,OAAO,OAAO,CAAC,MAAM,CAAC,QAAQ;oBAAC;gBAAE;gBACjD;oBAAE,OAAO;gBAAE;aACZ;QACH;QACA,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,OAAO;QAAM;IAC1B;AACF;AAEO,eAAe;IACpB,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,OAAO;YACL,UAAU;YACV,WAAW;QACb;QACA,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAGO,SAAS,iBAAiB,WAA0B;IACzD,IAAI,CAAC,aAAa,OAAO,EAAE;IAC3B,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO,EAAE;IACX;AACF;AAGO,SAAS,eAAe,KAAa,EAAE,QAAgB;IAC5D,IAAI,UAAU,GAAG,OAAO;QAAE,QAAQ;QAAgB,OAAO;QAAM,OAAO;IAAM;IAC5E,IAAI,SAAS,UAAU,OAAO;QAAE,QAAQ;QAAa,OAAO;QAAQ,OAAO;IAAS;IACpF,OAAO;QAAE,QAAQ;QAAY,OAAO;QAAQ,OAAO;IAAQ;AAC7D", "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/ProductCard.tsx"], "sourcesContent": ["import { Product } from '@/types';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nexport default function ProductCard({ product }: ProductCardProps) {\n  const spicyIcons = '🌶️'.repeat(product.spicyLevel);\n\n  // 获取类别显示文本，优先使用productType名称\n  const getCategoryText = () => {\n    if ('productType' in product && product.productType && typeof product.productType === 'object' && 'name' in product.productType) {\n      return (product.productType as any).name;\n    }\n\n    // 回退到硬编码的类别映射\n    const categoryText: Record<string, string> = {\n      'fresh-noodles': '手工鲜面条',\n      'dumpling-wrappers': '饺子皮',\n      'noodle-sheets': '面皮',\n      'shaobing': '烧饼',\n      'wonton-wrappers': '馄饨皮',\n      'steamed-buns': '馒头花卷',\n      noodles: '面条',\n      pasta: '面片',\n      soup: '汤面'\n    };\n\n    return categoryText[product.category] || product.category || '未分类';\n  };\n\n  // 获取库存状态\n  const getStockStatus = () => {\n    const stock = (product as any).stock || 0;\n    const minStock = (product as any).minStock || 5;\n\n    if (stock === 0) return { status: 'out_of_stock', label: '缺货', color: 'bg-red-100 text-red-800' };\n    if (stock <= minStock) return { status: 'low_stock', label: '库存不足', color: 'bg-yellow-100 text-yellow-800' };\n    return { status: 'in_stock', label: '库存充足', color: 'bg-green-100 text-green-800' };\n  };\n\n  const stockStatus = getStockStatus();\n  const isActive = (product as any).isActive !== false;\n\n  return (\n    <div className={`bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow ${!isActive ? 'opacity-60' : ''}`}>\n      <div className=\"relative h-48\">\n        <Image\n          src={product.image}\n          alt={product.name}\n          fill\n          className=\"object-cover\"\n        />\n        <div className=\"absolute top-2 left-2 flex flex-col gap-1\">\n          {spicyIcons && (\n            <span className=\"bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs\">\n              {spicyIcons}\n            </span>\n          )}\n          <span className={`px-2 py-1 rounded-full text-xs font-medium ${stockStatus.color}`}>\n            {stockStatus.label}\n          </span>\n        </div>\n        <div className=\"absolute top-2 right-2 flex flex-col gap-1\">\n          <span className=\"bg-orange-500 text-white px-2 py-1 rounded-full text-xs\">\n            {getCategoryText()}\n          </span>\n          {!isActive && (\n            <span className=\"bg-gray-500 text-white px-2 py-1 rounded-full text-xs\">\n              已下架\n            </span>\n          )}\n        </div>\n      </div>\n      <div className=\"p-4\">\n        <h3 className=\"text-xl font-semibold text-gray-800 mb-2\">{product.name}</h3>\n        <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">{product.description}</p>\n        \n        <div className=\"flex items-center justify-between mb-3\">\n          <span className=\"text-2xl font-bold text-orange-600\">¥{product.price}</span>\n          <div className=\"flex items-center space-x-1\">\n            <span className=\"text-gray-500 text-sm\">辣度:</span>\n            <span className=\"text-sm\">{spicyIcons || '不辣'}</span>\n          </div>\n        </div>\n\n        <div className=\"flex items-center justify-between mb-3\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-gray-500 text-sm\">库存:</span>\n            <span className={`text-sm font-medium ${\n              (product as any).stock === 0 ? 'text-red-600' :\n              (product as any).stock <= (product as any).minStock ? 'text-yellow-600' : 'text-green-600'\n            }`}>\n              {(product as any).stock || 0} 件\n            </span>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            <span className={`px-2 py-1 rounded-full text-xs ${\n              product.available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n            }`}>\n              {product.available ? '有货' : '缺货'}\n            </span>\n          </div>\n        </div>\n\n        {/* 发布日期 */}\n        {(product as any).publishedAt && (\n          <div className=\"flex items-center justify-between mb-3 text-xs text-gray-500\">\n            <span>发布时间:</span>\n            <span>{new Date((product as any).publishedAt).toLocaleDateString('zh-CN')}</span>\n          </div>\n        )}\n\n        <div className=\"mb-3\">\n          <div className=\"flex flex-wrap gap-1\">\n            {product.ingredients.slice(0, 3).map((ingredient, index) => (\n              <span\n                key={index}\n                className=\"bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs\"\n              >\n                {ingredient}\n              </span>\n            ))}\n            {product.ingredients.length > 3 && (\n              <span className=\"text-gray-500 text-xs\">...</span>\n            )}\n          </div>\n        </div>\n\n        <div className=\"flex justify-between items-center\">\n          <Link\n            href={`/products/${product.id}`}\n            className=\"text-orange-500 hover:text-orange-600 text-sm font-medium\"\n          >\n            查看详情 →\n          </Link>\n          <span className={`text-sm ${product.available ? 'text-green-600' : 'text-red-600'}`}>\n            {product.available ? '有货' : '暂缺'}\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAMe,SAAS,YAAY,EAAE,OAAO,EAAoB;IAC/D,MAAM,aAAa,MAAM,MAAM,CAAC,QAAQ,UAAU;IAElD,6BAA6B;IAC7B,MAAM,kBAAkB;QACtB,IAAI,iBAAiB,WAAW,QAAQ,WAAW,IAAI,OAAO,QAAQ,WAAW,KAAK,YAAY,UAAU,QAAQ,WAAW,EAAE;YAC/H,OAAO,AAAC,QAAQ,WAAW,CAAS,IAAI;QAC1C;QAEA,cAAc;QACd,MAAM,eAAuC;YAC3C,iBAAiB;YACjB,qBAAqB;YACrB,iBAAiB;YACjB,YAAY;YACZ,mBAAmB;YACnB,gBAAgB;YAChB,SAAS;YACT,OAAO;YACP,MAAM;QACR;QAEA,OAAO,YAAY,CAAC,QAAQ,QAAQ,CAAC,IAAI,QAAQ,QAAQ,IAAI;IAC/D;IAEA,SAAS;IACT,MAAM,iBAAiB;QACrB,MAAM,QAAQ,AAAC,QAAgB,KAAK,IAAI;QACxC,MAAM,WAAW,AAAC,QAAgB,QAAQ,IAAI;QAE9C,IAAI,UAAU,GAAG,OAAO;YAAE,QAAQ;YAAgB,OAAO;YAAM,OAAO;QAA0B;QAChG,IAAI,SAAS,UAAU,OAAO;YAAE,QAAQ;YAAa,OAAO;YAAQ,OAAO;QAAgC;QAC3G,OAAO;YAAE,QAAQ;YAAY,OAAO;YAAQ,OAAO;QAA8B;IACnF;IAEA,MAAM,cAAc;IACpB,MAAM,WAAW,AAAC,QAAgB,QAAQ,KAAK;IAE/C,qBACE,8OAAC;QAAI,WAAW,CAAC,gFAAgF,EAAE,CAAC,WAAW,eAAe,IAAI;;0BAChI,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,KAAK;wBAClB,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;4BACZ,4BACC,8OAAC;gCAAK,WAAU;0CACb;;;;;;0CAGL,8OAAC;gCAAK,WAAW,CAAC,2CAA2C,EAAE,YAAY,KAAK,EAAE;0CAC/E,YAAY,KAAK;;;;;;;;;;;;kCAGtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb;;;;;;4BAEF,CAAC,0BACA,8OAAC;gCAAK,WAAU;0CAAwD;;;;;;;;;;;;;;;;;;0BAM9E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4C,QAAQ,IAAI;;;;;;kCACtE,8OAAC;wBAAE,WAAU;kCAA2C,QAAQ,WAAW;;;;;;kCAE3E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;;oCAAqC;oCAAE,QAAQ,KAAK;;;;;;;0CACpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;kDAAW,cAAc;;;;;;;;;;;;;;;;;;kCAI7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAW,CAAC,oBAAoB,EACpC,AAAC,QAAgB,KAAK,KAAK,IAAI,iBAC/B,AAAC,QAAgB,KAAK,IAAI,AAAC,QAAgB,QAAQ,GAAG,oBAAoB,kBAC1E;;4CACE,QAAgB,KAAK,IAAI;4CAAE;;;;;;;;;;;;;0CAGjC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAW,CAAC,+BAA+B,EAC/C,QAAQ,SAAS,GAAG,gCAAgC,2BACpD;8CACC,QAAQ,SAAS,GAAG,OAAO;;;;;;;;;;;;;;;;;oBAMhC,QAAgB,WAAW,kBAC3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAM,IAAI,KAAK,AAAC,QAAgB,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;kCAIrE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,QAAQ,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,YAAY,sBAChD,8OAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;gCAMR,QAAQ,WAAW,CAAC,MAAM,GAAG,mBAC5B,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAK9C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gCAC/B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAK,WAAW,CAAC,QAAQ,EAAE,QAAQ,SAAS,GAAG,mBAAmB,gBAAgB;0CAChF,QAAQ,SAAS,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 950, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/WeatherWidget.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/WeatherWidget.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/WeatherWidget.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/WeatherWidget.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/WeatherWidget.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/WeatherWidget.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 982, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/StoreLocator.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StoreLocator.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StoreLocator.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/StoreLocator.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StoreLocator.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StoreLocator.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1014, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/page.tsx"], "sourcesContent": ["import { getProducts, getStores, parseIngredients } from '@/lib/database';\nimport ProductCard from '@/components/ProductCard';\nimport WeatherWidget from '@/components/WeatherWidget';\nimport StoreLocator from '@/components/StoreLocator';\nimport Link from 'next/link';\n\nexport default async function Home() {\n  const rawProducts = await getProducts();\n  const rawStores = await getStores();\n\n  // 转换产品数据格式以保持兼容性，只显示上架的产品\n  const products = rawProducts\n    .filter(product => (product as any).isActive !== false) // 只显示上架的产品\n    .map(product => ({\n      ...product,\n      category: product.productTypeId,\n      description: product.description || '',\n      ingredients: parseIngredients(product.ingredients),\n      image: product.image || '',\n      stock: (product as any).stock || 0,\n      minStock: (product as any).minStock || 5,\n      isActive: (product as any).isActive !== false,\n      publishedAt: (product as any).publishedAt?.toISOString() || null,\n      createdAt: product.createdAt.toISOString(),\n      updatedAt: product.updatedAt.toISOString(),\n    }));\n\n  // 转换门店数据格式以保持兼容性\n  const stores = rawStores.map(store => ({\n    ...store,\n    phone: store.phone || '',\n    latitude: store.latitude || 0,\n    longitude: store.longitude || 0,\n    openTime: store.openTime || '',\n    closeTime: store.closeTime || '',\n    image: store.image || '',\n    createdAt: store.createdAt.toISOString(),\n    updatedAt: store.updatedAt.toISOString(),\n  }));\n\n  const featuredProducts = products.slice(0, 6);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-b from-orange-50 to-white\">\n      <header className=\"bg-white shadow-lg\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <div className=\"flex items-center space-x-4\">\n              <h1 className=\"text-3xl font-bold text-orange-600\">香香手工面食店</h1>\n              <span className=\"text-gray-600\">纯手工制作 · 新鲜现做</span>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <WeatherWidget />\n              <Link\n                href=\"/admin/login\"\n                className=\"bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors text-sm font-medium\"\n              >\n                管理登录\n              </Link>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"container mx-auto px-4 py-8\">\n        <section className=\"mb-12 text-center\">\n          <h2 className=\"text-4xl font-bold text-gray-800 mb-4\">传统手工 · 品质保证</h2>\n          <p className=\"text-xl text-gray-600 mb-6\">专注手工面食制作，为您提供最新鲜的产品</p>\n\n          {/* 搜索入口 */}\n          <div className=\"max-w-md mx-auto mb-8\">\n            <Link\n              href=\"/products\"\n              className=\"flex items-center justify-center w-full bg-orange-500 text-white py-3 px-6 rounded-lg hover:bg-orange-600 transition-colors text-lg font-medium\"\n            >\n              🔍 搜索产品\n            </Link>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 text-center\">\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <div className=\"text-orange-500 text-4xl mb-4\">👐</div>\n              <h3 className=\"text-xl font-semibold mb-2\">纯手工制作</h3>\n              <p className=\"text-gray-600\">拒绝机器生产，坚持传统手工工艺</p>\n            </div>\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <div className=\"text-orange-500 text-4xl mb-4\">🌾</div>\n              <h3 className=\"text-xl font-semibold mb-2\">优质原料</h3>\n              <p className=\"text-gray-600\">精选高筋面粉，不添加任何防腐剂</p>\n            </div>\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <div className=\"text-orange-500 text-4xl mb-4\">🕐</div>\n              <h3 className=\"text-xl font-semibold mb-2\">新鲜现做</h3>\n              <p className=\"text-gray-600\">当日制作当日售，保证产品新鲜度</p>\n            </div>\n          </div>\n        </section>\n\n        <section className=\"mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-800 mb-8 text-center\">热销产品</h2>\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {featuredProducts.map((product) => (\n              <ProductCard key={product.id} product={product} />\n            ))}\n          </div>\n          <div className=\"text-center mt-8\">\n            <Link\n              href=\"/products\"\n              className=\"inline-block bg-orange-500 text-white px-8 py-3 rounded-lg hover:bg-orange-600 transition-colors text-lg font-semibold\"\n            >\n              查看全部产品\n            </Link>\n          </div>\n        </section>\n\n        <section className=\"mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-800 mb-8 text-center\">门店位置</h2>\n          <StoreLocator stores={stores} />\n          <div className=\"text-center mt-6\">\n            <Link\n              href=\"/stores\"\n              className=\"inline-block bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors\"\n            >\n              查看所有门店地图\n            </Link>\n          </div>\n        </section>\n      </main>\n\n      <footer className=\"bg-gray-800 text-white py-8\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div>\n              <h3 className=\"text-xl font-bold mb-4\">香香手工面食店</h3>\n              <p className=\"text-gray-300\">传承手工技艺，品味家的温暖</p>\n              <p className=\"text-gray-300 mt-2\">主营：手工面条、饺子皮、馄饨皮、烧饼等</p>\n            </div>\n            <div>\n              <h3 className=\"text-xl font-bold mb-4\">联系我们</h3>\n              <p className=\"text-gray-300\">服务热线：400-888-8888</p>\n              <p className=\"text-gray-300\">营业时间：6:00-20:00</p>\n              <p className=\"text-gray-300\">支持批发零售，欢迎来电咨询</p>\n            </div>\n            <div>\n              <h3 className=\"text-xl font-bold mb-4\">关注我们</h3>\n              <p className=\"text-gray-300\">微信公众号：香香手工面食</p>\n              <p className=\"text-gray-300\">添加微信，了解每日新品</p>\n            </div>\n          </div>\n          <div className=\"text-center mt-8 pt-8 border-t border-gray-700\">\n            <p className=\"text-gray-300\">&copy; 2024 香香手工面食店. 保留所有权利.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEe,eAAe;IAC5B,MAAM,cAAc,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IACpC,MAAM,YAAY,MAAM,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD;IAEhC,0BAA0B;IAC1B,MAAM,WAAW,YACd,MAAM,CAAC,CAAA,UAAW,AAAC,QAAgB,QAAQ,KAAK,OAAO,WAAW;KAClE,GAAG,CAAC,CAAA,UAAW,CAAC;YACf,GAAG,OAAO;YACV,UAAU,QAAQ,aAAa;YAC/B,aAAa,QAAQ,WAAW,IAAI;YACpC,aAAa,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,WAAW;YACjD,OAAO,QAAQ,KAAK,IAAI;YACxB,OAAO,AAAC,QAAgB,KAAK,IAAI;YACjC,UAAU,AAAC,QAAgB,QAAQ,IAAI;YACvC,UAAU,AAAC,QAAgB,QAAQ,KAAK;YACxC,aAAa,AAAC,QAAgB,WAAW,EAAE,iBAAiB;YAC5D,WAAW,QAAQ,SAAS,CAAC,WAAW;YACxC,WAAW,QAAQ,SAAS,CAAC,WAAW;QAC1C,CAAC;IAEH,iBAAiB;IACjB,MAAM,SAAS,UAAU,GAAG,CAAC,CAAA,QAAS,CAAC;YACrC,GAAG,KAAK;YACR,OAAO,MAAM,KAAK,IAAI;YACtB,UAAU,MAAM,QAAQ,IAAI;YAC5B,WAAW,MAAM,SAAS,IAAI;YAC9B,UAAU,MAAM,QAAQ,IAAI;YAC5B,WAAW,MAAM,SAAS,IAAI;YAC9B,OAAO,MAAM,KAAK,IAAI;YACtB,WAAW,MAAM,SAAS,CAAC,WAAW;YACtC,WAAW,MAAM,SAAS,CAAC,WAAW;QACxC,CAAC;IAED,MAAM,mBAAmB,SAAS,KAAK,CAAC,GAAG;IAE3C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,mIAAA,CAAA,UAAa;;;;;kDACd,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;0CAIH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;0DAC/C,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;0DAC/C,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;0DAC/C,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAKnC,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,8OAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,iIAAA,CAAA,UAAW;wCAAkB,SAAS;uCAArB,QAAQ,EAAE;;;;;;;;;;0CAGhC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAML,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,8OAAC,kIAAA,CAAA,UAAY;gCAAC,QAAQ;;;;;;0CACtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAOP,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAEpC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAGjC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}]}