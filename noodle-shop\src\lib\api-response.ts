// 统一的API响应工具

import { NextResponse } from 'next/server';
import { ApiResponse } from '@/types';

export class ApiResponseBuilder {
  static success<T>(data: T, message?: string): Response {
    return NextResponse.json({
      data,
      message: message || 'Success',
    } as ApiResponse<T>);
  }

  static error(message: string, status: number = 500): Response {
    return NextResponse.json({
      error: message,
    } as ApiResponse<never>, { status });
  }

  static notFound(resource: string = 'Resource'): Response {
    return this.error(`${resource} not found`, 404);
  }

  static badRequest(message: string = 'Bad request'): Response {
    return this.error(message, 400);
  }

  static unauthorized(message: string = 'Unauthorized'): Response {
    return this.error(message, 401);
  }

  static forbidden(message: string = 'Forbidden'): Response {
    return this.error(message, 403);
  }

  static conflict(message: string = 'Conflict'): Response {
    return this.error(message, 409);
  }

  static created<T>(data: T, message?: string): Response {
    return NextResponse.json({
      data,
      message: message || 'Created successfully',
    } as ApiResponse<T>, { status: 201 });
  }

  static noContent(): Response {
    return new Response(null, { status: 204 });
  }
}

// 分页响应构建器
export function buildPaginatedResponse<T>(
  items: T[],
  page: number,
  limit: number,
  total: number
) {
  const totalPages = Math.ceil(total / limit);
  
  return {
    products: items,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

// HTTP状态码常量
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
} as const;
