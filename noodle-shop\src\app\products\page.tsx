'use client';

import { useState, useEffect } from 'react';
import ProductCard from '@/components/ProductCard';
import ProductSearch from '@/components/ProductSearch';
import WeatherWidget from '@/components/WeatherWidget';
import ClientOnly from '@/components/ClientOnly';
import Link from 'next/link';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image: string;
  ingredients: string[];
  spicyLevel: number;
  available: boolean;
  stock: number;
  minStock: number;
  isActive: boolean;
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
  productType: {
    name: string;
  };
}

interface ProductType {
  id: string;
  name: string;
}

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [productTypes, setProductTypes] = useState<ProductType[]>([]);
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProducts();
    fetchProductTypes();
  }, []);

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/products');
      const data = await response.json();
      setProducts(data);
    } catch (error) {
      console.error('Failed to fetch products:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchProductTypes = async () => {
    try {
      const response = await fetch('/api/product-types');
      const data = await response.json();
      setProductTypes(data);
    } catch (error) {
      console.error('Failed to fetch product types:', error);
    }
  };

  const handleSearch = (results: any) => {
    console.log('Search results:', results); // 调试日志
    if (results && results.products) {
      setSearchResults(results.products);
      setIsSearching(true);
    } else {
      setSearchResults([]);
      setIsSearching(false);
    }
  };

  const displayProducts = isSearching ? searchResults : products;

  if (loading) {
    return (
      <ClientOnly fallback={<div>加载中...</div>}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载中...</p>
          </div>
        </div>
      </ClientOnly>
    );
  }

  return (
    <ClientOnly fallback={<div>加载中...</div>}>
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm">
          <div className="container mx-auto px-4 py-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <Link href="/" className="text-2xl font-bold text-orange-600 hover:text-orange-700">
                  香香面条店
                </Link>
                <span className="text-gray-600">菜品列表</span>
              </div>
              <WeatherWidget />
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-4">所有菜品</h1>
            <p className="text-gray-600">精选传统面食，每一道都是用心之作</p>
          </div>

          {/* 搜索组件 */}
          <ProductSearch
            categories={productTypes}
            onSearch={handleSearch}
          />

          {/* 搜索结果提示 */}
          {isSearching && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-blue-800">
                找到 {searchResults.length} 个搜索结果
                <button
                  onClick={() => setIsSearching(false)}
                  className="ml-4 text-blue-600 hover:text-blue-800 underline"
                >
                  查看全部产品
                </button>
              </p>
            </div>
          )}

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {displayProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>

          {displayProducts.length === 0 && !loading && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🍜</div>
              <h2 className="text-xl text-gray-600">
                {isSearching ? '没有找到匹配的产品' : '暂无菜品'}
              </h2>
              {isSearching && (
                <p className="text-gray-500 mt-2">
                  请尝试调整搜索条件或
                  <button
                    onClick={() => setIsSearching(false)}
                    className="text-orange-600 hover:text-orange-800 underline ml-1"
                  >
                    查看全部产品
                  </button>
                </p>
              )}
            </div>
          )}
        </main>
      </div>
    </ClientOnly>
  );
}