import { getProducts } from '@/lib/data';
import ProductCard from '@/components/ProductCard';
import WeatherWidget from '@/components/WeatherWidget';
import Link from 'next/link';

export default async function ProductsPage() {
  const products = await getProducts();
  const categories = {
    all: '全部',
    noodles: '面条',
    pasta: '面片',
    soup: '汤面'
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-2xl font-bold text-orange-600 hover:text-orange-700">
                香香面条店
              </Link>
              <span className="text-gray-600">菜品列表</span>
            </div>
            <WeatherWidget />
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">所有菜品</h1>
          <p className="text-gray-600">精选传统面食，每一道都是用心之作</p>
        </div>

        <div className="mb-6">
          <div className="flex flex-wrap gap-4">
            {Object.entries(categories).map(([key, label]) => (
              <button
                key={key}
                className="px-4 py-2 bg-white border border-gray-200 rounded-lg hover:border-orange-500 hover:text-orange-600 transition-colors"
              >
                {label}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {products.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>

        {products.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🍜</div>
            <h2 className="text-xl text-gray-600">暂无菜品</h2>
          </div>
        )}
      </main>
    </div>
  );
}