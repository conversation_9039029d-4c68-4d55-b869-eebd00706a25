const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('开始填充数据库...');

  // 清空现有数据
  await prisma.product.deleteMany();
  await prisma.productType.deleteMany();
  await prisma.store.deleteMany();

  // 创建产品类型
  const productTypes = [
    {
      id: 'fresh-noodles',
      name: '手工鲜面条',
      description: '纯手工制作的各式新鲜面条，口感劲道，营养丰富',
      displayOrder: 1,
      isActive: true,
    },
    {
      id: 'dumpling-wrappers',
      name: '饺子皮',
      description: '手工擀制的饺子皮，薄厚均匀，包饺子不破皮',
      displayOrder: 2,
      isActive: true,
    },
    {
      id: 'noodle-sheets',
      name: '面皮',
      description: '各种手工面皮，可用于制作凉皮、面片等美食',
      displayOrder: 3,
      isActive: true,
    },
    {
      id: 'shaobing',
      name: '烧饼',
      description: '传统手工烧饼，外酥内软，香味扑鼻',
      displayOrder: 4,
      isActive: true,
    },
    {
      id: 'wonton-wrappers',
      name: '馄饨皮',
      description: '薄如纸的馄饨皮，包馄饨的最佳选择',
      displayOrder: 5,
      isActive: true,
    },
    {
      id: 'steamed-buns',
      name: '馒头花卷',
      description: '手工发面制作的各式馒头花卷',
      displayOrder: 6,
      isActive: true,
    },
  ];

  for (const type of productTypes) {
    await prisma.productType.create({ data: type });
  }

  console.log('产品类型创建完成');

  // 创建产品
  const products = [
    {
      id: '1',
      name: '手工拉面',
      description: '纯手工拉制，面条劲道有弹性，可选粗细',
      price: 12,
      productTypeId: 'fresh-noodles',
      image: '/images/lanzhou-beef-noodles.svg',
      ingredients: JSON.stringify(['高筋面粉', '食用盐', '碱水']),
      spicyLevel: 0,
      available: true,
      stock: 50,
      minStock: 10,
      isActive: true,
      publishedAt: new Date('2024-01-15'),
    },
    {
      id: '2',
      name: '手工刀削面',
      description: '传统刀削工艺，面片厚薄均匀，口感独特',
      price: 15,
      productTypeId: 'fresh-noodles',
      image: '/images/dao-xiao-noodles.svg',
      ingredients: JSON.stringify(['高筋面粉', '鸡蛋', '食用盐']),
      spicyLevel: 0,
      available: true,
      stock: 30,
      minStock: 8,
      isActive: true,
      publishedAt: new Date('2024-01-20'),
    },
    {
      id: '3',
      name: '手工饺子皮',
      description: '现擀现卖，薄厚适中，包饺子不破皮，每斤约80张',
      price: 18,
      productTypeId: 'dumpling-wrappers',
      image: '/images/sour-soup-pasta.svg',
      ingredients: JSON.stringify(['中筋面粉', '温水', '食用盐']),
      spicyLevel: 0,
      available: true,
      stock: 25,
      minStock: 5,
      isActive: true,
      publishedAt: new Date('2024-02-01'),
    },
    {
      id: '4',
      name: '手工宽面条',
      description: '宽度2-3厘米，适合炒制或凉拌，口感爽滑',
      price: 13,
      productTypeId: 'fresh-noodles',
      image: '/images/oil-splashed-noodles.svg',
      ingredients: JSON.stringify(['高筋面粉', '鸡蛋', '食用油']),
      spicyLevel: 0,
      available: true,
      stock: 40,
      minStock: 10,
      isActive: true,
      publishedAt: new Date('2024-02-05'),
    },
    {
      id: '5',
      name: '传统烧饼',
      description: '老面发酵，芝麻香酥，外脆内软，可夹肉夹菜',
      price: 5,
      productTypeId: 'shaobing',
      image: '/images/mutton-pita-bread-soup.svg',
      ingredients: JSON.stringify(['面粉', '老面', '芝麻', '食用油']),
      spicyLevel: 0,
      available: true,
      stock: 60,
      minStock: 15,
      isActive: true,
      publishedAt: new Date('2024-02-10'),
    },
    {
      id: '6',
      name: '手工细面条',
      description: '细如发丝，适合做汤面，易消化',
      price: 14,
      productTypeId: 'fresh-noodles',
      image: '/images/saozi-noodles.svg',
      ingredients: JSON.stringify(['高筋面粉', '食用盐', '鸡蛋清']),
      spicyLevel: 0,
      available: true,
      stock: 35,
      minStock: 8,
      isActive: true,
      publishedAt: new Date('2024-02-15'),
    },
    {
      id: '7',
      name: '手工馄饨皮',
      description: '薄如纸，透光可见，每斤约120张',
      price: 20,
      productTypeId: 'wonton-wrappers',
      image: '/images/sour-soup-pasta.svg',
      ingredients: JSON.stringify(['中筋面粉', '淀粉', '鸡蛋']),
      spicyLevel: 0,
      available: true,
      stock: 20,
      minStock: 5,
      isActive: true,
      publishedAt: new Date('2024-02-20'),
    },
    {
      id: '8',
      name: '手工面片',
      description: '可做烩面片、炒面片，厚度适中，不易烂',
      price: 12,
      productTypeId: 'noodle-sheets',
      image: '/images/dao-xiao-noodles.svg',
      ingredients: JSON.stringify(['中筋面粉', '食用盐', '温水']),
      spicyLevel: 0,
      available: true,
      stock: 45,
      minStock: 10,
      isActive: true,
      publishedAt: new Date('2024-02-25'),
    },
    {
      id: '9',
      name: '花卷馒头',
      description: '手工发面，松软香甜，有原味、葱花、糖等口味',
      price: 3,
      productTypeId: 'steamed-buns',
      image: '/images/mutton-pita-bread-soup.svg',
      ingredients: JSON.stringify(['面粉', '酵母', '白糖', '葱花']),
      spicyLevel: 0,
      available: true,
      stock: 80,
      minStock: 20,
      isActive: true,
      publishedAt: new Date('2024-03-01'),
    },
    {
      id: '10',
      name: '手工凉皮',
      description: '爽滑筋道，可凉拌可热炒，夏季热销',
      price: 10,
      productTypeId: 'noodle-sheets',
      image: '/images/oil-splashed-noodles.svg',
      ingredients: JSON.stringify(['面粉', '淀粉', '食用油']),
      spicyLevel: 0,
      available: false, // 设置为缺货状态
      stock: 2,
      minStock: 5,
      isActive: false, // 设置为下架状态
      publishedAt: new Date('2024-03-05'),
    },
  ];

  for (const product of products) {
    await prisma.product.create({ data: product });
  }

  console.log('产品创建完成');

  // 创建门店
  const stores = [
    {
      id: '1',
      name: '香香手工面食店总店',
      address: '北京市朝阳区建国路88号',
      phone: '010-88888888',
      latitude: 39.9042,
      longitude: 116.4074,
      openTime: '06:00',
      closeTime: '20:00',
      isOpen: true,
    },
    {
      id: '2',
      name: '香香手工面食店朝阳分店',
      address: '北京市朝阳区望京西路66号',
      phone: '010-66666666',
      latitude: 39.9961,
      longitude: 116.4761,
      openTime: '06:00',
      closeTime: '19:30',
      isOpen: true,
    },
    {
      id: '3',
      name: '香香手工面食店海淀分店',
      address: '北京市海淀区中关村大街100号',
      phone: '010-55555555',
      latitude: 39.9861,
      longitude: 116.3061,
      openTime: '06:30',
      closeTime: '20:00',
      isOpen: true,
    },
  ];

  for (const store of stores) {
    await prisma.store.create({ data: store });
  }

  console.log('门店创建完成');
  console.log('数据库填充完成！');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
