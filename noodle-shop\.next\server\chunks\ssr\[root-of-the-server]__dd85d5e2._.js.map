{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/admin/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\n\ninterface Product {\n  id: string;\n  name: string;\n  available: boolean;\n}\n\ninterface Store {\n  id: string;\n  name: string;\n}\n\nexport default function AdminDashboard() {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [stores, setStores] = useState<Store[]>([]);\n  const [loading, setLoading] = useState(true);\n  const router = useRouter();\n\n  useEffect(() => {\n    // 检查身份验证\n    const isAuth = localStorage.getItem('adminAuth');\n    if (!isAuth) {\n      router.push('/admin/login');\n      return;\n    }\n\n    // 加载数据 - 直接导入静态数据\n    const loadData = async () => {\n      try {\n        // 直接导入静态数据文件\n        const productsModule = await import('@/data/products.json');\n        const storesModule = await import('@/data/stores.json');\n        \n        setProducts(productsModule.default);\n        setStores(storesModule.default);\n      } catch (error) {\n        console.error('加载数据失败:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, [router]);\n\n  const handleLogout = () => {\n    localStorage.removeItem('adminAuth');\n    router.push('/admin/login');\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-4xl mb-4\">🍜</div>\n          <div className=\"text-lg text-gray-600\">加载中...</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4 flex justify-between items-center\">\n          <h1 className=\"text-2xl font-bold text-orange-600\">管理后台</h1>\n          <button\n            onClick={handleLogout}\n            className=\"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors\"\n          >\n            退出登录\n          </button>\n        </div>\n      </header>\n\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n          <div className=\"bg-white p-6 rounded-lg shadow-md\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-800\">菜品总数</h3>\n                <p className=\"text-3xl font-bold text-orange-600\">{products.length}</p>\n              </div>\n              <div className=\"text-4xl text-orange-500\">🍜</div>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-md\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-800\">门店数量</h3>\n                <p className=\"text-3xl font-bold text-green-600\">{stores.length}</p>\n              </div>\n              <div className=\"text-4xl text-green-500\">🏪</div>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-md\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-800\">在售菜品</h3>\n                <p className=\"text-3xl font-bold text-blue-600\">\n                  {products.filter(p => p.available).length}\n                </p>\n              </div>\n              <div className=\"text-4xl text-blue-500\">✅</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <div className=\"bg-white p-6 rounded-lg shadow-md\">\n            <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n              <span className=\"mr-2\">🍜</span>\n              菜品管理\n            </h2>\n            <p className=\"text-gray-600 mb-4\">管理菜品信息，包括添加、编辑和删除菜品</p>\n            <div className=\"space-y-2\">\n              <Link\n                href=\"/admin/products\"\n                className=\"block w-full bg-orange-500 text-white text-center py-2 px-4 rounded hover:bg-orange-600 transition-colors\"\n              >\n                管理菜品\n              </Link>\n              <Link\n                href=\"/admin/products/new\"\n                className=\"block w-full bg-green-500 text-white text-center py-2 px-4 rounded hover:bg-green-600 transition-colors\"\n              >\n                添加新菜品\n              </Link>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-md\">\n            <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n              <span className=\"mr-2\">📦</span>\n              库存管理\n            </h2>\n            <p className=\"text-gray-600 mb-4\">管理产品库存数量和上下架状态</p>\n            <div className=\"space-y-2\">\n              <Link\n                href=\"/admin/inventory\"\n                className=\"block w-full bg-blue-500 text-white text-center py-2 px-4 rounded hover:bg-blue-600 transition-colors\"\n              >\n                库存管理\n              </Link>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-md\">\n            <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n              <span className=\"mr-2\">📋</span>\n              产品类型管理\n            </h2>\n            <p className=\"text-gray-600 mb-4\">管理产品分类，如手工面条、饺子皮等</p>\n            <div className=\"space-y-2\">\n              <Link\n                href=\"/admin/product-types\"\n                className=\"block w-full bg-purple-500 text-white text-center py-2 px-4 rounded hover:bg-purple-600 transition-colors\"\n              >\n                管理产品类型\n              </Link>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-md\">\n            <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n              <span className=\"mr-2\">🏪</span>\n              门店管理\n            </h2>\n            <p className=\"text-gray-600 mb-4\">管理门店信息，包括位置、营业时间等</p>\n            <div className=\"space-y-2\">\n              <Link\n                href=\"/admin/stores\"\n                className=\"block w-full bg-blue-500 text-white text-center py-2 px-4 rounded hover:bg-blue-600 transition-colors\"\n              >\n                管理门店\n              </Link>\n              <Link\n                href=\"/admin/stores/new\"\n                className=\"block w-full bg-green-500 text-white text-center py-2 px-4 rounded hover:bg-green-600 transition-colors\"\n              >\n                添加新门店\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-8 bg-white p-6 rounded-lg shadow-md\">\n          <h2 className=\"text-xl font-semibold mb-4\">快速操作</h2>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            <Link\n              href=\"/\"\n              className=\"text-center p-4 border border-gray-200 rounded-lg hover:border-orange-500 hover:text-orange-600 transition-colors\"\n            >\n              <div className=\"text-2xl mb-2\">🏠</div>\n              <div className=\"text-sm\">返回首页</div>\n            </Link>\n            <Link\n              href=\"/products\"\n              className=\"text-center p-4 border border-gray-200 rounded-lg hover:border-orange-500 hover:text-orange-600 transition-colors\"\n            >\n              <div className=\"text-2xl mb-2\">📱</div>\n              <div className=\"text-sm\">查看网站</div>\n            </Link>\n            <button className=\"text-center p-4 border border-gray-200 rounded-lg hover:border-red-500 hover:text-red-600 transition-colors\">\n              <div className=\"text-2xl mb-2\">🗑️</div>\n              <div className=\"text-sm\">清理缓存</div>\n            </button>\n            <button className=\"text-center p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:text-green-600 transition-colors\">\n              <div className=\"text-2xl mb-2\">💾</div>\n              <div className=\"text-sm\">备份数据</div>\n            </button>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAiBe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;QACT,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,CAAC,QAAQ;YACX,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,kBAAkB;QAClB,MAAM,WAAW;YACf,IAAI;gBACF,aAAa;gBACb,MAAM,iBAAiB;gBACvB,MAAM,eAAe;gBAErB,YAAY,eAAe,OAAO;gBAClC,UAAU,aAAa,OAAO;YAChC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,WAAW;YAC3B,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAI,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI/C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAML,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAAsC,SAAS,MAAM;;;;;;;;;;;;sDAEpE,8OAAC;4CAAI,WAAU;sDAA2B;;;;;;;;;;;;;;;;;0CAI9C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAAqC,OAAO,MAAM;;;;;;;;;;;;sDAEjE,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;;;;;;;;;;;;0CAI7C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;;;;;;;;;;;;sDAG7C,8OAAC;4CAAI,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;;;;;;kCAK9C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAO;;;;;;4CAAS;;;;;;;kDAGlC,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAML,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAO;;;;;;4CAAS;;;;;;;kDAGlC,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAML,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAO;;;;;;4CAAS;;;;;;;kDAGlC,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAML,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAO;;;;;;4CAAS;;;;;;;kDAGlC,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;kCAOP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DAAU;;;;;;;;;;;;kDAE3B,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DAAU;;;;;;;;;;;;kDAE3B,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DAAU;;;;;;;;;;;;kDAE3B,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}]}