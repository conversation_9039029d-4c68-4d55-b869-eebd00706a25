'use client';

import { Store } from '@/types';
import { useState } from 'react';

interface StoreLocatorProps {
  stores: Store[];
}

export default function StoreLocator({ stores }: StoreLocatorProps) {
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="grid grid-cols-1 lg:grid-cols-2">
        <div className="p-6">
          <h3 className="text-xl font-semibold mb-4">门店列表</h3>
          <div className="space-y-4">
            {stores.map((store) => (
              <div
                key={store.id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedStore?.id === store.id
                    ? 'border-orange-500 bg-orange-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedStore(store)}
              >
                <h4 className="font-semibold text-gray-800">{store.name}</h4>
                <p className="text-gray-600 text-sm mt-1">{store.address}</p>
                <div className="flex justify-between items-center mt-2 text-sm">
                  <span className="text-gray-500">📞 {store.phone}</span>
                  <span className={`${store.isOpen ? 'text-green-600' : 'text-red-600'}`}>
                    {store.isOpen ? '营业中' : '已打烊'}
                  </span>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  营业时间: {store.openTime} - {store.closeTime}
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="bg-gray-100 p-6 flex items-center justify-center">
          {selectedStore ? (
            <div className="text-center">
              <div className="text-6xl mb-4">🗺️</div>
              <h3 className="text-xl font-semibold mb-2">{selectedStore.name}</h3>
              <p className="text-gray-600 mb-2">{selectedStore.address}</p>
              <p className="text-sm text-gray-500">
                纬度: {selectedStore.latitude.toFixed(4)}, 
                经度: {selectedStore.longitude.toFixed(4)}
              </p>
              <button className="mt-4 bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 transition-colors">
                导航到此店
              </button>
            </div>
          ) : (
            <div className="text-center text-gray-500">
              <div className="text-6xl mb-4">📍</div>
              <p>请选择门店查看位置信息</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}