{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/products/%5Bid%5D/not-found.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function ProductNotFound() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <div className=\"text-6xl mb-4\">🍜</div>\n        <h1 className=\"text-4xl font-bold text-gray-800 mb-4\">菜品不存在</h1>\n        <p className=\"text-gray-600 mb-8\">抱歉，您查找的菜品不存在</p>\n        <div className=\"space-x-4\">\n          <Link\n            href=\"/products\"\n            className=\"inline-block bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors\"\n          >\n            查看所有菜品\n          </Link>\n          <Link\n            href=\"/\"\n            className=\"inline-block border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:border-orange-500 hover:text-orange-600 transition-colors\"\n          >\n            返回首页\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BAAgB;;;;;;8BAC/B,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAClC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}]}