// 性能监控工具

// 性能计时器
export class PerformanceTimer {
  private startTime: number;
  private endTime?: number;
  private label: string;

  constructor(label: string) {
    this.label = label;
    this.startTime = performance.now();
  }

  end(): number {
    this.endTime = performance.now();
    const duration = this.endTime - this.startTime;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`⏱️ ${this.label}: ${duration.toFixed(2)}ms`);
    }
    
    return duration;
  }

  static measure<T>(label: string, fn: () => T): T;
  static measure<T>(label: string, fn: () => Promise<T>): Promise<T>;
  static measure<T>(label: string, fn: () => T | Promise<T>): T | Promise<T> {
    const timer = new PerformanceTimer(label);
    
    try {
      const result = fn();
      
      if (result instanceof Promise) {
        return result.finally(() => timer.end());
      } else {
        timer.end();
        return result;
      }
    } catch (error) {
      timer.end();
      throw error;
    }
  }
}

// 内存使用监控
export function logMemoryUsage(label?: string): void {
  if (typeof window === 'undefined' || !('memory' in performance)) {
    return;
  }

  const memory = (performance as any).memory;
  const used = Math.round(memory.usedJSHeapSize / 1048576 * 100) / 100;
  const total = Math.round(memory.totalJSHeapSize / 1048576 * 100) / 100;
  const limit = Math.round(memory.jsHeapSizeLimit / 1048576 * 100) / 100;

  console.log(`🧠 Memory ${label ? `(${label})` : ''}: ${used}MB / ${total}MB (limit: ${limit}MB)`);
}

// 网络请求监控
export function monitorFetch() {
  if (typeof window === 'undefined') return;

  const originalFetch = window.fetch;
  
  window.fetch = async function(...args) {
    const url = args[0] instanceof Request ? args[0].url : args[0];
    const timer = new PerformanceTimer(`Fetch: ${url}`);
    
    try {
      const response = await originalFetch.apply(this, args);
      timer.end();
      
      if (!response.ok) {
        console.warn(`❌ Fetch failed: ${url} (${response.status})`);
      }
      
      return response;
    } catch (error) {
      timer.end();
      console.error(`💥 Fetch error: ${url}`, error);
      throw error;
    }
  };
}

// 组件渲染性能监控
export function useRenderPerformance(componentName: string) {
  if (process.env.NODE_ENV === 'development') {
    const renderStart = performance.now();
    
    return () => {
      const renderEnd = performance.now();
      const duration = renderEnd - renderStart;
      
      if (duration > 16) { // 超过一帧的时间
        console.warn(`🐌 Slow render: ${componentName} took ${duration.toFixed(2)}ms`);
      }
    };
  }
  
  return () => {};
}

// 页面加载性能监控
export function logPagePerformance(): void {
  if (typeof window === 'undefined') return;

  window.addEventListener('load', () => {
    setTimeout(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        const metrics = {
          'DNS查询': navigation.domainLookupEnd - navigation.domainLookupStart,
          'TCP连接': navigation.connectEnd - navigation.connectStart,
          '请求响应': navigation.responseEnd - navigation.requestStart,
          'DOM解析': navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          '页面加载': navigation.loadEventEnd - navigation.loadEventStart,
          '总时间': navigation.loadEventEnd - navigation.navigationStart,
        };

        console.group('📊 页面性能指标');
        Object.entries(metrics).forEach(([name, value]) => {
          console.log(`${name}: ${value.toFixed(2)}ms`);
        });
        console.groupEnd();
      }
    }, 0);
  });
}

// 资源加载监控
export function monitorResourceLoading(): void {
  if (typeof window === 'undefined') return;

  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      if (entry.entryType === 'resource') {
        const resource = entry as PerformanceResourceTiming;
        const duration = resource.responseEnd - resource.startTime;
        
        if (duration > 1000) { // 超过1秒的资源
          console.warn(`🐌 Slow resource: ${resource.name} took ${duration.toFixed(2)}ms`);
        }
      }
    });
  });

  observer.observe({ entryTypes: ['resource'] });
}

// 长任务监控
export function monitorLongTasks(): void {
  if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return;

  try {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        console.warn(`⚠️ Long task detected: ${entry.duration.toFixed(2)}ms`);
      });
    });

    observer.observe({ entryTypes: ['longtask'] });
  } catch (error) {
    // Long task API not supported
  }
}

// 初始化性能监控
export function initPerformanceMonitoring(): void {
  if (process.env.NODE_ENV === 'development') {
    logPagePerformance();
    monitorResourceLoading();
    monitorLongTasks();
    monitorFetch();
  }
}
