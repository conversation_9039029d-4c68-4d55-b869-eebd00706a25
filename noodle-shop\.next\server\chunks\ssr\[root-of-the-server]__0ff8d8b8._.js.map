{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, ReactNode } from 'react';\r\n\r\ninterface ClientOnlyProps {\r\n  children: ReactNode;\r\n  fallback?: ReactNode;\r\n}\r\n\r\nexport default function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\r\n  const [hasMounted, setHasMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setHasMounted(true);\r\n  }, []);\r\n\r\n  if (!hasMounted) {\r\n    return <>{fallback}</>;\r\n  }\r\n\r\n  return <>{children}</>;\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;IAC/E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/StockEditModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface Product {\n  id: string;\n  name: string;\n  stock: number;\n  minStock: number;\n  available: boolean;\n  isActive: boolean;\n}\n\ninterface StockEditModalProps {\n  product: Product | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (productId: string, newStock: number, newMinStock: number) => Promise<void>;\n}\n\nexport default function StockEditModal({ product, isOpen, onClose, onSave }: StockEditModalProps) {\n  const [stock, setStock] = useState(0);\n  const [minStock, setMinStock] = useState(0);\n  const [loading, setSaving] = useState(false);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    if (product) {\n      setStock(product.stock);\n      setMinStock(product.minStock);\n      setError('');\n    }\n  }, [product]);\n\n  const handleSave = async () => {\n    if (!product) return;\n\n    // 验证输入\n    if (stock < 0) {\n      setError('库存数量不能为负数');\n      return;\n    }\n\n    if (minStock < 0) {\n      setError('最低库存不能为负数');\n      return;\n    }\n\n    if (minStock > stock) {\n      setError('最低库存不能大于当前库存');\n      return;\n    }\n\n    setSaving(true);\n    setError('');\n\n    try {\n      await onSave(product.id, stock, minStock);\n      onClose();\n    } catch (error) {\n      setError('保存失败，请重试');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleStockChange = (value: string) => {\n    const num = parseInt(value) || 0;\n    setStock(Math.max(0, num));\n  };\n\n  const handleMinStockChange = (value: string) => {\n    const num = parseInt(value) || 0;\n    setMinStock(Math.max(0, num));\n  };\n\n  const adjustStock = (amount: number) => {\n    setStock(prev => Math.max(0, prev + amount));\n  };\n\n  if (!isOpen || !product) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md mx-4\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-bold text-gray-800\">编辑库存</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n            disabled={loading}\n          >\n            ✕\n          </button>\n        </div>\n\n        <div className=\"mb-4\">\n          <h3 className=\"font-medium text-gray-700 mb-2\">{product.name}</h3>\n          <div className=\"text-sm text-gray-500\">\n            当前状态: {product.isActive ? '已上架' : '已下架'} | {product.available ? '有货' : '缺货'}\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-md\">\n            <p className=\"text-red-600 text-sm\">{error}</p>\n          </div>\n        )}\n\n        <div className=\"space-y-4\">\n          {/* 当前库存 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              当前库存\n            </label>\n            <div className=\"flex items-center space-x-2\">\n              <button\n                type=\"button\"\n                onClick={() => adjustStock(-10)}\n                className=\"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\"\n                disabled={loading}\n              >\n                -10\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => adjustStock(-1)}\n                className=\"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\"\n                disabled={loading}\n              >\n                -1\n              </button>\n              <input\n                type=\"number\"\n                value={stock}\n                onChange={(e) => handleStockChange(e.target.value)}\n                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n                min=\"0\"\n                disabled={loading}\n              />\n              <button\n                type=\"button\"\n                onClick={() => adjustStock(1)}\n                className=\"px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-sm\"\n                disabled={loading}\n              >\n                +1\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => adjustStock(10)}\n                className=\"px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-sm\"\n                disabled={loading}\n              >\n                +10\n              </button>\n            </div>\n          </div>\n\n          {/* 最低库存 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              最低库存警告线\n            </label>\n            <input\n              type=\"number\"\n              value={minStock}\n              onChange={(e) => handleMinStockChange(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n              min=\"0\"\n              disabled={loading}\n            />\n            <p className=\"text-xs text-gray-500 mt-1\">\n              当库存低于此数值时会显示警告\n            </p>\n          </div>\n\n          {/* 库存状态预览 */}\n          <div className=\"p-3 bg-gray-50 rounded-md\">\n            <h4 className=\"text-sm font-medium text-gray-700 mb-2\">状态预览</h4>\n            <div className=\"space-y-1 text-sm\">\n              <div className=\"flex justify-between\">\n                <span>库存状态:</span>\n                <span className={`font-medium ${\n                  stock === 0 ? 'text-red-600' : \n                  stock <= minStock ? 'text-yellow-600' : 'text-green-600'\n                }`}>\n                  {stock === 0 ? '缺货' : stock <= minStock ? '库存不足' : '库存充足'}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>可售状态:</span>\n                <span className={stock > 0 ? 'text-green-600' : 'text-red-600'}>\n                  {stock > 0 ? '可售' : '不可售'}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex justify-end space-x-3 mt-6\">\n          <button\n            onClick={onClose}\n            className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors\"\n            disabled={loading}\n          >\n            取消\n          </button>\n          <button\n            onClick={handleSave}\n            disabled={loading}\n            className=\"px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors disabled:opacity-50\"\n          >\n            {loading ? '保存中...' : '保存'}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAoBe,SAAS,eAAe,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAuB;IAC9F,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACtC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,SAAS,QAAQ,KAAK;YACtB,YAAY,QAAQ,QAAQ;YAC5B,SAAS;QACX;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,aAAa;QACjB,IAAI,CAAC,SAAS;QAEd,OAAO;QACP,IAAI,QAAQ,GAAG;YACb,SAAS;YACT;QACF;QAEA,IAAI,WAAW,GAAG;YAChB,SAAS;YACT;QACF;QAEA,IAAI,WAAW,OAAO;YACpB,SAAS;YACT;QACF;QAEA,UAAU;QACV,SAAS;QAET,IAAI;YACF,MAAM,OAAO,QAAQ,EAAE,EAAE,OAAO;YAChC;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,MAAM,SAAS,UAAU;QAC/B,SAAS,KAAK,GAAG,CAAC,GAAG;IACvB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,MAAM,SAAS,UAAU;QAC/B,YAAY,KAAK,GAAG,CAAC,GAAG;IAC1B;IAEA,MAAM,cAAc,CAAC;QACnB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;IACtC;IAEA,IAAI,CAAC,UAAU,CAAC,SAAS,OAAO;IAEhC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkC;;;;;;sCAChD,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,UAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkC,QAAQ,IAAI;;;;;;sCAC5D,8OAAC;4BAAI,WAAU;;gCAAwB;gCAC9B,QAAQ,QAAQ,GAAG,QAAQ;gCAAM;gCAAI,QAAQ,SAAS,GAAG,OAAO;;;;;;;;;;;;;gBAI1E,uBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;8BAIzC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,YAAY,CAAC;4CAC5B,WAAU;4CACV,UAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,YAAY,CAAC;4CAC5B,WAAU;4CACV,UAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,WAAU;4CACV,KAAI;4CACJ,UAAU;;;;;;sDAEZ,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,YAAY;4CAC3B,WAAU;4CACV,UAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,YAAY;4CAC3B,WAAU;4CACV,UAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;oCACpD,WAAU;oCACV,KAAI;oCACJ,UAAU;;;;;;8CAEZ,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAM5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAW,CAAC,YAAY,EAC5B,UAAU,IAAI,iBACd,SAAS,WAAW,oBAAoB,kBACxC;8DACC,UAAU,IAAI,OAAO,SAAS,WAAW,SAAS;;;;;;;;;;;;sDAGvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAW,QAAQ,IAAI,mBAAmB;8DAC7C,QAAQ,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO9B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,UAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAMlC", "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/admin/inventory/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useNotification } from '@/components/Notification';\nimport ClientOnly from '@/components/ClientOnly';\nimport StockEditModal from '@/components/StockEditModal';\n\ninterface Product {\n  id: string;\n  name: string;\n  price: number;\n  stock: number;\n  minStock: number;\n  available: boolean;\n  isActive: boolean;\n  productType: {\n    name: string;\n  };\n}\n\nexport default function InventoryPage() {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const { showNotification } = useNotification();\n\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n\n  const fetchProducts = async () => {\n    try {\n      const response = await fetch('/api/products');\n      const data = await response.json();\n      setProducts(data);\n    } catch (error) {\n      console.error('Failed to fetch products:', error);\n      showNotification('获取产品列表失败', 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const updateStock = async (productId: string, quantity: number) => {\n    try {\n      const response = await fetch(`/api/products/${productId}/stock`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ quantity }),\n      });\n\n      if (response.ok) {\n        showNotification('库存更新成功', 'success');\n        fetchProducts();\n      } else {\n        showNotification('库存更新失败', 'error');\n      }\n    } catch (error) {\n      console.error('Failed to update stock:', error);\n      showNotification('库存更新失败', 'error');\n    }\n  };\n\n  const handleEditStock = (product: Product) => {\n    setEditingProduct(product);\n    setIsModalOpen(true);\n  };\n\n  const handleSaveStock = async (productId: string, newStock: number, newMinStock: number) => {\n    try {\n      // 更新库存数量\n      const stockResponse = await fetch(`/api/products/${productId}/stock`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ quantity: newStock }),\n      });\n\n      if (!stockResponse.ok) {\n        throw new Error('更新库存失败');\n      }\n\n      // 更新最低库存（需要新的API）\n      const minStockResponse = await fetch(`/api/products/${productId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ minStock: newMinStock }),\n      });\n\n      if (!minStockResponse.ok) {\n        throw new Error('更新最低库存失败');\n      }\n\n      showNotification('库存设置保存成功', 'success');\n      fetchProducts();\n    } catch (error) {\n      console.error('Failed to save stock settings:', error);\n      throw error;\n    }\n  };\n\n  const toggleProductStatus = async (productId: string) => {\n    try {\n      const response = await fetch(`/api/products/${productId}/toggle`, {\n        method: 'PUT',\n      });\n\n      if (response.ok) {\n        showNotification('产品状态更新成功', 'success');\n        fetchProducts();\n      } else {\n        showNotification('状态更新失败', 'error');\n      }\n    } catch (error) {\n      console.error('Failed to toggle product status:', error);\n      showNotification('状态更新失败', 'error');\n    }\n  };\n\n  const getStockStatus = (stock: number, minStock: number) => {\n    if (stock === 0) return { label: '缺货', color: 'text-red-600 bg-red-50' };\n    if (stock <= minStock) return { label: '库存不足', color: 'text-yellow-600 bg-yellow-50' };\n    return { label: '库存充足', color: 'text-green-600 bg-green-50' };\n  };\n\n  if (loading) {\n    return (\n      <ClientOnly fallback={<div>加载中...</div>}>\n        <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">加载中...</p>\n          </div>\n        </div>\n      </ClientOnly>\n    );\n  }\n\n  return (\n    <ClientOnly fallback={<div>加载中...</div>}>\n      <div className=\"min-h-screen bg-gray-50\">\n        <header className=\"bg-white shadow-sm\">\n          <div className=\"container mx-auto px-4 py-4\">\n            <div className=\"flex justify-between items-center\">\n              <div className=\"flex items-center space-x-4\">\n                <Link href=\"/admin\" className=\"text-2xl font-bold text-orange-600 hover:text-orange-700\">\n                  管理后台\n                </Link>\n                <span className=\"text-gray-600\">库存管理</span>\n              </div>\n              <Link\n                href=\"/admin\"\n                className=\"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition-colors\"\n              >\n                返回后台\n              </Link>\n            </div>\n          </div>\n        </header>\n\n        <main className=\"container mx-auto px-4 py-8\">\n          <div className=\"bg-white rounded-lg shadow-md\">\n            <div className=\"p-6 border-b\">\n              <h1 className=\"text-2xl font-bold text-gray-800\">库存管理</h1>\n              <p className=\"text-gray-600 mt-2\">管理产品库存和上下架状态</p>\n            </div>\n\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      产品信息\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      价格\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      库存状态\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      销售状态\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      操作\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {products.map((product) => {\n                    const stockStatus = getStockStatus(product.stock, product.minStock);\n                    return (\n                      <tr key={product.id} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-900\">{product.name}</div>\n                            <div className=\"text-sm text-gray-500\">{product.productType.name}</div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className=\"text-sm font-medium text-gray-900\">¥{product.price}</span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"space-y-1\">\n                            <div className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${stockStatus.color}`}>\n                              {stockStatus.label}\n                            </div>\n                            <div className=\"text-sm text-gray-600\">\n                              当前: {product.stock} / 最低: {product.minStock}\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                            product.isActive \n                              ? 'bg-blue-100 text-blue-800' \n                              : 'bg-gray-100 text-gray-800'\n                          }`}>\n                            {product.isActive ? '已上架' : '已下架'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <div className=\"flex flex-col space-y-1\">\n                            <div className=\"flex space-x-2\">\n                              <button\n                                onClick={() => updateStock(product.id, 10)}\n                                className=\"text-green-600 hover:text-green-900 text-xs\"\n                                title=\"增加库存 +10\"\n                              >\n                                +10\n                              </button>\n                              <button\n                                onClick={() => updateStock(product.id, -1)}\n                                className=\"text-red-600 hover:text-red-900 text-xs\"\n                                title=\"减少库存 -1\"\n                              >\n                                -1\n                              </button>\n                              <button\n                                onClick={() => handleEditStock(product)}\n                                className=\"text-blue-600 hover:text-blue-900 text-xs\"\n                                title=\"编辑库存设置\"\n                              >\n                                编辑\n                              </button>\n                            </div>\n                            <button\n                              onClick={() => toggleProductStatus(product.id)}\n                              className={`text-xs ${\n                                product.isActive\n                                  ? 'text-gray-600 hover:text-gray-900'\n                                  : 'text-blue-600 hover:text-blue-900'\n                              }`}\n                            >\n                              {product.isActive ? '下架' : '上架'}\n                            </button>\n                          </div>\n                        </td>\n                      </tr>\n                    );\n                  })}\n                </tbody>\n              </table>\n            </div>\n\n            {products.length === 0 && (\n              <div className=\"text-center py-12\">\n                <div className=\"text-6xl mb-4\">📦</div>\n                <h2 className=\"text-xl text-gray-600\">暂无产品</h2>\n                <p className=\"text-gray-500 mt-2\">请先添加产品</p>\n              </div>\n            )}\n          </div>\n        </main>\n\n        {/* 库存编辑模态框 */}\n        <StockEditModal\n          product={editingProduct}\n          isOpen={isModalOpen}\n          onClose={() => {\n            setIsModalOpen(false);\n            setEditingProduct(null);\n          }}\n          onSave={handleSaveStock}\n        />\n      </div>\n    </ClientOnly>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAqBe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,iBAAiB,YAAY;QAC/B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc,OAAO,WAAmB;QAC5C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,MAAM,CAAC,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,iBAAiB,UAAU;gBAC3B;YACF,OAAO;gBACL,iBAAiB,UAAU;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,iBAAiB,UAAU;QAC7B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,kBAAkB,OAAO,WAAmB,UAAkB;QAClE,IAAI;YACF,SAAS;YACT,MAAM,gBAAgB,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,MAAM,CAAC,EAAE;gBACpE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,UAAU;gBAAS;YAC5C;YAEA,IAAI,CAAC,cAAc,EAAE,EAAE;gBACrB,MAAM,IAAI,MAAM;YAClB;YAEA,kBAAkB;YAClB,MAAM,mBAAmB,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW,EAAE;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,UAAU;gBAAY;YAC/C;YAEA,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBACxB,MAAM,IAAI,MAAM;YAClB;YAEA,iBAAiB,YAAY;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,OAAO,CAAC,EAAE;gBAChE,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,iBAAiB,YAAY;gBAC7B;YACF,OAAO;gBACL,iBAAiB,UAAU;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,iBAAiB,UAAU;QAC7B;IACF;IAEA,MAAM,iBAAiB,CAAC,OAAe;QACrC,IAAI,UAAU,GAAG,OAAO;YAAE,OAAO;YAAM,OAAO;QAAyB;QACvE,IAAI,SAAS,UAAU,OAAO;YAAE,OAAO;YAAQ,OAAO;QAA+B;QACrF,OAAO;YAAE,OAAO;YAAQ,OAAO;QAA6B;IAC9D;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC,gIAAA,CAAA,UAAU;YAAC,wBAAU,8OAAC;0BAAI;;;;;;sBACzB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;IAKvC;IAEA,qBACE,8OAAC,gIAAA,CAAA,UAAU;QAAC,wBAAU,8OAAC;sBAAI;;;;;;kBACzB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAO,WAAU;8BAChB,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAA2D;;;;;;sDAGzF,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAElC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;8BAOP,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAGpC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;;;;;;;;;;;;sDAKnG,8OAAC;4CAAM,WAAU;sDACd,SAAS,GAAG,CAAC,CAAC;gDACb,MAAM,cAAc,eAAe,QAAQ,KAAK,EAAE,QAAQ,QAAQ;gDAClE,qBACE,8OAAC;oDAAoB,WAAU;;sEAC7B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAqC,QAAQ,IAAI;;;;;;kFAChE,8OAAC;wEAAI,WAAU;kFAAyB,QAAQ,WAAW,CAAC,IAAI;;;;;;;;;;;;;;;;;sEAGpE,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAU;;oEAAoC;oEAAE,QAAQ,KAAK;;;;;;;;;;;;sEAErE,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAW,CAAC,yDAAyD,EAAE,YAAY,KAAK,EAAE;kFAC5F,YAAY,KAAK;;;;;;kFAEpB,8OAAC;wEAAI,WAAU;;4EAAwB;4EAChC,QAAQ,KAAK;4EAAC;4EAAQ,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;sEAIjD,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,CAAC,yDAAyD,EACzE,QAAQ,QAAQ,GACZ,8BACA,6BACJ;0EACC,QAAQ,QAAQ,GAAG,QAAQ;;;;;;;;;;;sEAGhC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFACC,SAAS,IAAM,YAAY,QAAQ,EAAE,EAAE;gFACvC,WAAU;gFACV,OAAM;0FACP;;;;;;0FAGD,8OAAC;gFACC,SAAS,IAAM,YAAY,QAAQ,EAAE,EAAE,CAAC;gFACxC,WAAU;gFACV,OAAM;0FACP;;;;;;0FAGD,8OAAC;gFACC,SAAS,IAAM,gBAAgB;gFAC/B,WAAU;gFACV,OAAM;0FACP;;;;;;;;;;;;kFAIH,8OAAC;wEACC,SAAS,IAAM,oBAAoB,QAAQ,EAAE;wEAC7C,WAAW,CAAC,QAAQ,EAClB,QAAQ,QAAQ,GACZ,sCACA,qCACJ;kFAED,QAAQ,QAAQ,GAAG,OAAO;;;;;;;;;;;;;;;;;;mDA9D1B,QAAQ,EAAE;;;;;4CAoEvB;;;;;;;;;;;;;;;;;4BAKL,SAAS,MAAM,KAAK,mBACnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;;;;;;;;;;;;8BAO1C,8OAAC,oIAAA,CAAA,UAAc;oBACb,SAAS;oBACT,QAAQ;oBACR,SAAS;wBACP,eAAe;wBACf,kBAAkB;oBACpB;oBACA,QAAQ;;;;;;;;;;;;;;;;;AAKlB", "debugId": null}}]}