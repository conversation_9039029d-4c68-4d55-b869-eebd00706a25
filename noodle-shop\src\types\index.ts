export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string; // 改为字符串，关联到 ProductType.id
  productTypeId: string; // 新增：关联产品类型
  image: string;
  ingredients: string[];
  spicyLevel: number; // 1-5
  available: boolean;
  stock: number; // 库存数量
  minStock: number; // 最低库存警告
  isActive: boolean; // 上下架状态
  publishedAt: string | null; // 发布日期
  createdAt: string;
  updatedAt: string;
  productType?: ProductType; // 关联的产品类型
}

export interface ProductType {
  id: string;
  name: string;
  description: string | null;
  displayOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Store {
  id: string;
  name: string;
  address: string;
  phone: string | null;
  latitude: number | null;
  longitude: number | null;
  openTime: string | null;
  closeTime: string | null;
  isOpen: boolean;
  image: string | null; // 新增：店铺图片
  createdAt: string;
  updatedAt: string;
}

// API 响应类型
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  products: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 搜索参数类型
export interface SearchParams {
  q?: string;
  category?: string;
  sortBy?: 'createdAt' | 'publishedAt' | 'name' | 'price';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// 库存状态类型
export interface StockStatus {
  status: 'in_stock' | 'low_stock' | 'out_of_stock';
  label: string;
  color: string;
}

// 表单数据类型
export interface ProductFormData {
  name: string;
  description: string;
  price: number;
  productTypeId: string;
  image: string;
  ingredients: string[];
  spicyLevel: number;
  available: boolean;
  stock: number;
  minStock: number;
  isActive: boolean;
}

export interface StoreFormData {
  name: string;
  address: string;
  phone: string;
  latitude: number;
  longitude: number;
  openTime: string;
  closeTime: string;
  isOpen: boolean;
  image: string;
}

// 通知类型
export interface NotificationProps {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

export interface WeatherInfo {
  temperature: number;
  description: string;
  city: string;
  icon: string;
}

export interface LocationInfo {
  ip: string;
  city: string;
  region: string;
  country: string;
  latitude: number;
  longitude: number;
}