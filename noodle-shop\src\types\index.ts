export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string; // 改为字符串，关联到 ProductType.id
  productTypeId: string; // 新增：关联产品类型
  image: string;
  ingredients: string[];
  spicyLevel: number; // 1-5
  available: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ProductType {
  id: string;
  name: string;
  description: string;
  displayOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Store {
  id: string;
  name: string;
  address: string;
  phone: string;
  latitude: number;
  longitude: number;
  openTime: string;
  closeTime: string;
  isOpen: boolean;
  image?: string; // 新增：店铺图片
  createdAt: string;
  updatedAt: string;
}

export interface WeatherInfo {
  temperature: number;
  description: string;
  city: string;
  icon: string;
}

export interface LocationInfo {
  ip: string;
  city: string;
  region: string;
  country: string;
  latitude: number;
  longitude: number;
}