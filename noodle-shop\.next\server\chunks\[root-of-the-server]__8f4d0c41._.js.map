{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/api/product-types/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { ProductType } from '@/types';\r\nimport productTypesData from '@/data/productTypes.json';\r\n\r\n// 模拟数据库存储\r\nlet productTypes: ProductType[] = [...productTypesData];\r\n\r\n// GET - 获取所有产品类型\r\nexport async function GET() {\r\n  try {\r\n    // 按显示顺序排序\r\n    const sortedTypes = productTypes\r\n      .filter(type => type.isActive)\r\n      .sort((a, b) => a.displayOrder - b.displayOrder);\r\n    \r\n    return NextResponse.json(sortedTypes);\r\n  } catch (error) {\r\n    return NextResponse.json(\r\n      { error: '获取产品类型失败' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// POST - 创建新产品类型\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n    \r\n    // 验证必填字段\r\n    if (!body.name || !body.description) {\r\n      return NextResponse.json(\r\n        { error: '名称和描述为必填项' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // 生成新的产品类型\r\n    const newProductType: ProductType = {\r\n      id: `type-${Date.now()}`,\r\n      name: body.name,\r\n      description: body.description,\r\n      displayOrder: body.displayOrder || productTypes.length + 1,\r\n      isActive: body.isActive !== false,\r\n      createdAt: new Date().toISOString(),\r\n      updatedAt: new Date().toISOString()\r\n    };\r\n\r\n    productTypes.push(newProductType);\r\n\r\n    return NextResponse.json(newProductType, { status: 201 });\r\n  } catch (error) {\r\n    return NextResponse.json(\r\n      { error: '创建产品类型失败' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}"], "names": [], "mappings": ";;;;AAAA;AAEA;;;AAEA,UAAU;AACV,IAAI,eAA8B;OAAI,mGAAA,CAAA,UAAgB;CAAC;AAGhD,eAAe;IACpB,IAAI;QACF,UAAU;QACV,MAAM,cAAc,aACjB,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAC5B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY;QAEjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,SAAS;QACT,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,WAAW,EAAE;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAY,GACrB;gBAAE,QAAQ;YAAI;QAElB;QAEA,WAAW;QACX,MAAM,iBAA8B;YAClC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACxB,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;YAC7B,cAAc,KAAK,YAAY,IAAI,aAAa,MAAM,GAAG;YACzD,UAAU,KAAK,QAAQ,KAAK;YAC5B,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,aAAa,IAAI,CAAC;QAElB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,gBAAgB;YAAE,QAAQ;QAAI;IACzD,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}