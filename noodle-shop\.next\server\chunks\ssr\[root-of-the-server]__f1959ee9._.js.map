{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/Notification.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, createContext, useContext, ReactNode } from 'react';\r\n\r\ninterface NotificationContextType {\r\n  showNotification: (message: string, type?: 'success' | 'error' | 'warning' | 'info') => void;\r\n  showConfirm: (message: string, onConfirm: () => void, onCancel?: () => void) => void;\r\n}\r\n\r\nconst NotificationContext = createContext<NotificationContextType | undefined>(undefined);\r\n\r\nexport const useNotification = () => {\r\n  const context = useContext(NotificationContext);\r\n  if (!context) {\r\n    throw new Error('useNotification must be used within a NotificationProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface Notification {\r\n  id: string;\r\n  message: string;\r\n  type: 'success' | 'error' | 'warning' | 'info';\r\n}\r\n\r\ninterface ConfirmDialog {\r\n  message: string;\r\n  onConfirm: () => void;\r\n  onCancel?: () => void;\r\n}\r\n\r\nexport function NotificationProvider({ children }: { children: ReactNode }) {\r\n  const [notifications, setNotifications] = useState<Notification[]>([]);\r\n  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialog | null>(null);\r\n\r\n  const showNotification = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {\r\n    const id = Date.now().toString();\r\n    const notification = { id, message, type };\r\n    \r\n    setNotifications(prev => [...prev, notification]);\r\n    \r\n    // 自动移除通知\r\n    setTimeout(() => {\r\n      setNotifications(prev => prev.filter(n => n.id !== id));\r\n    }, 5000);\r\n  };\r\n\r\n  const showConfirm = (message: string, onConfirm: () => void, onCancel?: () => void) => {\r\n    setConfirmDialog({ message, onConfirm, onCancel });\r\n  };\r\n\r\n  const removeNotification = (id: string) => {\r\n    setNotifications(prev => prev.filter(n => n.id !== id));\r\n  };\r\n\r\n  const handleConfirm = () => {\r\n    if (confirmDialog) {\r\n      confirmDialog.onConfirm();\r\n      setConfirmDialog(null);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    if (confirmDialog) {\r\n      confirmDialog.onCancel?.();\r\n      setConfirmDialog(null);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <NotificationContext.Provider value={{ showNotification, showConfirm }}>\r\n      {children}\r\n      \r\n      {/* 通知列表 */}\r\n      <div className=\"fixed top-4 right-4 z-50 space-y-2\">\r\n        {notifications.map((notification) => (\r\n          <div\r\n            key={notification.id}\r\n            className={`max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300 ${\r\n              notification.type === 'success' ? 'border-l-4 border-green-500' :\r\n              notification.type === 'error' ? 'border-l-4 border-red-500' :\r\n              notification.type === 'warning' ? 'border-l-4 border-yellow-500' :\r\n              'border-l-4 border-blue-500'\r\n            }`}\r\n          >\r\n            <div className=\"p-4\">\r\n              <div className=\"flex items-start\">\r\n                <div className=\"flex-shrink-0\">\r\n                  {notification.type === 'success' && (\r\n                    <svg className=\"h-6 w-6 text-green-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                  )}\r\n                  {notification.type === 'error' && (\r\n                    <svg className=\"h-6 w-6 text-red-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                  )}\r\n                  {notification.type === 'warning' && (\r\n                    <svg className=\"h-6 w-6 text-yellow-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n                    </svg>\r\n                  )}\r\n                  {notification.type === 'info' && (\r\n                    <svg className=\"h-6 w-6 text-blue-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                  )}\r\n                </div>\r\n                <div className=\"ml-3 w-0 flex-1 pt-0.5\">\r\n                  <p className=\"text-sm font-medium text-gray-900\">{notification.message}</p>\r\n                </div>\r\n                <div className=\"ml-4 flex-shrink-0 flex\">\r\n                  <button\r\n                    className=\"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n                    onClick={() => removeNotification(notification.id)}\r\n                  >\r\n                    <span className=\"sr-only\">关闭</span>\r\n                    <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                      <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                    </svg>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* 确认对话框 */}\r\n      {confirmDialog && (\r\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center\">\r\n          <div className=\"relative mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\r\n            <div className=\"mt-3 text-center\">\r\n              <div className=\"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100\">\r\n                <svg className=\"h-6 w-6 text-yellow-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-lg font-medium text-gray-900 mt-4\">确认操作</h3>\r\n              <div className=\"mt-2 px-7 py-3\">\r\n                <p className=\"text-sm text-gray-500\">{confirmDialog.message}</p>\r\n              </div>\r\n              <div className=\"flex justify-center space-x-3 mt-4\">\r\n                <button\r\n                  onClick={handleCancel}\r\n                  className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500\"\r\n                >\r\n                  取消\r\n                </button>\r\n                <button\r\n                  onClick={handleConfirm}\r\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-red-500 rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500\"\r\n                >\r\n                  确认\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </NotificationContext.Provider>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AASA,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAuC;AAExE,MAAM,kBAAkB;IAC7B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAcO,SAAS,qBAAqB,EAAE,QAAQ,EAA2B;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAEzE,MAAM,mBAAmB,CAAC,SAAiB,OAAiD,MAAM;QAChG,MAAM,KAAK,KAAK,GAAG,GAAG,QAAQ;QAC9B,MAAM,eAAe;YAAE;YAAI;YAAS;QAAK;QAEzC,iBAAiB,CAAA,OAAQ;mBAAI;gBAAM;aAAa;QAEhD,SAAS;QACT,WAAW;YACT,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACrD,GAAG;IACL;IAEA,MAAM,cAAc,CAAC,SAAiB,WAAuB;QAC3D,iBAAiB;YAAE;YAAS;YAAW;QAAS;IAClD;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrD;IAEA,MAAM,gBAAgB;QACpB,IAAI,eAAe;YACjB,cAAc,SAAS;YACvB,iBAAiB;QACnB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,eAAe;YACjB,cAAc,QAAQ;YACtB,iBAAiB;QACnB;IACF;IAEA,qBACE,8OAAC,oBAAoB,QAAQ;QAAC,OAAO;YAAE;YAAkB;QAAY;;YAClE;0BAGD,8OAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;wBAEC,WAAW,CAAC,yJAAyJ,EACnK,aAAa,IAAI,KAAK,YAAY,gCAClC,aAAa,IAAI,KAAK,UAAU,8BAChC,aAAa,IAAI,KAAK,YAAY,iCAClC,8BACA;kCAEF,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CACZ,aAAa,IAAI,KAAK,2BACrB,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC7E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CAGxE,aAAa,IAAI,KAAK,yBACrB,8OAAC;gDAAI,WAAU;gDAAuB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC3E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CAGxE,aAAa,IAAI,KAAK,2BACrB,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CAGxE,aAAa,IAAI,KAAK,wBACrB,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;kDAI3E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAqC,aAAa,OAAO;;;;;;;;;;;kDAExE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,SAAS,IAAM,mBAAmB,aAAa,EAAE;;8DAEjD,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,SAAQ;oDAAY,MAAK;8DAChD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA1C9O,aAAa,EAAE;;;;;;;;;;YAqDzB,+BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC9E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAyB,cAAc,OAAO;;;;;;;;;;;0CAE7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}