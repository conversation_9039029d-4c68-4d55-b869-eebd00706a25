'use client';

import { useState, useEffect } from 'react';
import { Product, ProductType } from '@/types';
import { useRouter } from 'next/navigation';
import ImageUpload from '@/components/ImageUpload';

interface ProductFormProps {
  product?: Product;
  isEdit?: boolean;
}

export default function ProductForm({ product, isEdit = false }: ProductFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [productTypes, setProductTypes] = useState<ProductType[]>([]);
  const [formData, setFormData] = useState({
    name: product?.name || '',
    description: product?.description || '',
    price: product?.price || 0,
    category: product?.category || '',
    productTypeId: product?.productTypeId || '',
    image: product?.image || '',
    ingredients: product?.ingredients?.join(', ') || '',
    spicyLevel: product?.spicyLevel || 0,
    available: product?.available ?? true,
  });

  useEffect(() => {
    fetchProductTypes();
  }, []);

  const fetchProductTypes = async () => {
    try {
      const response = await fetch('/api/product-types');
      const data = await response.json();
      setProductTypes(data);
      
      // 如果没有设置产品类型，设置第一个为默认值
      if (!formData.productTypeId && data.length > 0) {
        setFormData(prev => ({
          ...prev,
          productTypeId: data[0].id,
          category: data[0].id
        }));
      }
    } catch (error) {
      console.error('Failed to fetch product types:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const ingredientsArray = formData.ingredients
        .split(',')
        .map(item => item.trim())
        .filter(item => item.length > 0);

      const productData = {
        ...formData,
        price: Number(formData.price),
        spicyLevel: Number(formData.spicyLevel),
        ingredients: ingredientsArray,
        category: formData.productTypeId, // 保持兼容性
        productTypeId: formData.productTypeId,
        image: formData.image || `/images/default-product.svg`,
      };

      const url = isEdit ? `/api/products/${product?.id}` : '/api/products';
      const method = isEdit ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      if (response.ok) {
        router.push('/admin/products');
      } else {
        alert(isEdit ? '更新失败' : '添加失败');
      }
    } catch (error) {
      console.error('Failed to save product:', error);
      alert(isEdit ? '更新失败' : '添加失败');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <h1 className="text-2xl font-bold text-orange-600">
            {isEdit ? '编辑菜品' : '添加新菜品'}
          </h1>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                菜品名称 *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                描述 *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                required
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  价格 (¥) *
                </label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  required
                  min="0"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  产品类型 *
                </label>
                <select
                  name="productTypeId"
                  value={formData.productTypeId}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFormData(prev => ({
                      ...prev,
                      productTypeId: value,
                      category: value // 保持兼容性
                    }));
                  }}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                >
                  {productTypes.length === 0 ? (
                    <option value="">加载中...</option>
                  ) : (
                    productTypes.map(type => (
                      <option key={type.id} value={type.id}>
                        {type.name}
                      </option>
                    ))
                  )}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                产品图片
              </label>
              <ImageUpload
                value={formData.image}
                onChange={(url) => setFormData({ ...formData, image: url })}
                placeholder="点击或拖拽图片上传"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                配料 (用逗号分隔) *
              </label>
              <input
                type="text"
                name="ingredients"
                value={formData.ingredients}
                onChange={handleInputChange}
                required
                placeholder="例如: 牛肉, 拉面, 白萝卜"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                辣度等级 (0-5)
              </label>
              <select
                name="spicyLevel"
                value={formData.spicyLevel}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                <option value={0}>0 - 不辣</option>
                <option value={1}>1 - 微辣</option>
                <option value={2}>2 - 轻辣</option>
                <option value={3}>3 - 中辣</option>
                <option value={4}>4 - 很辣</option>
                <option value={5}>5 - 超辣</option>
              </select>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                name="available"
                checked={formData.available}
                onChange={handleInputChange}
                className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-700">
                现在有货
              </label>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => router.push('/admin/products')}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors disabled:opacity-50"
              >
                {loading ? '保存中...' : (isEdit ? '更新' : '添加')}
              </button>
            </div>
          </form>
        </div>
      </main>
    </div>
  );
}