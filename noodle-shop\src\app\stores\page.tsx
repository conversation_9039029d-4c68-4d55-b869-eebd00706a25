import { getStores } from '@/lib/database';
import WeatherWidget from '@/components/WeatherWidget';
import Link from 'next/link';
import Image from 'next/image';

export default async function StoresPage() {
  const rawStores = await getStores();

  // 转换门店数据格式以保持兼容性
  const stores = rawStores.map(store => ({
    ...store,
    phone: store.phone || '',
    latitude: store.latitude || 0,
    longitude: store.longitude || 0,
    openTime: store.openTime || '',
    closeTime: store.closeTime || '',
    image: store.image || '/images/default-store.svg',
    createdAt: store.createdAt.toISOString(),
    updatedAt: store.updatedAt.toISOString(),
  }));

  const isCurrentlyOpen = (store: typeof stores[0]) => {
    if (!store.isOpen || !store.openTime || !store.closeTime) return false;

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    const [openHour, openMin] = store.openTime.split(':').map(Number);
    const [closeHour, closeMin] = store.closeTime.split(':').map(Number);
    const openTime = openHour * 60 + openMin;
    const closeTime = closeHour * 60 + closeMin;

    return currentTime >= openTime && currentTime <= closeTime;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-orange-50 to-white">
      <header className="bg-white shadow-lg">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-3xl font-bold text-orange-600 hover:text-orange-700">
                香香手工面食店
              </Link>
              <span className="text-gray-600">门店分布</span>
            </div>
            <nav className="flex gap-6">
              <Link href="/" className="text-gray-700 hover:text-orange-600 transition-colors">
                首页
              </Link>
              <Link href="/products" className="text-gray-700 hover:text-orange-600 transition-colors">
                产品展示
              </Link>
              <Link href="/stores" className="text-orange-600 font-semibold">
                门店位置
              </Link>
            </nav>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <section className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-4 text-center">我们的门店</h1>
          <p className="text-center text-gray-600 mb-8">
            选择离您最近的门店，享受新鲜手工面食
          </p>
        </section>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 地图区域 */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-lg p-4">
              <h2 className="text-xl font-semibold mb-4">门店地图</h2>
              <StoreMap
                stores={stores}
                selectedStore={selectedStore}
                onStoreSelect={setSelectedStore}
                height="500px"
                enableSearch={true}
                showCurrentLocation={true}
              />
            </div>
          </div>

          {/* 门店列表 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-lg p-4">
              <h2 className="text-xl font-semibold mb-4">门店列表</h2>
              <div className="space-y-4 max-h-[500px] overflow-y-auto">
                {stores.map((store) => (
                  <div
                    key={store.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedStore?.id === store.id
                        ? 'border-orange-500 bg-orange-50'
                        : 'border-gray-200 hover:border-orange-300'
                    }`}
                    onClick={() => setSelectedStore(store)}
                  >
                    <h3 className="font-semibold text-gray-800">{store.name}</h3>
                    <p className="text-sm text-gray-600 mt-1">{store.address}</p>
                    <p className="text-sm text-gray-600">电话：{store.phone}</p>
                    <p className="text-sm text-gray-600">
                      营业时间：{store.openTime} - {store.closeTime}
                    </p>
                    <div className="mt-2">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          store.isOpen
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {store.isOpen ? '营业中' : '已打烊'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 选中门店详情 */}
        {selectedStore && (
          <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              {selectedStore.name}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-700 mb-2">联系信息</h3>
                <p className="text-gray-600">地址：{selectedStore.address}</p>
                <p className="text-gray-600">电话：{selectedStore.phone}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-700 mb-2">营业信息</h3>
                <p className="text-gray-600">
                  营业时间：{selectedStore.openTime} - {selectedStore.closeTime}
                </p>
                <p className="text-gray-600">
                  当前状态：
                  <span
                    className={`ml-2 font-semibold ${
                      selectedStore.isOpen ? 'text-green-600' : 'text-red-600'
                    }`}
                  >
                    {selectedStore.isOpen ? '营业中' : '已打烊'}
                  </span>
                </p>
              </div>
            </div>
            <div className="mt-4">
              <h3 className="font-semibold text-gray-700 mb-2">特色服务</h3>
              <div className="flex flex-wrap gap-2">
                <span className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm">
                  手工现做
                </span>
                <span className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm">
                  支持外卖
                </span>
                <span className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm">
                  批发零售
                </span>
                <span className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm">
                  会员优惠
                </span>
              </div>
            </div>
          </div>
        )}
      </main>

      <footer className="bg-gray-800 text-white py-8 mt-12">
        <div className="container mx-auto px-4 text-center">
          <p className="mb-2">© 2024 香香手工面食店 版权所有</p>
          <p className="text-gray-400">传承手工技艺 · 品味家的温暖</p>
        </div>
      </footer>
    </div>
  );
}