'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useNotification } from '@/components/Notification';
import ClientOnly from '@/components/ClientOnly';
import StockEditModal from '@/components/StockEditModal';

interface Product {
  id: string;
  name: string;
  price: number;
  stock: number;
  minStock: number;
  available: boolean;
  isActive: boolean;
  productType: {
    name: string;
  };
}

export default function InventoryPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { showNotification } = useNotification();

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/products');
      const data = await response.json();
      setProducts(data);
    } catch (error) {
      console.error('Failed to fetch products:', error);
      showNotification('获取产品列表失败', 'error');
    } finally {
      setLoading(false);
    }
  };

  const updateStock = async (productId: string, quantity: number) => {
    try {
      const response = await fetch(`/api/products/${productId}/stock`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ quantity }),
      });

      if (response.ok) {
        showNotification('库存更新成功', 'success');
        fetchProducts();
      } else {
        showNotification('库存更新失败', 'error');
      }
    } catch (error) {
      console.error('Failed to update stock:', error);
      showNotification('库存更新失败', 'error');
    }
  };

  const handleEditStock = (product: Product) => {
    setEditingProduct(product);
    setIsModalOpen(true);
  };

  const handleSaveStock = async (productId: string, newStock: number, newMinStock: number) => {
    try {
      // 更新库存数量
      const stockResponse = await fetch(`/api/products/${productId}/stock`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ quantity: newStock }),
      });

      if (!stockResponse.ok) {
        throw new Error('更新库存失败');
      }

      // 更新最低库存（需要新的API）
      const minStockResponse = await fetch(`/api/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ minStock: newMinStock }),
      });

      if (!minStockResponse.ok) {
        throw new Error('更新最低库存失败');
      }

      showNotification('库存设置保存成功', 'success');
      fetchProducts();
    } catch (error) {
      console.error('Failed to save stock settings:', error);
      throw error;
    }
  };

  const toggleProductStatus = async (productId: string) => {
    try {
      const response = await fetch(`/api/products/${productId}/toggle`, {
        method: 'PUT',
      });

      if (response.ok) {
        showNotification('产品状态更新成功', 'success');
        fetchProducts();
      } else {
        showNotification('状态更新失败', 'error');
      }
    } catch (error) {
      console.error('Failed to toggle product status:', error);
      showNotification('状态更新失败', 'error');
    }
  };

  const getStockStatus = (stock: number, minStock: number) => {
    if (stock === 0) return { label: '缺货', color: 'text-red-600 bg-red-50' };
    if (stock <= minStock) return { label: '库存不足', color: 'text-yellow-600 bg-yellow-50' };
    return { label: '库存充足', color: 'text-green-600 bg-green-50' };
  };

  if (loading) {
    return (
      <ClientOnly fallback={<div>加载中...</div>}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载中...</p>
          </div>
        </div>
      </ClientOnly>
    );
  }

  return (
    <ClientOnly fallback={<div>加载中...</div>}>
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm">
          <div className="container mx-auto px-4 py-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <Link href="/admin" className="text-2xl font-bold text-orange-600 hover:text-orange-700">
                  管理后台
                </Link>
                <span className="text-gray-600">库存管理</span>
              </div>
              <Link
                href="/admin"
                className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition-colors"
              >
                返回后台
              </Link>
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-md">
            <div className="p-6 border-b">
              <h1 className="text-2xl font-bold text-gray-800">库存管理</h1>
              <p className="text-gray-600 mt-2">管理产品库存和上下架状态</p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      产品信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      价格
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      库存状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      销售状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {products.map((product) => {
                    const stockStatus = getStockStatus(product.stock, product.minStock);
                    return (
                      <tr key={product.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{product.name}</div>
                            <div className="text-sm text-gray-500">{product.productType.name}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm font-medium text-gray-900">¥{product.price}</span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="space-y-1">
                            <div className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${stockStatus.color}`}>
                              {stockStatus.label}
                            </div>
                            <div className="text-sm text-gray-600">
                              当前: {product.stock} / 最低: {product.minStock}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            product.isActive 
                              ? 'bg-blue-100 text-blue-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {product.isActive ? '已上架' : '已下架'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex flex-col space-y-1">
                            <div className="flex space-x-2">
                              <button
                                onClick={() => updateStock(product.id, 10)}
                                className="text-green-600 hover:text-green-900 text-xs"
                                title="增加库存 +10"
                              >
                                +10
                              </button>
                              <button
                                onClick={() => updateStock(product.id, -1)}
                                className="text-red-600 hover:text-red-900 text-xs"
                                title="减少库存 -1"
                              >
                                -1
                              </button>
                              <button
                                onClick={() => handleEditStock(product)}
                                className="text-blue-600 hover:text-blue-900 text-xs"
                                title="编辑库存设置"
                              >
                                编辑
                              </button>
                            </div>
                            <button
                              onClick={() => toggleProductStatus(product.id)}
                              className={`text-xs ${
                                product.isActive
                                  ? 'text-gray-600 hover:text-gray-900'
                                  : 'text-blue-600 hover:text-blue-900'
                              }`}
                            >
                              {product.isActive ? '下架' : '上架'}
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {products.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📦</div>
                <h2 className="text-xl text-gray-600">暂无产品</h2>
                <p className="text-gray-500 mt-2">请先添加产品</p>
              </div>
            )}
          </div>
        </main>

        {/* 库存编辑模态框 */}
        <StockEditModal
          product={editingProduct}
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setEditingProduct(null);
          }}
          onSave={handleSaveStock}
        />
      </div>
    </ClientOnly>
  );
}
