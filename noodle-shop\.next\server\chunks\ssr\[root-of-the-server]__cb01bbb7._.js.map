{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/admin/product-types/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport { ProductType } from '@/types';\r\n\r\nexport default function AdminProductTypes() {\r\n  const [productTypes, setProductTypes] = useState<ProductType[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [showForm, setShowForm] = useState(false);\r\n  const [editingType, setEditingType] = useState<ProductType | null>(null);\r\n  const router = useRouter();\r\n\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    description: '',\r\n    displayOrder: 1,\r\n    isActive: true\r\n  });\r\n\r\n  useEffect(() => {\r\n    const auth = localStorage.getItem('adminAuth');\r\n    if (!auth) {\r\n      router.push('/admin/login');\r\n      return;\r\n    }\r\n    fetchProductTypes();\r\n  }, [router]);\r\n\r\n  const fetchProductTypes = async () => {\r\n    try {\r\n      const response = await fetch('/api/product-types');\r\n      const data = await response.json();\r\n      setProductTypes(data);\r\n    } catch (error) {\r\n      console.error('Failed to fetch product types:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    try {\r\n      const url = editingType ? `/api/product-types/${editingType.id}` : '/api/product-types';\r\n      const method = editingType ? 'PUT' : 'POST';\r\n      \r\n      const response = await fetch(url, {\r\n        method,\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(formData),\r\n      });\r\n\r\n      if (response.ok) {\r\n        await fetchProductTypes();\r\n        setShowForm(false);\r\n        setEditingType(null);\r\n        setFormData({\r\n          name: '',\r\n          description: '',\r\n          displayOrder: productTypes.length + 1,\r\n          isActive: true\r\n        });\r\n      } else {\r\n        const error = await response.json();\r\n        alert(error.error || '操作失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to save product type:', error);\r\n      alert('操作失败');\r\n    }\r\n  };\r\n\r\n  const handleEdit = (type: ProductType) => {\r\n    setEditingType(type);\r\n    setFormData({\r\n      name: type.name,\r\n      description: type.description,\r\n      displayOrder: type.displayOrder,\r\n      isActive: type.isActive\r\n    });\r\n    setShowForm(true);\r\n  };\r\n\r\n  const handleDelete = async (id: string) => {\r\n    if (confirm('确定要删除这个产品类型吗？删除后相关产品可能无法正常显示。')) {\r\n      try {\r\n        const response = await fetch(`/api/product-types/${id}`, {\r\n          method: 'DELETE',\r\n        });\r\n        if (response.ok) {\r\n          await fetchProductTypes();\r\n        } else {\r\n          alert('删除失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to delete product type:', error);\r\n        alert('删除失败');\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleToggleActive = async (type: ProductType) => {\r\n    try {\r\n      const response = await fetch(`/api/product-types/${type.id}`, {\r\n        method: 'PUT',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          ...type,\r\n          isActive: !type.isActive\r\n        }),\r\n      });\r\n\r\n      if (response.ok) {\r\n        await fetchProductTypes();\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to toggle product type:', error);\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto\"></div>\r\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <header className=\"bg-white shadow\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex justify-between items-center py-6\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <Link href=\"/admin\" className=\"text-orange-600 hover:text-orange-700\">\r\n                ← 返回管理后台\r\n              </Link>\r\n              <h1 className=\"text-3xl font-bold text-gray-900\">产品类型管理</h1>\r\n            </div>\r\n            <button\r\n              onClick={() => {\r\n                setEditingType(null);\r\n                setFormData({\r\n                  name: '',\r\n                  description: '',\r\n                  displayOrder: productTypes.length + 1,\r\n                  isActive: true\r\n                });\r\n                setShowForm(true);\r\n              }}\r\n              className=\"bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors\"\r\n            >\r\n              添加类型\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\r\n        <div className=\"px-4 py-6 sm:px-0\">\r\n          <div className=\"bg-white shadow rounded-lg\">\r\n            <div className=\"px-6 py-4 border-b border-gray-200\">\r\n              <h2 className=\"text-lg font-medium text-gray-900\">产品类型列表</h2>\r\n            </div>\r\n            <div className=\"overflow-x-auto\">\r\n              <table className=\"min-w-full divide-y divide-gray-200\">\r\n                <thead className=\"bg-gray-50\">\r\n                  <tr>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      排序\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      类型名称\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      描述\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      状态\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      操作\r\n                    </th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                  {productTypes.map((type) => (\r\n                    <tr key={type.id}>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\r\n                        {type.displayOrder}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"text-sm font-medium text-gray-900\">{type.name}</div>\r\n                        <div className=\"text-sm text-gray-500\">ID: {type.id}</div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 text-sm text-gray-500\">\r\n                        {type.description}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <button\r\n                          onClick={() => handleToggleActive(type)}\r\n                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\r\n                            type.isActive \r\n                              ? 'bg-green-100 text-green-800' \r\n                              : 'bg-gray-100 text-gray-800'\r\n                          }`}\r\n                        >\r\n                          {type.isActive ? '启用' : '停用'}\r\n                        </button>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\r\n                        <button\r\n                          onClick={() => handleEdit(type)}\r\n                          className=\"text-orange-600 hover:text-orange-900\"\r\n                        >\r\n                          编辑\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDelete(type.id)}\r\n                          className=\"text-red-600 hover:text-red-900\"\r\n                        >\r\n                          删除\r\n                        </button>\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n\r\n      {showForm && (\r\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\r\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\r\n            <div className=\"mt-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\r\n                {editingType ? '编辑产品类型' : '添加产品类型'}\r\n              </h3>\r\n              <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700\">类型名称</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={formData.name}\r\n                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}\r\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700\">描述</label>\r\n                  <textarea\r\n                    value={formData.description}\r\n                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}\r\n                    rows={3}\r\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700\">显示顺序</label>\r\n                  <input\r\n                    type=\"number\"\r\n                    value={formData.displayOrder}\r\n                    onChange={(e) => setFormData({ ...formData, displayOrder: parseInt(e.target.value) })}\r\n                    min=\"1\"\r\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"flex items-center\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={formData.isActive}\r\n                      onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}\r\n                      className=\"rounded border-gray-300 text-orange-600 focus:ring-orange-500\"\r\n                    />\r\n                    <span className=\"ml-2 text-sm text-gray-700\">启用该类型</span>\r\n                  </label>\r\n                </div>\r\n                <div className=\"flex justify-end space-x-3 pt-4\">\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => {\r\n                      setShowForm(false);\r\n                      setEditingType(null);\r\n                    }}\r\n                    className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300\"\r\n                  >\r\n                    取消\r\n                  </button>\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"px-4 py-2 text-sm font-medium text-white bg-orange-500 rounded-md hover:bg-orange-600\"\r\n                  >\r\n                    {editingType ? '更新' : '添加'}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,cAAc;QACd,UAAU;IACZ;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,aAAa,OAAO,CAAC;QAClC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;YACZ;QACF;QACA;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,MAAM,cAAc,CAAC,mBAAmB,EAAE,YAAY,EAAE,EAAE,GAAG;YACnE,MAAM,SAAS,cAAc,QAAQ;YAErC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,YAAY;gBACZ,eAAe;gBACf,YAAY;oBACV,MAAM;oBACN,aAAa;oBACb,cAAc,aAAa,MAAM,GAAG;oBACpC,UAAU;gBACZ;YACF,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,MAAM,KAAK,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,YAAY;YACV,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;YAC7B,cAAc,KAAK,YAAY;YAC/B,UAAU,KAAK,QAAQ;QACzB;QACA,YAAY;IACd;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,QAAQ,kCAAkC;YAC5C,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,IAAI,EAAE;oBACvD,QAAQ;gBACV;gBACA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM;gBACR,OAAO;oBACL,MAAM;gBACR;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,MAAM;YACR;QACF;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,KAAK,EAAE,EAAE,EAAE;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,IAAI;oBACP,UAAU,CAAC,KAAK,QAAQ;gBAC1B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAwC;;;;;;kDAGtE,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;0CAEnD,8OAAC;gCACC,SAAS;oCACP,eAAe;oCACf,YAAY;wCACV,MAAM;wCACN,aAAa;wCACb,cAAc,aAAa,MAAM,GAAG;wCACpC,UAAU;oCACZ;oCACA,YAAY;gCACd;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAOP,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;0CAEpD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;;;;;;;;;;;;sDAKnG,8OAAC;4CAAM,WAAU;sDACd,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,KAAK,YAAY;;;;;;sEAEpB,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;8EAAqC,KAAK,IAAI;;;;;;8EAC7D,8OAAC;oEAAI,WAAU;;wEAAwB;wEAAK,KAAK,EAAE;;;;;;;;;;;;;sEAErD,8OAAC;4DAAG,WAAU;sEACX,KAAK,WAAW;;;;;;sEAEnB,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEACC,SAAS,IAAM,mBAAmB;gEAClC,WAAW,CAAC,yDAAyD,EACnE,KAAK,QAAQ,GACT,gCACA,6BACJ;0EAED,KAAK,QAAQ,GAAG,OAAO;;;;;;;;;;;sEAG5B,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEACC,SAAS,IAAM,WAAW;oEAC1B,WAAU;8EACX;;;;;;8EAGD,8OAAC;oEACC,SAAS,IAAM,aAAa,KAAK,EAAE;oEACnC,WAAU;8EACX;;;;;;;;;;;;;mDAjCI,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YA8C7B,0BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,cAAc,WAAW;;;;;;0CAE5B,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAC3D,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACjE,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAGZ,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAC3D,8OAAC;gDACC,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACxE,MAAM;gDACN,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAGZ,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAC3D,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,YAAY;gDAC5B,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,cAAc,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAE;gDACnF,KAAI;gDACJ,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAGZ,8OAAC;kDACC,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,QAAQ;oDAC1B,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,UAAU,EAAE,MAAM,CAAC,OAAO;wDAAC;oDACvE,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;;;;;;kDAGjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,SAAS;oDACP,YAAY;oDACZ,eAAe;gDACjB;gDACA,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,WAAU;0DAET,cAAc,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1C", "debugId": null}}]}