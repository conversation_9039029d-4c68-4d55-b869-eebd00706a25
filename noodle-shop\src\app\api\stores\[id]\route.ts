import { NextRequest } from 'next/server';
import { getStoreById, updateStore, deleteStore } from '@/lib/data';

interface RouteContext {
  params: Promise<{ id: string }>;
}

export async function GET(request: NextRequest, context: RouteContext) {
  try {
    const { id } = await context.params;
    const store = await getStoreById(id);
    
    if (!store) {
      return Response.json({ error: 'Store not found' }, { status: 404 });
    }
    
    return Response.json(store);
  } catch (error) {
    console.error('Failed to fetch store:', error);
    return Response.json({ error: 'Failed to fetch store' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, context: RouteContext) {
  try {
    const { id } = await context.params;
    const body = await request.json();
    const store = await updateStore(id, body);
    
    if (!store) {
      return Response.json({ error: 'Store not found' }, { status: 404 });
    }
    
    return Response.json(store);
  } catch (error) {
    console.error('Failed to update store:', error);
    return Response.json({ error: 'Failed to update store' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, context: RouteContext) {
  try {
    const { id } = await context.params;
    const success = await deleteStore(id);
    
    if (!success) {
      return Response.json({ error: 'Store not found' }, { status: 404 });
    }
    
    return Response.json({ message: 'Store deleted successfully' });
  } catch (error) {
    console.error('Failed to delete store:', error);
    return Response.json({ error: 'Failed to delete store' }, { status: 500 });
  }
}