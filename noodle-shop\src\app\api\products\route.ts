import { NextRequest } from 'next/server';
import { getProducts, addProduct } from '@/lib/data';

export async function GET() {
  try {
    const products = await getProducts();
    return Response.json(products);
  } catch (error) {
    console.error('Failed to fetch products:', error);
    return Response.json({ error: 'Failed to fetch products' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const product = await addProduct(body);
    return Response.json(product, { status: 201 });
  } catch (error) {
    console.error('Failed to create product:', error);
    return Response.json({ error: 'Failed to create product' }, { status: 500 });
  }
}