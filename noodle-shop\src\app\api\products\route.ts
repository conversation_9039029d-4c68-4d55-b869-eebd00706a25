import { NextRequest } from 'next/server';
import { getProducts, createProduct, parseIngredients } from '@/lib/database';

export async function GET() {
  try {
    const products = await getProducts();

    // 转换数据格式以保持与前端的兼容性
    const formattedProducts = products.map(product => ({
      ...product,
      category: product.productTypeId, // 保持向后兼容
      ingredients: parseIngredients(product.ingredients),
      stock: product.stock || 0,
      minStock: product.minStock || 5,
      isActive: product.isActive !== false,
    }));

    return Response.json(formattedProducts);
  } catch (error) {
    console.error('Failed to fetch products:', error);
    return Response.json({ error: 'Failed to fetch products' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 数据验证和转换
    const productData = {
      name: body.name,
      description: body.description,
      price: Number(body.price),
      productTypeId: body.productTypeId || body.category,
      image: body.image,
      ingredients: body.ingredients || [],
      spicyLevel: Number(body.spicyLevel) || 0,
      available: body.available !== false,
      stock: Number(body.stock) || 0,
      minStock: Number(body.minStock) || 5,
      isActive: body.isActive !== false,
    };

    const product = await createProduct(productData);

    // 转换返回数据格式
    const formattedProduct = {
      ...product,
      category: product.productTypeId,
      ingredients: parseIngredients(product.ingredients),
      stock: product.stock || 0,
      minStock: product.minStock || 5,
      isActive: product.isActive !== false,
    };

    return Response.json(formattedProduct, { status: 201 });
  } catch (error) {
    console.error('Failed to create product:', error);
    return Response.json({ error: 'Failed to create product' }, { status: 500 });
  }
}