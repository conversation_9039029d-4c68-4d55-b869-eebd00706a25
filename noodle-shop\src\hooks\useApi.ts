// 通用API调用Hook

import { useState, useCallback } from 'react';
import { handleClientError } from '@/lib/errors';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}

export function useApi<T = any>(options: UseApiOptions = {}) {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(async (
    url: string,
    options: RequestInit = {}
  ): Promise<T | null> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const data = await response.json();
      setState({ data, loading: false, error: null });
      
      if (options.onSuccess) {
        options.onSuccess(data);
      }
      
      return data;
    } catch (error) {
      const errorMessage = handleClientError(error);
      setState(prev => ({ ...prev, loading: false, error: errorMessage }));
      
      if (options.onError) {
        options.onError(errorMessage);
      }
      
      return null;
    }
  }, [options]);

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null });
  }, []);

  return {
    ...state,
    execute,
    reset,
  };
}

// 专用的CRUD操作Hook
export function useCrudApi<T = any>(baseUrl: string, options: UseApiOptions = {}) {
  const api = useApi<T>(options);

  const getAll = useCallback(() => {
    return api.execute(baseUrl);
  }, [api, baseUrl]);

  const getById = useCallback((id: string) => {
    return api.execute(`${baseUrl}/${id}`);
  }, [api, baseUrl]);

  const create = useCallback((data: Partial<T>) => {
    return api.execute(baseUrl, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }, [api, baseUrl]);

  const update = useCallback((id: string, data: Partial<T>) => {
    return api.execute(`${baseUrl}/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }, [api, baseUrl]);

  const remove = useCallback((id: string) => {
    return api.execute(`${baseUrl}/${id}`, {
      method: 'DELETE',
    });
  }, [api, baseUrl]);

  return {
    ...api,
    getAll,
    getById,
    create,
    update,
    remove,
  };
}
