{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/lib/errors.ts"], "sourcesContent": ["// 错误处理工具类\n\nexport class AppError extends Error {\n  public readonly statusCode: number;\n  public readonly isOperational: boolean;\n\n  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {\n    super(message);\n    this.statusCode = statusCode;\n    this.isOperational = isOperational;\n\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nexport class ValidationError extends AppError {\n  constructor(message: string) {\n    super(message, 400);\n  }\n}\n\nexport class NotFoundError extends AppError {\n  constructor(resource: string = 'Resource') {\n    super(`${resource} not found`, 404);\n  }\n}\n\nexport class ConflictError extends AppError {\n  constructor(message: string) {\n    super(message, 409);\n  }\n}\n\n// 错误处理中间件\nexport function handleApiError(error: unknown): Response {\n  console.error('API Error:', error);\n\n  if (error instanceof AppError) {\n    return Response.json(\n      { error: error.message },\n      { status: error.statusCode }\n    );\n  }\n\n  if (error instanceof Error) {\n    return Response.json(\n      { error: error.message },\n      { status: 500 }\n    );\n  }\n\n  return Response.json(\n    { error: 'Internal server error' },\n    { status: 500 }\n  );\n}\n\n// 异步错误包装器\nexport function asyncHandler<T extends any[], R>(\n  fn: (...args: T) => Promise<R>\n) {\n  return async (...args: T): Promise<R> => {\n    try {\n      return await fn(...args);\n    } catch (error) {\n      throw error;\n    }\n  };\n}\n\n// 客户端错误处理\nexport function handleClientError(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return '发生未知错误';\n}\n\n// 表单验证错误\nexport interface ValidationErrors {\n  [key: string]: string[];\n}\n\nexport function formatValidationErrors(errors: ValidationErrors): string {\n  const messages: string[] = [];\n  for (const [field, fieldErrors] of Object.entries(errors)) {\n    messages.push(`${field}: ${fieldErrors.join(', ')}`);\n  }\n  return messages.join('; ');\n}\n"], "names": [], "mappings": "AAAA,UAAU;;;;;;;;;;;AAEH,MAAM,iBAAiB;IACZ,WAAmB;IACnB,cAAuB;IAEvC,YAAY,OAAe,EAAE,aAAqB,GAAG,EAAE,gBAAyB,IAAI,CAAE;QACpF,KAAK,CAAC;QACN,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,aAAa,GAAG;QAErB,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;IAChD;AACF;AAEO,MAAM,wBAAwB;IACnC,YAAY,OAAe,CAAE;QAC3B,KAAK,CAAC,SAAS;IACjB;AACF;AAEO,MAAM,sBAAsB;IACjC,YAAY,WAAmB,UAAU,CAAE;QACzC,KAAK,CAAC,GAAG,SAAS,UAAU,CAAC,EAAE;IACjC;AACF;AAEO,MAAM,sBAAsB;IACjC,YAAY,OAAe,CAAE;QAC3B,KAAK,CAAC,SAAS;IACjB;AACF;AAGO,SAAS,eAAe,KAAc;IAC3C,QAAQ,KAAK,CAAC,cAAc;IAE5B,IAAI,iBAAiB,UAAU;QAC7B,OAAO,SAAS,IAAI,CAClB;YAAE,OAAO,MAAM,OAAO;QAAC,GACvB;YAAE,QAAQ,MAAM,UAAU;QAAC;IAE/B;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,SAAS,IAAI,CAClB;YAAE,OAAO,MAAM,OAAO;QAAC,GACvB;YAAE,QAAQ;QAAI;IAElB;IAEA,OAAO,SAAS,IAAI,CAClB;QAAE,OAAO;IAAwB,GACjC;QAAE,QAAQ;IAAI;AAElB;AAGO,SAAS,aACd,EAA8B;IAE9B,OAAO,OAAO,GAAG;QACf,IAAI;YACF,OAAO,MAAM,MAAM;QACrB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;AAGO,SAAS,kBAAkB,KAAc;IAC9C,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,OAAO;AACT;AAOO,SAAS,uBAAuB,MAAwB;IAC7D,MAAM,WAAqB,EAAE;IAC7B,KAAK,MAAM,CAAC,OAAO,YAAY,IAAI,OAAO,OAAO,CAAC,QAAS;QACzD,SAAS,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,YAAY,IAAI,CAAC,OAAO;IACrD;IACA,OAAO,SAAS,IAAI,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/lib/validation.ts"], "sourcesContent": ["// 数据验证工具\n\nimport { z } from 'zod';\nimport { ValidationError } from './errors';\n\n// 基础验证规则\nexport const baseValidation = {\n  id: z.string().min(1, 'ID不能为空'),\n  name: z.string().min(1, '名称不能为空').max(100, '名称不能超过100个字符'),\n  description: z.string().max(500, '描述不能超过500个字符').optional(),\n  price: z.number().positive('价格必须大于0'),\n  phone: z.string().regex(/^1[3-9]\\d{9}$/, '请输入有效的手机号码').optional(),\n  email: z.string().email('请输入有效的邮箱地址').optional(),\n  url: z.string().url('请输入有效的URL').optional(),\n};\n\n// 产品验证模式\nexport const ProductValidationSchema = z.object({\n  id: baseValidation.id.optional(),\n  name: baseValidation.name,\n  description: baseValidation.description,\n  price: baseValidation.price,\n  productTypeId: baseValidation.id,\n  image: baseValidation.url.optional(),\n  ingredients: z.array(z.string()).default([]),\n  spicyLevel: z.number().int().min(0).max(5).default(0),\n  available: z.boolean().default(true),\n  stock: z.number().int().min(0).default(0),\n  minStock: z.number().int().min(0).default(5),\n  isActive: z.boolean().default(true),\n  publishedAt: z.string().datetime().optional().nullable(),\n});\n\n// 产品类型验证模式\nexport const ProductTypeValidationSchema = z.object({\n  id: baseValidation.id.optional(),\n  name: baseValidation.name,\n  description: baseValidation.description,\n  displayOrder: z.number().int().min(0).default(0),\n  isActive: z.boolean().default(true),\n});\n\n// 门店验证模式\nexport const StoreValidationSchema = z.object({\n  id: baseValidation.id.optional(),\n  name: baseValidation.name,\n  address: z.string().min(1, '地址不能为空').max(200, '地址不能超过200个字符'),\n  phone: baseValidation.phone,\n  latitude: z.number().min(-90).max(90).optional().nullable(),\n  longitude: z.number().min(-180).max(180).optional().nullable(),\n  openTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式(HH:MM)').optional(),\n  closeTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式(HH:MM)').optional(),\n  isOpen: z.boolean().default(true),\n  image: baseValidation.url.optional(),\n});\n\n// 搜索参数验证模式\nexport const SearchParamsSchema = z.object({\n  q: z.string().max(100, '搜索关键词不能超过100个字符').optional(),\n  category: z.string().optional(),\n  sortBy: z.enum(['createdAt', 'publishedAt', 'name', 'price']).default('createdAt'),\n  sortOrder: z.enum(['asc', 'desc']).default('desc'),\n  page: z.number().int().min(1).default(1),\n  limit: z.number().int().min(1).max(100).default(12),\n});\n\n// 验证函数\nexport function validateData<T>(schema: z.ZodSchema<T>, data: unknown): T {\n  try {\n    return schema.parse(data);\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      const messages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);\n      throw new ValidationError(messages.join(', '));\n    }\n    throw error;\n  }\n}\n\n// 安全解析（不抛出错误）\nexport function safeValidateData<T>(schema: z.ZodSchema<T>, data: unknown): {\n  success: boolean;\n  data?: T;\n  errors?: string[];\n} {\n  try {\n    const result = schema.safeParse(data);\n    if (result.success) {\n      return { success: true, data: result.data };\n    } else {\n      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`);\n      return { success: false, errors };\n    }\n  } catch (error) {\n    return { success: false, errors: ['验证过程中发生错误'] };\n  }\n}\n\n// 部分验证（用于更新操作）\nexport function validatePartialData<T>(schema: z.ZodSchema<T>, data: unknown): Partial<T> {\n  const partialSchema = schema.partial();\n  return validateData(partialSchema, data);\n}\n\n// 自定义验证规则\nexport const customValidators = {\n  // 验证营业时间\n  validateBusinessHours: (openTime?: string, closeTime?: string) => {\n    if (!openTime || !closeTime) return true;\n    \n    const open = new Date(`2000-01-01 ${openTime}`);\n    const close = new Date(`2000-01-01 ${closeTime}`);\n    \n    return open < close;\n  },\n\n  // 验证库存逻辑\n  validateStock: (stock: number, minStock: number) => {\n    return stock >= 0 && minStock >= 0 && minStock <= stock;\n  },\n\n  // 验证价格范围\n  validatePriceRange: (price: number, min: number = 0, max: number = 10000) => {\n    return price >= min && price <= max;\n  },\n\n  // 验证坐标\n  validateCoordinates: (latitude?: number, longitude?: number) => {\n    if (latitude === undefined || longitude === undefined) return true;\n    return latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180;\n  },\n};\n"], "names": [], "mappings": "AAAA,SAAS;;;;;;;;;;;;AAET;AACA;;;AAGO,MAAM,iBAAiB;IAC5B,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACtB,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC3C,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,gBAAgB,QAAQ;IACzD,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC3B,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,iBAAiB,cAAc,QAAQ;IAC/D,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ;IAC9C,KAAK,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,aAAa,QAAQ;AAC3C;AAGO,MAAM,0BAA0B,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,IAAI,eAAe,EAAE,CAAC,QAAQ;IAC9B,MAAM,eAAe,IAAI;IACzB,aAAa,eAAe,WAAW;IACvC,OAAO,eAAe,KAAK;IAC3B,eAAe,eAAe,EAAE;IAChC,OAAO,eAAe,GAAG,CAAC,QAAQ;IAClC,aAAa,+KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,+KAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IAC3C,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACnD,WAAW,+KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACvC,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAC1C,UAAU,+KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ;AACxD;AAGO,MAAM,8BAA8B,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClD,IAAI,eAAe,EAAE,CAAC,QAAQ;IAC9B,MAAM,eAAe,IAAI;IACzB,aAAa,eAAe,WAAW;IACvC,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAC9C,UAAU,+KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAGO,MAAM,wBAAwB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,IAAI,eAAe,EAAE,CAAC,QAAQ;IAC9B,MAAM,eAAe,IAAI;IACzB,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC9C,OAAO,eAAe,KAAK;IAC3B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,GAAG,QAAQ;IACzD,WAAW,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,KAAK,QAAQ,GAAG,QAAQ;IAC5D,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,oCAAoC,qBAAqB,QAAQ;IAC5F,WAAW,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,oCAAoC,qBAAqB,QAAQ;IAC7F,QAAQ,+KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC5B,OAAO,eAAe,GAAG,CAAC,QAAQ;AACpC;AAGO,MAAM,qBAAqB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,GAAG,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,mBAAmB,QAAQ;IAClD,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,QAAQ,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAa;QAAe;QAAQ;KAAQ,EAAE,OAAO,CAAC;IACtE,WAAW,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;KAAO,EAAE,OAAO,CAAC;IAC3C,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACtC,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,OAAO,CAAC;AAClD;AAGO,SAAS,aAAgB,MAAsB,EAAE,IAAa;IACnE,IAAI;QACF,OAAO,OAAO,KAAK,CAAC;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,+KAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,MAAM,WAAW,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE;YAChF,MAAM,IAAI,sHAAA,CAAA,kBAAe,CAAC,SAAS,IAAI,CAAC;QAC1C;QACA,MAAM;IACR;AACF;AAGO,SAAS,iBAAoB,MAAsB,EAAE,IAAa;IAKvE,IAAI;QACF,MAAM,SAAS,OAAO,SAAS,CAAC;QAChC,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO;gBAAE,SAAS;gBAAM,MAAM,OAAO,IAAI;YAAC;QAC5C,OAAO;YACL,MAAM,SAAS,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE;YACrF,OAAO;gBAAE,SAAS;gBAAO;YAAO;QAClC;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,SAAS;YAAO,QAAQ;gBAAC;aAAY;QAAC;IACjD;AACF;AAGO,SAAS,oBAAuB,MAAsB,EAAE,IAAa;IAC1E,MAAM,gBAAgB,OAAO,OAAO;IACpC,OAAO,aAAa,eAAe;AACrC;AAGO,MAAM,mBAAmB;IAC9B,SAAS;IACT,uBAAuB,CAAC,UAAmB;QACzC,IAAI,CAAC,YAAY,CAAC,WAAW,OAAO;QAEpC,MAAM,OAAO,IAAI,KAAK,CAAC,WAAW,EAAE,UAAU;QAC9C,MAAM,QAAQ,IAAI,KAAK,CAAC,WAAW,EAAE,WAAW;QAEhD,OAAO,OAAO;IAChB;IAEA,SAAS;IACT,eAAe,CAAC,OAAe;QAC7B,OAAO,SAAS,KAAK,YAAY,KAAK,YAAY;IACpD;IAEA,SAAS;IACT,oBAAoB,CAAC,OAAe,MAAc,CAAC,EAAE,MAAc,KAAK;QACtE,OAAO,SAAS,OAAO,SAAS;IAClC;IAEA,OAAO;IACP,qBAAqB,CAAC,UAAmB;QACvC,IAAI,aAAa,aAAa,cAAc,WAAW,OAAO;QAC9D,OAAO,YAAY,CAAC,MAAM,YAAY,MAAM,aAAa,CAAC,OAAO,aAAa;IAChF;AACF", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/lib/database.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\nimport { z } from 'zod';\nimport { NotFoundError, ValidationError } from './errors';\nimport { validateData } from './validation';\nimport {\n  ProductValidationSchema,\n  ProductTypeValidationSchema,\n  StoreValidationSchema\n} from './validation';\n\n// 全局Prisma客户端实例\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient({\n  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],\n});\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Zod验证模式\nexport const ProductTypeSchema = z.object({\n  id: z.string().min(1),\n  name: z.string().min(1),\n  description: z.string().optional(),\n  displayOrder: z.number().int().default(0),\n  isActive: z.boolean().default(true),\n});\n\nexport const ProductSchema = z.object({\n  id: z.string().min(1),\n  name: z.string().min(1),\n  description: z.string().optional(),\n  price: z.number().positive(),\n  productTypeId: z.string().min(1),\n  image: z.string().optional(),\n  ingredients: z.array(z.string()).optional(),\n  spicyLevel: z.number().int().min(0).max(5).default(0),\n  available: z.boolean().default(true),\n  stock: z.number().int().min(0).default(0),\n  minStock: z.number().int().min(0).default(5),\n  isActive: z.boolean().default(true),\n});\n\nexport const StoreSchema = z.object({\n  id: z.string().min(1),\n  name: z.string().min(1),\n  address: z.string().min(1),\n  phone: z.string().optional(),\n  latitude: z.number().optional(),\n  longitude: z.number().optional(),\n  openTime: z.string().optional(),\n  closeTime: z.string().optional(),\n  isOpen: z.boolean().default(true),\n  image: z.string().optional(),\n});\n\n// 产品类型相关操作\nexport async function getProductTypes() {\n  return await prisma.productType.findMany({\n    where: { isActive: true },\n    orderBy: { displayOrder: 'asc' },\n  });\n}\n\nexport async function getProductTypeById(id: string) {\n  return await prisma.productType.findUnique({\n    where: { id },\n  });\n}\n\nexport async function createProductType(data: z.infer<typeof ProductTypeSchema>) {\n  const validatedData = ProductTypeSchema.parse(data);\n  return await prisma.productType.create({\n    data: validatedData,\n  });\n}\n\nexport async function updateProductType(id: string, data: Partial<z.infer<typeof ProductTypeSchema>>) {\n  return await prisma.productType.update({\n    where: { id },\n    data,\n  });\n}\n\nexport async function deleteProductType(id: string) {\n  // 检查是否有关联的产品\n  const productCount = await prisma.product.count({\n    where: { productTypeId: id },\n  });\n  \n  if (productCount > 0) {\n    throw new Error('无法删除：该产品类型下还有产品');\n  }\n  \n  return await prisma.productType.delete({\n    where: { id },\n  });\n}\n\n// 产品相关操作\nexport async function getProducts() {\n  return await prisma.product.findMany({\n    include: {\n      productType: true,\n    },\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\nexport async function getProductById(id: string) {\n  return await prisma.product.findUnique({\n    where: { id },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function getProductsByCategory(productTypeId: string) {\n  return await prisma.product.findMany({\n    where: { productTypeId },\n    include: {\n      productType: true,\n    },\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\nexport async function createProduct(data: Omit<z.infer<typeof ProductValidationSchema>, 'id'>) {\n  const validatedData = validateData(ProductValidationSchema.omit({ id: true }), data);\n\n  // 检查产品类型是否存在\n  const productType = await prisma.productType.findUnique({\n    where: { id: validatedData.productTypeId },\n  });\n\n  if (!productType) {\n    throw new NotFoundError('产品类型');\n  }\n\n  // 生成新ID\n  const newId = Date.now().toString();\n\n  return await prisma.product.create({\n    data: {\n      ...validatedData,\n      id: newId,\n      ingredients: validatedData.ingredients ? JSON.stringify(validatedData.ingredients) : null,\n      publishedAt: validatedData.isActive ? new Date() : null,\n    },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function updateProduct(id: string, data: Partial<Omit<z.infer<typeof ProductSchema>, 'id'>>) {\n  const updateData: any = { ...data };\n  \n  if (data.ingredients) {\n    updateData.ingredients = JSON.stringify(data.ingredients);\n  }\n  \n  return await prisma.product.update({\n    where: { id },\n    data: updateData,\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function deleteProduct(id: string) {\n  return await prisma.product.delete({\n    where: { id },\n  });\n}\n\n// 门店相关操作\nexport async function getStores() {\n  return await prisma.store.findMany({\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\nexport async function getStoreById(id: string) {\n  return await prisma.store.findUnique({\n    where: { id },\n  });\n}\n\nexport async function createStore(data: Omit<z.infer<typeof StoreSchema>, 'id'>) {\n  const validatedData = StoreSchema.omit({ id: true }).parse(data);\n  \n  // 生成新ID\n  const newId = Date.now().toString();\n  \n  return await prisma.store.create({\n    data: {\n      ...validatedData,\n      id: newId,\n    },\n  });\n}\n\nexport async function updateStore(id: string, data: Partial<Omit<z.infer<typeof StoreSchema>, 'id'>>) {\n  return await prisma.store.update({\n    where: { id },\n    data,\n  });\n}\n\nexport async function deleteStore(id: string) {\n  return await prisma.store.delete({\n    where: { id },\n  });\n}\n\n// 库存管理相关操作\nexport async function updateProductStock(id: string, quantity: number) {\n  const product = await prisma.product.findUnique({ where: { id } });\n  if (!product) throw new Error('产品不存在');\n\n  const newStock = Math.max(0, product.stock + quantity);\n\n  return await prisma.product.update({\n    where: { id },\n    data: {\n      stock: newStock,\n      available: newStock > 0, // 自动更新可用状态\n    },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function toggleProductStatus(id: string) {\n  const product = await prisma.product.findUnique({ where: { id } });\n  if (!product) throw new Error('产品不存在');\n\n  return await prisma.product.update({\n    where: { id },\n    data: { isActive: !product.isActive },\n    include: {\n      productType: true,\n    },\n  });\n}\n\nexport async function getLowStockProducts() {\n  return await prisma.product.findMany({\n    where: {\n      OR: [\n        { stock: { lte: prisma.product.fields.minStock } },\n        { stock: 0 },\n      ],\n    },\n    include: {\n      productType: true,\n    },\n    orderBy: { stock: 'asc' },\n  });\n}\n\nexport async function getActiveProducts() {\n  return await prisma.product.findMany({\n    where: {\n      isActive: true,\n      available: true,\n    },\n    include: {\n      productType: true,\n    },\n    orderBy: { createdAt: 'desc' },\n  });\n}\n\n// 工具函数：解析ingredients JSON字符串\nexport function parseIngredients(ingredients: string | null): string[] {\n  if (!ingredients) return [];\n  try {\n    return JSON.parse(ingredients);\n  } catch {\n    return [];\n  }\n}\n\n// 工具函数：获取库存状态\nexport function getStockStatus(stock: number, minStock: number) {\n  if (stock === 0) return { status: 'out_of_stock', label: '缺货', color: 'red' };\n  if (stock <= minStock) return { status: 'low_stock', label: '库存不足', color: 'yellow' };\n  return { status: 'in_stock', label: '库存充足', color: 'green' };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAOA,gBAAgB;AAChB,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY,CAAC;IAC/D,KAAK,uCAAyC;QAAC;QAAS;QAAS;KAAO,GAAG;AAC7E;AAEA,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,MAAM,oBAAoB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;IACvC,UAAU,+KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAEO,MAAM,gBAAgB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,eAAe,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC9B,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,aAAa,+KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,+KAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IACzC,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACnD,WAAW,+KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACvC,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAC1C,UAAU,+KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAEO,MAAM,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACxB,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,QAAQ,+KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC5B,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AAGO,eAAe;IACpB,OAAO,MAAM,OAAO,WAAW,CAAC,QAAQ,CAAC;QACvC,OAAO;YAAE,UAAU;QAAK;QACxB,SAAS;YAAE,cAAc;QAAM;IACjC;AACF;AAEO,eAAe,mBAAmB,EAAU;IACjD,OAAO,MAAM,OAAO,WAAW,CAAC,UAAU,CAAC;QACzC,OAAO;YAAE;QAAG;IACd;AACF;AAEO,eAAe,kBAAkB,IAAuC;IAC7E,MAAM,gBAAgB,kBAAkB,KAAK,CAAC;IAC9C,OAAO,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;QACrC,MAAM;IACR;AACF;AAEO,eAAe,kBAAkB,EAAU,EAAE,IAAgD;IAClG,OAAO,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;QACrC,OAAO;YAAE;QAAG;QACZ;IACF;AACF;AAEO,eAAe,kBAAkB,EAAU;IAChD,aAAa;IACb,MAAM,eAAe,MAAM,OAAO,OAAO,CAAC,KAAK,CAAC;QAC9C,OAAO;YAAE,eAAe;QAAG;IAC7B;IAEA,IAAI,eAAe,GAAG;QACpB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;QACrC,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe;IACpB,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEO,eAAe,eAAe,EAAU;IAC7C,OAAO,MAAM,OAAO,OAAO,CAAC,UAAU,CAAC;QACrC,OAAO;YAAE;QAAG;QACZ,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,sBAAsB,aAAqB;IAC/D,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,OAAO;YAAE;QAAc;QACvB,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEO,eAAe,cAAc,IAAyD;IAC3F,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,0HAAA,CAAA,0BAAuB,CAAC,IAAI,CAAC;QAAE,IAAI;IAAK,IAAI;IAE/E,aAAa;IACb,MAAM,cAAc,MAAM,OAAO,WAAW,CAAC,UAAU,CAAC;QACtD,OAAO;YAAE,IAAI,cAAc,aAAa;QAAC;IAC3C;IAEA,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,sHAAA,CAAA,gBAAa,CAAC;IAC1B;IAEA,QAAQ;IACR,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;IAEjC,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,MAAM;YACJ,GAAG,aAAa;YAChB,IAAI;YACJ,aAAa,cAAc,WAAW,GAAG,KAAK,SAAS,CAAC,cAAc,WAAW,IAAI;YACrF,aAAa,cAAc,QAAQ,GAAG,IAAI,SAAS;QACrD;QACA,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,cAAc,EAAU,EAAE,IAAwD;IACtG,MAAM,aAAkB;QAAE,GAAG,IAAI;IAAC;IAElC,IAAI,KAAK,WAAW,EAAE;QACpB,WAAW,WAAW,GAAG,KAAK,SAAS,CAAC,KAAK,WAAW;IAC1D;IAEA,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;QACZ,MAAM;QACN,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,cAAc,EAAU;IAC5C,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe;IACpB,OAAO,MAAM,OAAO,KAAK,CAAC,QAAQ,CAAC;QACjC,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEO,eAAe,aAAa,EAAU;IAC3C,OAAO,MAAM,OAAO,KAAK,CAAC,UAAU,CAAC;QACnC,OAAO;YAAE;QAAG;IACd;AACF;AAEO,eAAe,YAAY,IAA6C;IAC7E,MAAM,gBAAgB,YAAY,IAAI,CAAC;QAAE,IAAI;IAAK,GAAG,KAAK,CAAC;IAE3D,QAAQ;IACR,MAAM,QAAQ,KAAK,GAAG,GAAG,QAAQ;IAEjC,OAAO,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;QAC/B,MAAM;YACJ,GAAG,aAAa;YAChB,IAAI;QACN;IACF;AACF;AAEO,eAAe,YAAY,EAAU,EAAE,IAAsD;IAClG,OAAO,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;QAC/B,OAAO;YAAE;QAAG;QACZ;IACF;AACF;AAEO,eAAe,YAAY,EAAU;IAC1C,OAAO,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;QAC/B,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe,mBAAmB,EAAU,EAAE,QAAgB;IACnE,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC,UAAU,CAAC;QAAE,OAAO;YAAE;QAAG;IAAE;IAChE,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,QAAQ,KAAK,GAAG;IAE7C,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;QACZ,MAAM;YACJ,OAAO;YACP,WAAW,WAAW;QACxB;QACA,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe,oBAAoB,EAAU;IAClD,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC,UAAU,CAAC;QAAE,OAAO;YAAE;QAAG;IAAE;IAChE,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,OAAO,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;QACjC,OAAO;YAAE;QAAG;QACZ,MAAM;YAAE,UAAU,CAAC,QAAQ,QAAQ;QAAC;QACpC,SAAS;YACP,aAAa;QACf;IACF;AACF;AAEO,eAAe;IACpB,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,OAAO;YACL,IAAI;gBACF;oBAAE,OAAO;wBAAE,KAAK,OAAO,OAAO,CAAC,MAAM,CAAC,QAAQ;oBAAC;gBAAE;gBACjD;oBAAE,OAAO;gBAAE;aACZ;QACH;QACA,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,OAAO;QAAM;IAC1B;AACF;AAEO,eAAe;IACpB,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;QACnC,OAAO;YACL,UAAU;YACV,WAAW;QACb;QACA,SAAS;YACP,aAAa;QACf;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAGO,SAAS,iBAAiB,WAA0B;IACzD,IAAI,CAAC,aAAa,OAAO,EAAE;IAC3B,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO,EAAE;IACX;AACF;AAGO,SAAS,eAAe,KAAa,EAAE,QAAgB;IAC5D,IAAI,UAAU,GAAG,OAAO;QAAE,QAAQ;QAAgB,OAAO;QAAM,OAAO;IAAM;IAC5E,IAAI,SAAS,UAAU,OAAO;QAAE,QAAQ;QAAa,OAAO;QAAQ,OAAO;IAAS;IACpF,OAAO;QAAE,QAAQ;QAAY,OAAO;QAAQ,OAAO;IAAQ;AAC7D", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/api/products/search/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\nimport { prisma, parseIngredients } from '@/lib/database';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const query = searchParams.get('q') || '';\n    const category = searchParams.get('category') || '';\n    const sortBy = searchParams.get('sortBy') || 'createdAt';\n    const sortOrder = searchParams.get('sortOrder') || 'desc';\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '12');\n    \n    const skip = (page - 1) * limit;\n    \n    // 构建搜索条件\n    const where: any = {\n      isActive: true, // 只搜索上架的产品\n    };\n    \n    // 添加搜索关键词条件\n    if (query) {\n      where.OR = [\n        { name: { contains: query, mode: 'insensitive' } },\n        { description: { contains: query, mode: 'insensitive' } },\n        { ingredients: { contains: query, mode: 'insensitive' } },\n      ];\n    }\n    \n    // 添加分类筛选\n    if (category && category !== 'all') {\n      where.productTypeId = category;\n    }\n    \n    // 构建排序条件\n    const orderBy: any = {};\n    if (sortBy === 'price') {\n      orderBy.price = sortOrder;\n    } else if (sortBy === 'name') {\n      orderBy.name = sortOrder;\n    } else if (sortBy === 'publishedAt') {\n      orderBy.publishedAt = sortOrder;\n    } else {\n      orderBy.createdAt = sortOrder;\n    }\n    \n    // 执行搜索\n    const [products, total] = await Promise.all([\n      prisma.product.findMany({\n        where,\n        include: {\n          productType: true,\n        },\n        orderBy,\n        skip,\n        take: limit,\n      }),\n      prisma.product.count({ where }),\n    ]);\n    \n    // 转换数据格式\n    const formattedProducts = products.map(product => ({\n      ...product,\n      category: product.productTypeId,\n      ingredients: parseIngredients(product.ingredients),\n      stock: product.stock || 0,\n      minStock: product.minStock || 5,\n      isActive: product.isActive !== false,\n      publishedAt: product.publishedAt?.toISOString() || null,\n    }));\n    \n    return Response.json({\n      products: formattedProducts,\n      pagination: {\n        page,\n        limit,\n        total,\n        totalPages: Math.ceil(total / limit),\n        hasNext: page * limit < total,\n        hasPrev: page > 1,\n      },\n    });\n  } catch (error) {\n    console.error('Failed to search products:', error);\n    return Response.json({ error: 'Failed to search products' }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AACA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC,QAAQ;QACvC,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QACjD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QACnD,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QAEpD,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,SAAS;QACT,MAAM,QAAa;YACjB,UAAU;QACZ;QAEA,YAAY;QACZ,IAAI,OAAO;YACT,MAAM,EAAE,GAAG;gBACT;oBAAE,MAAM;wBAAE,UAAU;wBAAO,MAAM;oBAAc;gBAAE;gBACjD;oBAAE,aAAa;wBAAE,UAAU;wBAAO,MAAM;oBAAc;gBAAE;gBACxD;oBAAE,aAAa;wBAAE,UAAU;wBAAO,MAAM;oBAAc;gBAAE;aACzD;QACH;QAEA,SAAS;QACT,IAAI,YAAY,aAAa,OAAO;YAClC,MAAM,aAAa,GAAG;QACxB;QAEA,SAAS;QACT,MAAM,UAAe,CAAC;QACtB,IAAI,WAAW,SAAS;YACtB,QAAQ,KAAK,GAAG;QAClB,OAAO,IAAI,WAAW,QAAQ;YAC5B,QAAQ,IAAI,GAAG;QACjB,OAAO,IAAI,WAAW,eAAe;YACnC,QAAQ,WAAW,GAAG;QACxB,OAAO;YACL,QAAQ,SAAS,GAAG;QACtB;QAEA,OAAO;QACP,MAAM,CAAC,UAAU,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC1C,wHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACtB;gBACA,SAAS;oBACP,aAAa;gBACf;gBACA;gBACA;gBACA,MAAM;YACR;YACA,wHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBAAE;YAAM;SAC9B;QAED,SAAS;QACT,MAAM,oBAAoB,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBACjD,GAAG,OAAO;gBACV,UAAU,QAAQ,aAAa;gBAC/B,aAAa,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,WAAW;gBACjD,OAAO,QAAQ,KAAK,IAAI;gBACxB,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,UAAU,QAAQ,QAAQ,KAAK;gBAC/B,aAAa,QAAQ,WAAW,EAAE,iBAAiB;YACrD,CAAC;QAED,OAAO,SAAS,IAAI,CAAC;YACnB,UAAU;YACV,YAAY;gBACV;gBACA;gBACA;gBACA,YAAY,KAAK,IAAI,CAAC,QAAQ;gBAC9B,SAAS,OAAO,QAAQ;gBACxB,SAAS,OAAO;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,SAAS,IAAI,CAAC;YAAE,OAAO;QAA4B,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}