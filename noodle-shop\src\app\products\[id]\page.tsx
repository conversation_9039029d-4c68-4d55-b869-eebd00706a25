import { getProductById, getProducts, parseIngredients } from '@/lib/database';
import WeatherWidget from '@/components/WeatherWidget';
import Link from 'next/link';
import Image from 'next/image';
import { notFound } from 'next/navigation';

interface ProductDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function ProductDetailPage({ params }: ProductDetailPageProps) {
  const { id } = await params;
  const rawProduct = await getProductById(id);

  if (!rawProduct) {
    notFound();
  }

  // 转换产品数据格式以保持兼容性
  const product = {
    ...rawProduct,
    category: rawProduct.productTypeId,
    description: rawProduct.description || '',
    ingredients: parseIngredients(rawProduct.ingredients),
    image: rawProduct.image || '',
    stock: rawProduct.stock || 0,
    minStock: rawProduct.minStock || 5,
    isActive: rawProduct.isActive !== false,
    publishedAt: rawProduct.publishedAt?.toISOString() || null,
    createdAt: rawProduct.createdAt.toISOString(),
    updatedAt: rawProduct.updatedAt.toISOString(),
  };

  const spicyIcons = '🌶️'.repeat(product.spicyLevel);

  // 使用产品类型名称而不是硬编码的类别
  const categoryText = rawProduct.productType?.name || '未分类';

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-2xl font-bold text-orange-600 hover:text-orange-700">
                香香面条店
              </Link>
              <span className="text-gray-600">|</span>
              <Link href="/products" className="text-gray-600 hover:text-orange-600">
                菜品列表
              </Link>
            </div>
            <WeatherWidget />
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2">
            <div className="relative h-96 lg:h-auto">
              <Image
                src={product.image}
                alt={product.name}
                fill
                className="object-cover"
              />
            </div>
            
            <div className="p-8">
              <div className="flex items-center justify-between mb-4">
                <h1 className="text-3xl font-bold text-gray-800">{product.name}</h1>
                <span className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm">
                  {categoryText}
                </span>
              </div>
              
              <p className="text-gray-600 text-lg mb-6">{product.description}</p>
              
              <div className="grid grid-cols-2 gap-6 mb-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">价格</h3>
                  <span className="text-3xl font-bold text-orange-600">¥{product.price}</span>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-2">辣度</h3>
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl">{spicyIcons || '不辣'}</span>
                    <span className="text-gray-600">({product.spicyLevel}/5)</span>
                  </div>
                </div>
              </div>
              
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-3">主要配料</h3>
                <div className="flex flex-wrap gap-2">
                  {product.ingredients.map((ingredient, index) => (
                    <span
                      key={index}
                      className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm"
                    >
                      {ingredient}
                    </span>
                  ))}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-800 mb-2">库存状态</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">当前库存:</span>
                      <span className={`font-bold ${
                        product.stock === 0 ? 'text-red-600' :
                        product.stock <= product.minStock ? 'text-yellow-600' : 'text-green-600'
                      }`}>
                        {product.stock} 件
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">最低库存:</span>
                      <span className="text-gray-700">{product.minStock} 件</span>
                    </div>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      product.available
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {product.available ? '✓ 有货' : '✗ 暂缺'}
                    </span>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-800 mb-2">产品类型</h4>
                  <span className="text-orange-600 font-medium">{categoryText}</span>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-800 mb-2">销售状态</h4>
                  <div className="space-y-2">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      product.isActive
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {product.isActive ? '✓ 已上架' : '✗ 已下架'}
                    </span>
                    {!product.isActive && (
                      <p className="text-sm text-gray-500">该产品暂时下架，不可购买</p>
                    )}
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg mb-6">
                <h4 className="font-semibold text-blue-800 mb-2">💡 制作建议</h4>
                <p className="text-blue-700 text-sm">
                  {product.spicyLevel > 0
                    ? `这是一道${product.spicyLevel}级辣度的美食，建议搭配清淡汤品食用。`
                    : '口味清淡，适合全家老少享用。'}
                  新鲜制作，建议尽快食用以保持最佳口感。
                </p>
              </div>

              <div className="border-t pt-6 mb-6">
                <h4 className="font-semibold text-gray-800 mb-3">产品信息</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  {product.publishedAt && (
                    <div>
                      <span className="text-gray-500">发布时间：</span>
                      <span className="text-gray-700 font-medium">{new Date(product.publishedAt).toLocaleDateString('zh-CN')}</span>
                    </div>
                  )}
                  <div>
                    <span className="text-gray-500">创建时间：</span>
                    <span className="text-gray-700">{new Date(product.createdAt).toLocaleDateString('zh-CN')}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">更新时间：</span>
                    <span className="text-gray-700">{new Date(product.updatedAt).toLocaleDateString('zh-CN')}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-4">
                <button
                  className={`flex-1 py-3 px-6 rounded-lg font-semibold transition-colors ${
                    product.available
                      ? 'bg-orange-500 text-white hover:bg-orange-600'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                  disabled={!product.available}
                >
                  {product.available ? '立即订购' : '暂时缺货'}
                </button>
                
                <Link
                  href="/products"
                  className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:border-orange-500 hover:text-orange-600 transition-colors"
                >
                  返回列表
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

export async function generateStaticParams() {
  const products = await getProducts();
  return products.map((product) => ({
    id: product.id,
  }));
}