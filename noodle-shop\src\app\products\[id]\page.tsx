import { getProductById, getProducts } from '@/lib/data';
import WeatherWidget from '@/components/WeatherWidget';
import Link from 'next/link';
import Image from 'next/image';
import { notFound } from 'next/navigation';

interface ProductDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function ProductDetailPage({ params }: ProductDetailPageProps) {
  const { id } = await params;
  const product = await getProductById(id);
  
  if (!product) {
    notFound();
  }

  const spicyIcons = '🌶️'.repeat(product.spicyLevel);
  const categoryText = {
    noodles: '面条',
    pasta: '面片',
    soup: '汤面'
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-2xl font-bold text-orange-600 hover:text-orange-700">
                香香面条店
              </Link>
              <span className="text-gray-600">|</span>
              <Link href="/products" className="text-gray-600 hover:text-orange-600">
                菜品列表
              </Link>
            </div>
            <WeatherWidget />
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2">
            <div className="relative h-96 lg:h-auto">
              <Image
                src={product.image}
                alt={product.name}
                fill
                className="object-cover"
              />
            </div>
            
            <div className="p-8">
              <div className="flex items-center justify-between mb-4">
                <h1 className="text-3xl font-bold text-gray-800">{product.name}</h1>
                <span className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm">
                  {categoryText[product.category]}
                </span>
              </div>
              
              <p className="text-gray-600 text-lg mb-6">{product.description}</p>
              
              <div className="grid grid-cols-2 gap-6 mb-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">价格</h3>
                  <span className="text-3xl font-bold text-orange-600">¥{product.price}</span>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-2">辣度</h3>
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl">{spicyIcons || '不辣'}</span>
                    <span className="text-gray-600">({product.spicyLevel}/5)</span>
                  </div>
                </div>
              </div>
              
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-3">主要配料</h3>
                <div className="flex flex-wrap gap-2">
                  {product.ingredients.map((ingredient, index) => (
                    <span
                      key={index}
                      className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm"
                    >
                      {ingredient}
                    </span>
                  ))}
                </div>
              </div>
              
              <div className="flex items-center justify-between mb-6">
                <div>
                  <span className="text-sm text-gray-500">库存状态：</span>
                  <span className={`ml-2 font-medium ${product.available ? 'text-green-600' : 'text-red-600'}`}>
                    {product.available ? '有货' : '暂缺'}
                  </span>
                </div>
              </div>
              
              <div className="flex space-x-4">
                <button
                  className={`flex-1 py-3 px-6 rounded-lg font-semibold transition-colors ${
                    product.available
                      ? 'bg-orange-500 text-white hover:bg-orange-600'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                  disabled={!product.available}
                >
                  {product.available ? '立即订购' : '暂时缺货'}
                </button>
                
                <Link
                  href="/products"
                  className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:border-orange-500 hover:text-orange-600 transition-colors"
                >
                  返回列表
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

export async function generateStaticParams() {
  const products = await getProducts();
  return products.map((product) => ({
    id: product.id,
  }));
}