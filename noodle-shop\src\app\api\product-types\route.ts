import { NextRequest, NextResponse } from 'next/server';
import { ProductType } from '@/types';
import productTypesData from '@/data/productTypes.json';

// 模拟数据库存储
let productTypes: ProductType[] = [...productTypesData];

// GET - 获取所有产品类型
export async function GET() {
  try {
    // 按显示顺序排序
    const sortedTypes = productTypes
      .filter(type => type.isActive)
      .sort((a, b) => a.displayOrder - b.displayOrder);
    
    return NextResponse.json(sortedTypes);
  } catch (error) {
    return NextResponse.json(
      { error: '获取产品类型失败' },
      { status: 500 }
    );
  }
}

// POST - 创建新产品类型
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证必填字段
    if (!body.name || !body.description) {
      return NextResponse.json(
        { error: '名称和描述为必填项' },
        { status: 400 }
      );
    }

    // 生成新的产品类型
    const newProductType: ProductType = {
      id: `type-${Date.now()}`,
      name: body.name,
      description: body.description,
      displayOrder: body.displayOrder || productTypes.length + 1,
      isActive: body.isActive !== false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    productTypes.push(newProductType);

    return NextResponse.json(newProductType, { status: 201 });
  } catch (error) {
    return NextResponse.json(
      { error: '创建产品类型失败' },
      { status: 500 }
    );
  }
}