{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/admin/products/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Product, ProductType } from '@/types';\nimport Link from 'next/link';\nimport Image from 'next/image';\n\nexport default function AdminProductsPage() {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [productTypes, setProductTypes] = useState<ProductType[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filter, setFilter] = useState('all');\n\n  useEffect(() => {\n    fetchProducts();\n    fetchProductTypes();\n  }, []);\n\n  const fetchProducts = async () => {\n    try {\n      const response = await fetch('/api/products');\n      const data = await response.json();\n      setProducts(data);\n    } catch (error) {\n      console.error('Failed to fetch products:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchProductTypes = async () => {\n    try {\n      const response = await fetch('/api/product-types');\n      const data = await response.json();\n      setProductTypes(data);\n    } catch (error) {\n      console.error('Failed to fetch product types:', error);\n    }\n  };\n\n  const deleteProduct = async (id: string) => {\n    if (!confirm('确定要删除这个菜品吗？')) return;\n    \n    try {\n      const response = await fetch(`/api/products/${id}`, {\n        method: 'DELETE',\n      });\n      \n      if (response.ok) {\n        setProducts(products.filter(p => p.id !== id));\n      } else {\n        alert('删除失败');\n      }\n    } catch (error) {\n      console.error('Failed to delete product:', error);\n      alert('删除失败');\n    }\n  };\n\n  const toggleAvailability = async (product: Product) => {\n    try {\n      const response = await fetch(`/api/products/${product.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...product,\n          available: !product.available,\n        }),\n      });\n      \n      if (response.ok) {\n        const updatedProduct = await response.json();\n        setProducts(products.map(p => \n          p.id === product.id ? updatedProduct : p\n        ));\n      }\n    } catch (error) {\n      console.error('Failed to update product:', error);\n    }\n  };\n\n  const filteredProducts = products.filter(product => {\n    if (filter === 'all') return true;\n    return product.productTypeId === filter;\n  });\n\n  // 动态生成类别选项\n  const categories: Record<string, string> = {\n    all: '全部',\n    ...productTypes.reduce((acc, type) => ({\n      ...acc,\n      [type.id]: type.name\n    }), {})\n  };\n\n  // 获取产品类型名称\n  const getProductTypeName = (productTypeId: string) => {\n    const type = productTypes.find(t => t.id === productTypeId);\n    return type ? type.name : '未分类';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">正在加载...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/admin\" className=\"text-2xl font-bold text-orange-600 hover:text-orange-700\">\n                管理后台\n              </Link>\n              <span className=\"text-gray-600\">菜品管理</span>\n            </div>\n            <Link\n              href=\"/admin/products/new\"\n              className=\"bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors\"\n            >\n              添加新菜品\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"mb-6\">\n          <div className=\"flex flex-wrap gap-2\">\n            {Object.entries(categories).map(([key, label]) => (\n              <button\n                key={key}\n                onClick={() => setFilter(key)}\n                className={`px-4 py-2 rounded-lg transition-colors ${\n                  filter === key\n                    ? 'bg-orange-500 text-white'\n                    : 'bg-white border border-gray-200 text-gray-700 hover:border-orange-500'\n                }`}\n              >\n                {label}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    菜品\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    类别\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    价格\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    状态\n                  </th>\n                  <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    操作\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {filteredProducts.map((product) => (\n                  <tr key={product.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <div className=\"flex-shrink-0 h-16 w-16\">\n                          <Image\n                            src={product.image}\n                            alt={product.name}\n                            width={64}\n                            height={64}\n                            className=\"h-16 w-16 rounded-lg object-cover\"\n                          />\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {product.name}\n                          </div>\n                          <div className=\"text-sm text-gray-500 line-clamp-1\">\n                            {product.description}\n                          </div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800\">\n                        {getProductTypeName(product.productTypeId)}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      ¥{product.price}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <button\n                        onClick={() => toggleAvailability(product)}\n                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${\n                          product.available\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                        }`}\n                      >\n                        {product.available ? '有货' : '缺货'}\n                      </button>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                      <div className=\"flex justify-end space-x-2\">\n                        <Link\n                          href={`/admin/products/edit/${product.id}`}\n                          className=\"text-orange-600 hover:text-orange-900\"\n                        >\n                          编辑\n                        </Link>\n                        <button\n                          onClick={() => deleteProduct(product.id)}\n                          className=\"text-red-600 hover:text-red-900\"\n                        >\n                          删除\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {filteredProducts.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">🍜</div>\n            <h2 className=\"text-xl text-gray-600\">暂无菜品</h2>\n            <p className=\"text-gray-500 mt-2\">\n              {filter !== 'all' ? '该分类下暂无菜品' : '请添加新菜品'}\n            </p>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;;;AALA;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;YACA;QACF;sCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,QAAQ,gBAAgB;QAE7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,iBAAmB,OAAH,KAAM;gBAClD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC5C,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,iBAA2B,OAAX,QAAQ,EAAE,GAAI;gBAC1D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,OAAO;oBACV,WAAW,CAAC,QAAQ,SAAS;gBAC/B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,iBAAiB,MAAM,SAAS,IAAI;gBAC1C,YAAY,SAAS,GAAG,CAAC,CAAA,IACvB,EAAE,EAAE,KAAK,QAAQ,EAAE,GAAG,iBAAiB;YAE3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,IAAI,WAAW,OAAO,OAAO;QAC7B,OAAO,QAAQ,aAAa,KAAK;IACnC;IAEA,WAAW;IACX,MAAM,aAAqC;QACzC,KAAK;QACL,GAAG,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,CAAC;gBACrC,GAAG,GAAG;gBACN,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,IAAI;YACtB,CAAC,GAAG,CAAC,EAAE;IACT;IAEA,WAAW;IACX,MAAM,qBAAqB,CAAC;QAC1B,MAAM,OAAO,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,OAAO,OAAO,KAAK,IAAI,GAAG;IAC5B;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAA2D;;;;;;kDAGzF,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAOP,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC;oCAAC,CAAC,KAAK,MAAM;qDAC3C,6LAAC;oCAEC,SAAS,IAAM,UAAU;oCACzB,WAAW,AAAC,0CAIX,OAHC,WAAW,MACP,6BACA;8CAGL;mCARI;;;;;;;;;;;;;;;;kCAcb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAkF;;;;;;;;;;;;;;;;;kDAKpG,6LAAC;wCAAM,WAAU;kDACd,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;gDAAoB,WAAU;;kEAC7B,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wEACJ,KAAK,QAAQ,KAAK;wEAClB,KAAK,QAAQ,IAAI;wEACjB,OAAO;wEACP,QAAQ;wEACR,WAAU;;;;;;;;;;;8EAGd,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACZ,QAAQ,IAAI;;;;;;sFAEf,6LAAC;4EAAI,WAAU;sFACZ,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;kEAK5B,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAU;sEACb,mBAAmB,QAAQ,aAAa;;;;;;;;;;;kEAG7C,6LAAC;wDAAG,WAAU;;4DAAoD;4DAC9D,QAAQ,KAAK;;;;;;;kEAEjB,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DACC,SAAS,IAAM,mBAAmB;4DAClC,WAAW,AAAC,iEAIX,OAHC,QAAQ,SAAS,GACb,gCACA;sEAGL,QAAQ,SAAS,GAAG,OAAO;;;;;;;;;;;kEAGhC,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+JAAA,CAAA,UAAI;oEACH,MAAM,AAAC,wBAAkC,OAAX,QAAQ,EAAE;oEACxC,WAAU;8EACX;;;;;;8EAGD,6LAAC;oEACC,SAAS,IAAM,cAAc,QAAQ,EAAE;oEACvC,WAAU;8EACX;;;;;;;;;;;;;;;;;;+CArDE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;oBAiE5B,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,6LAAC;gCAAE,WAAU;0CACV,WAAW,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;GAxPwB;KAAA", "debugId": null}}]}