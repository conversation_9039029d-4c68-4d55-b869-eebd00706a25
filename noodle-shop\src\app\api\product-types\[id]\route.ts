import { NextRequest, NextResponse } from 'next/server';
import { ProductType } from '@/types';
import productTypesData from '@/data/productTypes.json';

// 模拟数据库存储
let productTypes: ProductType[] = [...productTypesData];

// GET - 获取单个产品类型
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const productType = productTypes.find(type => type.id === params.id);
    
    if (!productType) {
      return NextResponse.json(
        { error: '产品类型不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json(productType);
  } catch (error) {
    return NextResponse.json(
      { error: '获取产品类型失败' },
      { status: 500 }
    );
  }
}

// PUT - 更新产品类型
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const index = productTypes.findIndex(type => type.id === params.id);
    
    if (index === -1) {
      return NextResponse.json(
        { error: '产品类型不存在' },
        { status: 404 }
      );
    }

    // 更新产品类型
    productTypes[index] = {
      ...productTypes[index],
      ...body,
      id: params.id, // 确保 ID 不被修改
      updatedAt: new Date().toISOString()
    };

    return NextResponse.json(productTypes[index]);
  } catch (error) {
    return NextResponse.json(
      { error: '更新产品类型失败' },
      { status: 500 }
    );
  }
}

// DELETE - 删除产品类型
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const index = productTypes.findIndex(type => type.id === params.id);
    
    if (index === -1) {
      return NextResponse.json(
        { error: '产品类型不存在' },
        { status: 404 }
      );
    }

    // 软删除：将 isActive 设为 false
    productTypes[index].isActive = false;
    productTypes[index].updatedAt = new Date().toISOString();

    return NextResponse.json({ message: '产品类型已删除' });
  } catch (error) {
    return NextResponse.json(
      { error: '删除产品类型失败' },
      { status: 500 }
    );
  }
}