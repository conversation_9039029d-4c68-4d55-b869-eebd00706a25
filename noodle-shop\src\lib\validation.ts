// 数据验证工具

import { z } from 'zod';
import { ValidationError } from './errors';

// 基础验证规则
export const baseValidation = {
  id: z.string().min(1, 'ID不能为空'),
  name: z.string().min(1, '名称不能为空').max(100, '名称不能超过100个字符'),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
  price: z.number().positive('价格必须大于0'),
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入有效的手机号码').optional(),
  email: z.string().email('请输入有效的邮箱地址').optional(),
  url: z.string().url('请输入有效的URL').optional(),
};

// 产品验证模式
export const ProductValidationSchema = z.object({
  id: baseValidation.id.optional(),
  name: baseValidation.name,
  description: baseValidation.description,
  price: baseValidation.price,
  productTypeId: baseValidation.id,
  image: baseValidation.url.optional(),
  ingredients: z.array(z.string()).default([]),
  spicyLevel: z.number().int().min(0).max(5).default(0),
  available: z.boolean().default(true),
  stock: z.number().int().min(0).default(0),
  minStock: z.number().int().min(0).default(5),
  isActive: z.boolean().default(true),
  publishedAt: z.string().datetime().optional().nullable(),
});

// 产品类型验证模式
export const ProductTypeValidationSchema = z.object({
  id: baseValidation.id.optional(),
  name: baseValidation.name,
  description: baseValidation.description,
  displayOrder: z.number().int().min(0).default(0),
  isActive: z.boolean().default(true),
});

// 门店验证模式
export const StoreValidationSchema = z.object({
  id: baseValidation.id.optional(),
  name: baseValidation.name,
  address: z.string().min(1, '地址不能为空').max(200, '地址不能超过200个字符'),
  phone: baseValidation.phone,
  latitude: z.number().min(-90).max(90).optional().nullable(),
  longitude: z.number().min(-180).max(180).optional().nullable(),
  openTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式(HH:MM)').optional(),
  closeTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式(HH:MM)').optional(),
  isOpen: z.boolean().default(true),
  image: baseValidation.url.optional(),
});

// 搜索参数验证模式
export const SearchParamsSchema = z.object({
  q: z.string().max(100, '搜索关键词不能超过100个字符').optional(),
  category: z.string().optional(),
  sortBy: z.enum(['createdAt', 'publishedAt', 'name', 'price']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(12),
});

// 验证函数
export function validateData<T>(schema: z.ZodSchema<T>, data: unknown): T {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const messages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new ValidationError(messages.join(', '));
    }
    throw error;
  }
}

// 安全解析（不抛出错误）
export function safeValidateData<T>(schema: z.ZodSchema<T>, data: unknown): {
  success: boolean;
  data?: T;
  errors?: string[];
} {
  try {
    const result = schema.safeParse(data);
    if (result.success) {
      return { success: true, data: result.data };
    } else {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      return { success: false, errors };
    }
  } catch (error) {
    return { success: false, errors: ['验证过程中发生错误'] };
  }
}

// 部分验证（用于更新操作）
export function validatePartialData<T>(schema: z.ZodSchema<T>, data: unknown): Partial<T> {
  const partialSchema = schema.partial();
  return validateData(partialSchema, data);
}

// 自定义验证规则
export const customValidators = {
  // 验证营业时间
  validateBusinessHours: (openTime?: string, closeTime?: string) => {
    if (!openTime || !closeTime) return true;
    
    const open = new Date(`2000-01-01 ${openTime}`);
    const close = new Date(`2000-01-01 ${closeTime}`);
    
    return open < close;
  },

  // 验证库存逻辑
  validateStock: (stock: number, minStock: number) => {
    return stock >= 0 && minStock >= 0 && minStock <= stock;
  },

  // 验证价格范围
  validatePriceRange: (price: number, min: number = 0, max: number = 10000) => {
    return price >= min && price <= max;
  },

  // 验证坐标
  validateCoordinates: (latitude?: number, longitude?: number) => {
    if (latitude === undefined || longitude === undefined) return true;
    return latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180;
  },
};
