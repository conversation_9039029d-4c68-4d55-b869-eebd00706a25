import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { NotFoundError, ValidationError } from './errors';
import { validateData } from './validation';
import {
  ProductValidationSchema,
  ProductTypeValidationSchema,
  StoreValidationSchema
} from './validation';

// 全局Prisma客户端实例
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
});

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Zod验证模式
export const ProductTypeSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1),
  description: z.string().optional(),
  displayOrder: z.number().int().default(0),
  isActive: z.boolean().default(true),
});

export const ProductSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1),
  description: z.string().optional(),
  price: z.number().positive(),
  productTypeId: z.string().min(1),
  image: z.string().optional(),
  ingredients: z.array(z.string()).optional(),
  spicyLevel: z.number().int().min(0).max(5).default(0),
  available: z.boolean().default(true),
  stock: z.number().int().min(0).default(0),
  minStock: z.number().int().min(0).default(5),
  isActive: z.boolean().default(true),
});

export const StoreSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1),
  address: z.string().min(1),
  phone: z.string().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  openTime: z.string().optional(),
  closeTime: z.string().optional(),
  isOpen: z.boolean().default(true),
  image: z.string().optional(),
});

// 产品类型相关操作
export async function getProductTypes() {
  return await prisma.productType.findMany({
    where: { isActive: true },
    orderBy: { displayOrder: 'asc' },
  });
}

export async function getProductTypeById(id: string) {
  return await prisma.productType.findUnique({
    where: { id },
  });
}

export async function createProductType(data: z.infer<typeof ProductTypeSchema>) {
  const validatedData = ProductTypeSchema.parse(data);
  return await prisma.productType.create({
    data: validatedData,
  });
}

export async function updateProductType(id: string, data: Partial<z.infer<typeof ProductTypeSchema>>) {
  return await prisma.productType.update({
    where: { id },
    data,
  });
}

export async function deleteProductType(id: string) {
  // 检查是否有关联的产品
  const productCount = await prisma.product.count({
    where: { productTypeId: id },
  });
  
  if (productCount > 0) {
    throw new Error('无法删除：该产品类型下还有产品');
  }
  
  return await prisma.productType.delete({
    where: { id },
  });
}

// 产品相关操作
export async function getProducts() {
  return await prisma.product.findMany({
    include: {
      productType: true,
    },
    orderBy: { createdAt: 'desc' },
  });
}

export async function getProductById(id: string) {
  return await prisma.product.findUnique({
    where: { id },
    include: {
      productType: true,
    },
  });
}

export async function getProductsByCategory(productTypeId: string) {
  return await prisma.product.findMany({
    where: { productTypeId },
    include: {
      productType: true,
    },
    orderBy: { createdAt: 'desc' },
  });
}

export async function createProduct(data: Omit<z.infer<typeof ProductValidationSchema>, 'id'>) {
  const validatedData = validateData(ProductValidationSchema.omit({ id: true }), data);

  // 检查产品类型是否存在
  const productType = await prisma.productType.findUnique({
    where: { id: validatedData.productTypeId },
  });

  if (!productType) {
    throw new NotFoundError('产品类型');
  }

  // 生成新ID
  const newId = Date.now().toString();

  return await prisma.product.create({
    data: {
      ...validatedData,
      id: newId,
      ingredients: validatedData.ingredients ? JSON.stringify(validatedData.ingredients) : null,
      publishedAt: validatedData.isActive ? new Date() : null,
    },
    include: {
      productType: true,
    },
  });
}

export async function updateProduct(id: string, data: Partial<Omit<z.infer<typeof ProductSchema>, 'id'>>) {
  const updateData: any = { ...data };
  
  if (data.ingredients) {
    updateData.ingredients = JSON.stringify(data.ingredients);
  }
  
  return await prisma.product.update({
    where: { id },
    data: updateData,
    include: {
      productType: true,
    },
  });
}

export async function deleteProduct(id: string) {
  return await prisma.product.delete({
    where: { id },
  });
}

// 门店相关操作
export async function getStores() {
  return await prisma.store.findMany({
    orderBy: { createdAt: 'desc' },
  });
}

export async function getStoreById(id: string) {
  return await prisma.store.findUnique({
    where: { id },
  });
}

export async function createStore(data: Omit<z.infer<typeof StoreSchema>, 'id'>) {
  const validatedData = StoreSchema.omit({ id: true }).parse(data);
  
  // 生成新ID
  const newId = Date.now().toString();
  
  return await prisma.store.create({
    data: {
      ...validatedData,
      id: newId,
    },
  });
}

export async function updateStore(id: string, data: Partial<Omit<z.infer<typeof StoreSchema>, 'id'>>) {
  return await prisma.store.update({
    where: { id },
    data,
  });
}

export async function deleteStore(id: string) {
  return await prisma.store.delete({
    where: { id },
  });
}

// 库存管理相关操作
export async function updateProductStock(id: string, quantity: number) {
  const product = await prisma.product.findUnique({ where: { id } });
  if (!product) throw new Error('产品不存在');

  const newStock = Math.max(0, product.stock + quantity);

  return await prisma.product.update({
    where: { id },
    data: {
      stock: newStock,
      available: newStock > 0, // 自动更新可用状态
    },
    include: {
      productType: true,
    },
  });
}

export async function toggleProductStatus(id: string) {
  const product = await prisma.product.findUnique({ where: { id } });
  if (!product) throw new Error('产品不存在');

  return await prisma.product.update({
    where: { id },
    data: { isActive: !product.isActive },
    include: {
      productType: true,
    },
  });
}

export async function getLowStockProducts() {
  return await prisma.product.findMany({
    where: {
      OR: [
        { stock: { lte: prisma.product.fields.minStock } },
        { stock: 0 },
      ],
    },
    include: {
      productType: true,
    },
    orderBy: { stock: 'asc' },
  });
}

export async function getActiveProducts() {
  return await prisma.product.findMany({
    where: {
      isActive: true,
      available: true,
    },
    include: {
      productType: true,
    },
    orderBy: { createdAt: 'desc' },
  });
}

// 工具函数：解析ingredients JSON字符串
export function parseIngredients(ingredients: string | null): string[] {
  if (!ingredients) return [];
  try {
    return JSON.parse(ingredients);
  } catch {
    return [];
  }
}

// 工具函数：获取库存状态
export function getStockStatus(stock: number, minStock: number) {
  if (stock === 0) return { status: 'out_of_stock', label: '缺货', color: 'red' };
  if (stock <= minStock) return { status: 'low_stock', label: '库存不足', color: 'yellow' };
  return { status: 'in_stock', label: '库存充足', color: 'green' };
}
