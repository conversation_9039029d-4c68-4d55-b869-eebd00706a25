import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const forwarded = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const ip = forwarded?.split(',')[0] || realIp || '127.0.0.1';
    
    if (ip === '127.0.0.1' || ip === '::1') {
      return Response.json({
        ip: '127.0.0.1',
        city: '北京',
        region: '北京市',
        country: '中国',
        latitude: 39.9042,
        longitude: 116.4074
      });
    }

    const response = await fetch(`http://ip-api.com/json/${ip}?lang=zh-CN`);
    const data = await response.json();
    
    if (data.status === 'success') {
      return Response.json({
        ip: data.query,
        city: data.city,
        region: data.regionName,
        country: data.country,
        latitude: data.lat,
        longitude: data.lon
      });
    }

    return Response.json({
      ip: '127.0.0.1',
      city: '北京',
      region: '北京市',
      country: '中国',
      latitude: 39.9042,
      longitude: 116.4074
    });
  } catch (error) {
    console.error('Location API error:', error);
    return Response.json({
      ip: '127.0.0.1',
      city: '北京',
      region: '北京市',
      country: '中国',
      latitude: 39.9042,
      longitude: 116.4074
    });
  }
}