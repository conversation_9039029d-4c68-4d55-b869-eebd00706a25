// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model ProductType {
  id           String   @id
  name         String
  description  String?
  displayOrder Int      @default(0) @map("display_order")
  isActive     Boolean  @default(true) @map("is_active")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  products Product[]

  @@map("product_types")
}

model Product {
  id            String    @id
  name          String
  description   String?
  price         Float
  productTypeId String    @map("product_type_id")
  image         String?
  ingredients   String? // JSON string
  spicyLevel    Int       @default(0) @map("spicy_level")
  available     Boolean   @default(true)
  stock         Int       @default(0) // 库存数量
  minStock      Int       @default(5) @map("min_stock") // 最低库存警告
  isActive      Boolean   @default(true) @map("is_active") // 上下架状态
  publishedAt   DateTime? @map("published_at") // 发布日期
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  // Relations
  productType ProductType @relation(fields: [productTypeId], references: [id])

  @@map("products")
}

model Store {
  id        String   @id
  name      String
  address   String
  phone     String?
  latitude  Float?
  longitude Float?
  openTime  String?  @map("open_time")
  closeTime String?  @map("close_time")
  isOpen    Boolean  @default(true) @map("is_open")
  image     String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("stores")
}
