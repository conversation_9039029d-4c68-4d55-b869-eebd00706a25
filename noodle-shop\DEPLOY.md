# Cloudflare Pages 部署指南

## 一键部署步骤

### 1. 安装 Wrangler CLI
```bash
npm install -g wrangler
```

### 2. 登录 Cloudflare 账户
```bash
npm run cf:login
```

### 3. 构建项目
```bash
npm run build:cf
```

### 4. 部署到 Cloudflare Pages
```bash
npm run deploy
```

## 可用命令

- `npm run build:cf` - 构建项目用于 Cloudflare Pages
- `npm run deploy` - 部署到生产环境
- `npm run deploy:preview` - 部署到预览环境
- `npm run cf:login` - 登录 Cloudflare 账户
- `npm run cf:whoami` - 查看当前登录用户

## 配置说明

- `wrangler.toml` - Cloudflare Pages 配置文件
- `next.config.ts` - 已配置静态导出模式
- 输出目录：`out/`

## 注意事项

1. 确保已登录 Cloudflare 账户
2. 项目将以静态站点形式部署
3. 首次部署可能需要几分钟时间
4. 部署完成后会提供访问链接

## 自定义域名

部署完成后，可在 Cloudflare Dashboard 中配置自定义域名。