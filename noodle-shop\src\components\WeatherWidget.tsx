'use client';

import { useState, useEffect } from 'react';
import { WeatherInfo, LocationInfo } from '@/types';

export default function WeatherWidget() {
  const [weather, setWeather] = useState<WeatherInfo | null>(null);
  const [location, setLocation] = useState<LocationInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchLocationAndWeather = async () => {
      try {
        const locationResponse = await fetch('/api/location');
        const locationData = await locationResponse.json();
        setLocation(locationData);

        const weatherResponse = await fetch(`/api/weather?lat=${locationData.latitude}&lon=${locationData.longitude}`);
        const weatherData = await weatherResponse.json();
        setWeather(weatherData);
      } catch (error) {
        console.error('Failed to fetch location or weather:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLocationAndWeather();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center space-x-2 text-gray-500">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
        <span>加载中...</span>
      </div>
    );
  }

  if (!weather || !location) {
    return (
      <div className="text-gray-500 text-sm">
        天气信息暂不可用
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-4 text-sm">
      <div className="flex items-center space-x-1">
        <span className="text-gray-600">IP:</span>
        <span className="font-mono text-orange-600">{location.ip}</span>
      </div>
      <div className="flex items-center space-x-2 bg-blue-50 px-3 py-1 rounded-full">
        <span className="text-blue-600">{location.city}</span>
        <span className="text-gray-500">|</span>
        <span className="text-blue-600">{weather.temperature}°C</span>
        <span className="text-gray-600">{weather.description}</span>
      </div>
    </div>
  );
}