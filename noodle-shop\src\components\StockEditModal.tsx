'use client';

import { useState, useEffect } from 'react';

interface Product {
  id: string;
  name: string;
  stock: number;
  minStock: number;
  available: boolean;
  isActive: boolean;
}

interface StockEditModalProps {
  product: Product | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (productId: string, newStock: number, newMinStock: number) => Promise<void>;
}

export default function StockEditModal({ product, isOpen, onClose, onSave }: StockEditModalProps) {
  const [stock, setStock] = useState(0);
  const [minStock, setMinStock] = useState(0);
  const [loading, setSaving] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (product) {
      setStock(product.stock);
      setMinStock(product.minStock);
      setError('');
    }
  }, [product]);

  const handleSave = async () => {
    if (!product) return;

    // 验证输入
    if (stock < 0) {
      setError('库存数量不能为负数');
      return;
    }

    if (minStock < 0) {
      setError('最低库存不能为负数');
      return;
    }

    if (minStock > stock) {
      setError('最低库存不能大于当前库存');
      return;
    }

    setSaving(true);
    setError('');

    try {
      await onSave(product.id, stock, minStock);
      onClose();
    } catch (error) {
      setError('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  const handleStockChange = (value: string) => {
    const num = parseInt(value) || 0;
    setStock(Math.max(0, num));
  };

  const handleMinStockChange = (value: string) => {
    const num = parseInt(value) || 0;
    setMinStock(Math.max(0, num));
  };

  const adjustStock = (amount: number) => {
    setStock(prev => Math.max(0, prev + amount));
  };

  if (!isOpen || !product) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">编辑库存</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={loading}
          >
            ✕
          </button>
        </div>

        <div className="mb-4">
          <h3 className="font-medium text-gray-700 mb-2">{product.name}</h3>
          <div className="text-sm text-gray-500">
            当前状态: {product.isActive ? '已上架' : '已下架'} | {product.available ? '有货' : '缺货'}
          </div>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        <div className="space-y-4">
          {/* 当前库存 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              当前库存
            </label>
            <div className="flex items-center space-x-2">
              <button
                type="button"
                onClick={() => adjustStock(-10)}
                className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
                disabled={loading}
              >
                -10
              </button>
              <button
                type="button"
                onClick={() => adjustStock(-1)}
                className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
                disabled={loading}
              >
                -1
              </button>
              <input
                type="number"
                value={stock}
                onChange={(e) => handleStockChange(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                min="0"
                disabled={loading}
              />
              <button
                type="button"
                onClick={() => adjustStock(1)}
                className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-sm"
                disabled={loading}
              >
                +1
              </button>
              <button
                type="button"
                onClick={() => adjustStock(10)}
                className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-sm"
                disabled={loading}
              >
                +10
              </button>
            </div>
          </div>

          {/* 最低库存 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              最低库存警告线
            </label>
            <input
              type="number"
              value={minStock}
              onChange={(e) => handleMinStockChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              min="0"
              disabled={loading}
            />
            <p className="text-xs text-gray-500 mt-1">
              当库存低于此数值时会显示警告
            </p>
          </div>

          {/* 库存状态预览 */}
          <div className="p-3 bg-gray-50 rounded-md">
            <h4 className="text-sm font-medium text-gray-700 mb-2">状态预览</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>库存状态:</span>
                <span className={`font-medium ${
                  stock === 0 ? 'text-red-600' : 
                  stock <= minStock ? 'text-yellow-600' : 'text-green-600'
                }`}>
                  {stock === 0 ? '缺货' : stock <= minStock ? '库存不足' : '库存充足'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>可售状态:</span>
                <span className={stock > 0 ? 'text-green-600' : 'text-red-600'}>
                  {stock > 0 ? '可售' : '不可售'}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
            disabled={loading}
          >
            取消
          </button>
          <button
            onClick={handleSave}
            disabled={loading}
            className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors disabled:opacity-50"
          >
            {loading ? '保存中...' : '保存'}
          </button>
        </div>
      </div>
    </div>
  );
}
