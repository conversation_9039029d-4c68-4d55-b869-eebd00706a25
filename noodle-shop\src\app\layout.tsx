import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { NotificationProvider } from "@/components/Notification";

const inter = Inter({
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "香香面条店 - 正宗手工面条面片",
  description: "提供最正宗的手工面条、面片，新鲜食材，传统工艺",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className={`${inter.className} antialiased`}>
        <NotificationProvider>
          {children}
        </NotificationProvider>
      </body>
    </html>
  );
}
