{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/api/location/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const forwarded = request.headers.get('x-forwarded-for');\n    const realIp = request.headers.get('x-real-ip');\n    const ip = forwarded?.split(',')[0] || realIp || '127.0.0.1';\n    \n    if (ip === '127.0.0.1' || ip === '::1') {\n      return Response.json({\n        ip: '127.0.0.1',\n        city: '北京',\n        region: '北京市',\n        country: '中国',\n        latitude: 39.9042,\n        longitude: 116.4074\n      });\n    }\n\n    const response = await fetch(`http://ip-api.com/json/${ip}?lang=zh-CN`);\n    const data = await response.json();\n    \n    if (data.status === 'success') {\n      return Response.json({\n        ip: data.query,\n        city: data.city,\n        region: data.regionName,\n        country: data.country,\n        latitude: data.lat,\n        longitude: data.lon\n      });\n    }\n\n    return Response.json({\n      ip: '127.0.0.1',\n      city: '北京',\n      region: '北京市',\n      country: '中国',\n      latitude: 39.9042,\n      longitude: 116.4074\n    });\n  } catch (error) {\n    console.error('Location API error:', error);\n    return Response.json({\n      ip: '127.0.0.1',\n      city: '北京',\n      region: '北京市',\n      country: '中国',\n      latitude: 39.9042,\n      longitude: 116.4074\n    });\n  }\n}"], "names": [], "mappings": ";;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;QACtC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,KAAK,WAAW,MAAM,IAAI,CAAC,EAAE,IAAI,UAAU;QAEjD,IAAI,OAAO,eAAe,OAAO,OAAO;YACtC,OAAO,SAAS,IAAI,CAAC;gBACnB,IAAI;gBACJ,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,UAAU;gBACV,WAAW;YACb;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,GAAG,WAAW,CAAC;QACtE,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,MAAM,KAAK,WAAW;YAC7B,OAAO,SAAS,IAAI,CAAC;gBACnB,IAAI,KAAK,KAAK;gBACd,MAAM,KAAK,IAAI;gBACf,QAAQ,KAAK,UAAU;gBACvB,SAAS,KAAK,OAAO;gBACrB,UAAU,KAAK,GAAG;gBAClB,WAAW,KAAK,GAAG;YACrB;QACF;QAEA,OAAO,SAAS,IAAI,CAAC;YACnB,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,SAAS;YACT,UAAU;YACV,WAAW;QACb;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,SAAS,IAAI,CAAC;YACnB,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,SAAS;YACT,UAAU;YACV,WAAW;QACb;IACF;AACF", "debugId": null}}]}