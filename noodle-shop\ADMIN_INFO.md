# 管理后台访问信息

## 🔐 登录信息

**后台地址：** `/admin/login`
- 完整URL：`https://your-domain.com/admin/login`
- 本地开发：`http://localhost:3000/admin/login`

**登录密码：** `noodle2024`

## 📋 管理功能

### 主要功能模块
1. **菜品管理** (`/admin/products`)
   - 查看所有菜品
   - 添加新菜品
   - 编辑菜品信息
   - 删除菜品

2. **门店管理** (`/admin/stores`) 
   - 查看门店列表
   - 添加新门店
   - 编辑门店信息

### 快速操作
- 返回首页
- 查看网站前台
- 清理缓存
- 备份数据

## 🛡️ 安全说明

- 使用简单的本地存储身份验证
- 密码存储在客户端 localStorage
- 适用于演示和开发环境
- 生产环境建议使用更安全的认证方案

## 🚀 部署后访问

部署到 Cloudflare Pages 后：
1. 访问 `https://your-site.pages.dev/admin/login`
2. 输入密码：`noodle2024`
3. 点击"登录管理后台"

## 📱 移动端适配

管理后台已适配移动设备，支持响应式布局。

## 🔧 开发说明

如需修改密码，编辑文件：
`src/app/admin/login/page.tsx` 第15行

```typescript
if (password === 'noodle2024') {
```

改为你想要的密码即可。