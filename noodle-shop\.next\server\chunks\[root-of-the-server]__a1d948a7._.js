module.exports = {

"[project]/.next-internal/server/app/api/products/[id]/toggle/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ProductSchema": ()=>ProductSchema,
    "ProductTypeSchema": ()=>ProductTypeSchema,
    "StoreSchema": ()=>StoreSchema,
    "createProduct": ()=>createProduct,
    "createProductType": ()=>createProductType,
    "createStore": ()=>createStore,
    "deleteProduct": ()=>deleteProduct,
    "deleteProductType": ()=>deleteProductType,
    "deleteStore": ()=>deleteStore,
    "getActiveProducts": ()=>getActiveProducts,
    "getLowStockProducts": ()=>getLowStockProducts,
    "getProductById": ()=>getProductById,
    "getProductTypeById": ()=>getProductTypeById,
    "getProductTypes": ()=>getProductTypes,
    "getProducts": ()=>getProducts,
    "getProductsByCategory": ()=>getProductsByCategory,
    "getStockStatus": ()=>getStockStatus,
    "getStoreById": ()=>getStoreById,
    "getStores": ()=>getStores,
    "parseIngredients": ()=>parseIngredients,
    "prisma": ()=>prisma,
    "toggleProductStatus": ()=>toggleProductStatus,
    "updateProduct": ()=>updateProduct,
    "updateProductStock": ()=>updateProductStock,
    "updateProductType": ()=>updateProductType,
    "updateStore": ()=>updateStore
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-route] (ecmascript) <export * as z>");
;
;
// 全局Prisma客户端实例
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
const ProductTypeSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    displayOrder: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().default(0),
    isActive: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(true)
});
const ProductSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    price: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    productTypeId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    image: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    ingredients: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).optional(),
    spicyLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().min(0).max(5).default(0),
    available: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(true),
    stock: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().min(0).default(0),
    minStock: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().min(0).default(5),
    isActive: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(true)
});
const StoreSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    address: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    phone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    latitude: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    longitude: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    openTime: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    closeTime: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    isOpen: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(true),
    image: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
});
async function getProductTypes() {
    return await prisma.productType.findMany({
        where: {
            isActive: true
        },
        orderBy: {
            displayOrder: 'asc'
        }
    });
}
async function getProductTypeById(id) {
    return await prisma.productType.findUnique({
        where: {
            id
        }
    });
}
async function createProductType(data) {
    const validatedData = ProductTypeSchema.parse(data);
    return await prisma.productType.create({
        data: validatedData
    });
}
async function updateProductType(id, data) {
    return await prisma.productType.update({
        where: {
            id
        },
        data
    });
}
async function deleteProductType(id) {
    // 检查是否有关联的产品
    const productCount = await prisma.product.count({
        where: {
            productTypeId: id
        }
    });
    if (productCount > 0) {
        throw new Error('无法删除：该产品类型下还有产品');
    }
    return await prisma.productType.delete({
        where: {
            id
        }
    });
}
async function getProducts() {
    return await prisma.product.findMany({
        include: {
            productType: true
        },
        orderBy: {
            createdAt: 'desc'
        }
    });
}
async function getProductById(id) {
    return await prisma.product.findUnique({
        where: {
            id
        },
        include: {
            productType: true
        }
    });
}
async function getProductsByCategory(productTypeId) {
    return await prisma.product.findMany({
        where: {
            productTypeId
        },
        include: {
            productType: true
        },
        orderBy: {
            createdAt: 'desc'
        }
    });
}
async function createProduct(data) {
    const validatedData = ProductSchema.omit({
        id: true
    }).parse(data);
    // 生成新ID
    const newId = Date.now().toString();
    return await prisma.product.create({
        data: {
            ...validatedData,
            id: newId,
            ingredients: validatedData.ingredients ? JSON.stringify(validatedData.ingredients) : null
        },
        include: {
            productType: true
        }
    });
}
async function updateProduct(id, data) {
    const updateData = {
        ...data
    };
    if (data.ingredients) {
        updateData.ingredients = JSON.stringify(data.ingredients);
    }
    return await prisma.product.update({
        where: {
            id
        },
        data: updateData,
        include: {
            productType: true
        }
    });
}
async function deleteProduct(id) {
    return await prisma.product.delete({
        where: {
            id
        }
    });
}
async function getStores() {
    return await prisma.store.findMany({
        orderBy: {
            createdAt: 'desc'
        }
    });
}
async function getStoreById(id) {
    return await prisma.store.findUnique({
        where: {
            id
        }
    });
}
async function createStore(data) {
    const validatedData = StoreSchema.omit({
        id: true
    }).parse(data);
    // 生成新ID
    const newId = Date.now().toString();
    return await prisma.store.create({
        data: {
            ...validatedData,
            id: newId
        }
    });
}
async function updateStore(id, data) {
    return await prisma.store.update({
        where: {
            id
        },
        data
    });
}
async function deleteStore(id) {
    return await prisma.store.delete({
        where: {
            id
        }
    });
}
async function updateProductStock(id, quantity) {
    const product = await prisma.product.findUnique({
        where: {
            id
        }
    });
    if (!product) throw new Error('产品不存在');
    const newStock = Math.max(0, product.stock + quantity);
    return await prisma.product.update({
        where: {
            id
        },
        data: {
            stock: newStock,
            available: newStock > 0
        },
        include: {
            productType: true
        }
    });
}
async function toggleProductStatus(id) {
    const product = await prisma.product.findUnique({
        where: {
            id
        }
    });
    if (!product) throw new Error('产品不存在');
    return await prisma.product.update({
        where: {
            id
        },
        data: {
            isActive: !product.isActive
        },
        include: {
            productType: true
        }
    });
}
async function getLowStockProducts() {
    return await prisma.product.findMany({
        where: {
            OR: [
                {
                    stock: {
                        lte: prisma.product.fields.minStock
                    }
                },
                {
                    stock: 0
                }
            ]
        },
        include: {
            productType: true
        },
        orderBy: {
            stock: 'asc'
        }
    });
}
async function getActiveProducts() {
    return await prisma.product.findMany({
        where: {
            isActive: true,
            available: true
        },
        include: {
            productType: true
        },
        orderBy: {
            createdAt: 'desc'
        }
    });
}
function parseIngredients(ingredients) {
    if (!ingredients) return [];
    try {
        return JSON.parse(ingredients);
    } catch  {
        return [];
    }
}
function getStockStatus(stock, minStock) {
    if (stock === 0) return {
        status: 'out_of_stock',
        label: '缺货',
        color: 'red'
    };
    if (stock <= minStock) return {
        status: 'low_stock',
        label: '库存不足',
        color: 'yellow'
    };
    return {
        status: 'in_stock',
        label: '库存充足',
        color: 'green'
    };
}
}),
"[project]/src/app/api/products/[id]/toggle/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "PUT": ()=>PUT
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
async function PUT(_request, context) {
    try {
        const { id } = await context.params;
        const product = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toggleProductStatus"])(id);
        return Response.json({
            ...product,
            category: product.productTypeId,
            ingredients: product.ingredients ? JSON.parse(product.ingredients) : []
        });
    } catch (error) {
        console.error('Failed to toggle product status:', error);
        if (error?.message === '产品不存在') {
            return Response.json({
                error: 'Product not found'
            }, {
                status: 404
            });
        }
        return Response.json({
            error: 'Failed to toggle product status'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__a1d948a7._.js.map