{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/api/weather/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const lat = searchParams.get('lat');\n    const lon = searchParams.get('lon');\n    \n    if (!lat || !lon) {\n      return Response.json({ error: 'Missing coordinates' }, { status: 400 });\n    }\n\n    // 使用固定的天气数据避免水合错误\n    const fixedWeather = { temp: 22, desc: '晴朗', icon: '☀️' };\n    \n    return Response.json({\n      temperature: fixedWeather.temp,\n      description: fixedWeather.desc,\n      city: '当前位置',\n      icon: fixedWeather.icon\n    });\n  } catch (error) {\n    console.error('Weather API error:', error);\n    return Response.json({\n      temperature: 20,\n      description: '晴朗',\n      city: '北京',\n      icon: '☀️'\n    });\n  }\n}"], "names": [], "mappings": ";;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;QAC7B,MAAM,MAAM,aAAa,GAAG,CAAC;QAE7B,IAAI,CAAC,OAAO,CAAC,KAAK;YAChB,OAAO,SAAS,IAAI,CAAC;gBAAE,OAAO;YAAsB,GAAG;gBAAE,QAAQ;YAAI;QACvE;QAEA,kBAAkB;QAClB,MAAM,eAAe;YAAE,MAAM;YAAI,MAAM;YAAM,MAAM;QAAK;QAExD,OAAO,SAAS,IAAI,CAAC;YACnB,aAAa,aAAa,IAAI;YAC9B,aAAa,aAAa,IAAI;YAC9B,MAAM;YACN,MAAM,aAAa,IAAI;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,SAAS,IAAI,CAAC;YACnB,aAAa;YACb,aAAa;YACb,MAAM;YACN,MAAM;QACR;IACF;AACF", "debugId": null}}]}