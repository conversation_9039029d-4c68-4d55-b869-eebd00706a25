{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/admin/login/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport Link from 'next/link';\r\n\r\nexport default function AdminLogin() {\r\n  const [password, setPassword] = useState('');\r\n  const [error, setError] = useState('');\r\n  const router = useRouter();\r\n\r\n  const handleLogin = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    // 简单的密码验证\r\n    if (password === 'noodle2024') {\r\n      localStorage.setItem('adminAuth', 'true');\r\n      router.push('/admin');\r\n    } else {\r\n      setError('密码错误，请重试');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n      <div className=\"max-w-md w-full bg-white rounded-lg shadow-md p-8\">\r\n        <div className=\"text-center mb-8\">\r\n          <h1 className=\"text-3xl font-bold text-orange-600\">🍜</h1>\r\n          <h2 className=\"text-2xl font-bold text-gray-800 mt-2\">管理后台登录</h2>\r\n          <p className=\"text-gray-600 mt-2\">香香面条店管理系统</p>\r\n        </div>\r\n\r\n        <form onSubmit={handleLogin} className=\"space-y-6\">\r\n          <div>\r\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n              管理员密码\r\n            </label>\r\n            <input\r\n              type=\"password\"\r\n              id=\"password\"\r\n              value={password}\r\n              onChange={(e) => setPassword(e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\r\n              placeholder=\"请输入管理员密码\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          {error && (\r\n            <div className=\"text-red-600 text-sm text-center bg-red-50 p-2 rounded\">\r\n              {error}\r\n            </div>\r\n          )}\r\n\r\n          <button\r\n            type=\"submit\"\r\n            className=\"w-full bg-orange-500 text-white py-2 px-4 rounded-md hover:bg-orange-600 transition-colors font-medium\"\r\n          >\r\n            登录管理后台\r\n          </button>\r\n        </form>\r\n\r\n        <div className=\"mt-6 text-center text-sm text-gray-500\">\r\n          <p>默认密码：noodle2024</p>\r\n          <p className=\"mt-1\">\r\n            <Link href=\"/\" className=\"text-orange-600 hover:text-orange-700\">\r\n              返回首页\r\n            </Link>\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc,CAAC;QACnB,EAAE,cAAc;QAEhB,UAAU;QACV,IAAI,aAAa,cAAc;YAC7B,aAAa,OAAO,CAAC,aAAa;YAClC,OAAO,IAAI,CAAC;QACd,OAAO;YACL,SAAS;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;8BAGpC,8OAAC;oBAAK,UAAU;oBAAa,WAAU;;sCACrC,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;wBAIX,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,8OAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCACH,8OAAC;4BAAE,WAAU;sCACX,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7E", "debugId": null}}]}