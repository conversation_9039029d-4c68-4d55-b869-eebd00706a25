{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, ReactNode } from 'react';\r\n\r\ninterface ClientOnlyProps {\r\n  children: ReactNode;\r\n  fallback?: ReactNode;\r\n}\r\n\r\nexport default function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\r\n  const [hasMounted, setHasMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setHasMounted(true);\r\n  }, []);\r\n\r\n  if (!hasMounted) {\r\n    return <>{fallback}</>;\r\n  }\r\n\r\n  return <>{children}</>;\r\n}"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASe,SAAS,WAAW,KAA8C;QAA9C,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB,GAA9C;;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,cAAc;QAChB;+BAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;GAZwB;KAAA", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/admin/products/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Product, ProductType } from '@/types';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useNotification } from '@/components/Notification';\nimport ClientOnly from '@/components/ClientOnly';\n\nexport default function AdminProductsPage() {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [productTypes, setProductTypes] = useState<ProductType[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filter, setFilter] = useState('all');\n  const [mounted, setMounted] = useState(false);\n  const { showNotification, showConfirm } = useNotification();\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useEffect(() => {\n    if (!mounted) return;\n    fetchProducts();\n    fetchProductTypes();\n  }, [mounted]);\n\n  const fetchProducts = async () => {\n    try {\n      const response = await fetch('/api/products');\n      const data = await response.json();\n      setProducts(data);\n    } catch (error) {\n      console.error('Failed to fetch products:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchProductTypes = async () => {\n    try {\n      const response = await fetch('/api/product-types');\n      const data = await response.json();\n      setProductTypes(data);\n    } catch (error) {\n      console.error('Failed to fetch product types:', error);\n    }\n  };\n\n  const deleteProduct = async (id: string) => {\n    showConfirm(\n      '确定要删除这个菜品吗？',\n      async () => {\n        try {\n          const response = await fetch(`/api/products/${id}`, {\n            method: 'DELETE',\n          });\n          \n          if (response.ok) {\n            setProducts(products.filter(p => p.id !== id));\n            showNotification('菜品删除成功', 'success');\n          } else {\n            showNotification('删除失败', 'error');\n          }\n        } catch (error) {\n          console.error('Failed to delete product:', error);\n          showNotification('删除失败', 'error');\n        }\n      }\n    );\n  };\n\n  const toggleAvailability = async (product: Product) => {\n    try {\n      const response = await fetch(`/api/products/${product.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...product,\n          available: !product.available,\n        }),\n      });\n      \n      if (response.ok) {\n        const updatedProduct = await response.json();\n        setProducts(products.map(p => \n          p.id === product.id ? updatedProduct : p\n        ));\n      }\n    } catch (error) {\n      console.error('Failed to update product:', error);\n    }\n  };\n\n  const filteredProducts = products.filter(product => {\n    if (filter === 'all') return true;\n    return product.productTypeId === filter;\n  });\n\n  // 动态生成类别选项\n  const categories: Record<string, string> = {\n    all: '全部',\n    ...productTypes.reduce((acc, type) => ({\n      ...acc,\n      [type.id]: type.name\n    }), {})\n  };\n\n  // 获取产品类型名称\n  const getProductTypeName = (productTypeId: string) => {\n    const type = productTypes.find(t => t.id === productTypeId);\n    return type ? type.name : '未分类';\n  };\n\n  if (!mounted) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">正在加载...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <ClientOnly fallback={\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    }>\n      <div className=\"min-h-screen bg-gray-50\">\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/admin\" className=\"text-2xl font-bold text-orange-600 hover:text-orange-700\">\n                管理后台\n              </Link>\n              <span className=\"text-gray-600\">菜品管理</span>\n            </div>\n            <Link\n              href=\"/admin/products/new\"\n              className=\"bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors\"\n            >\n              添加新菜品\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"mb-6\">\n          <div className=\"flex flex-wrap gap-2\">\n            {Object.entries(categories).map(([key, label]) => (\n              <button\n                key={key}\n                onClick={() => setFilter(key)}\n                className={`px-4 py-2 rounded-lg transition-colors ${\n                  filter === key\n                    ? 'bg-orange-500 text-white'\n                    : 'bg-white border border-gray-200 text-gray-700 hover:border-orange-500'\n                }`}\n              >\n                {label}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    菜品\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    类别\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    价格\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    状态\n                  </th>\n                  <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    操作\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {filteredProducts.map((product) => (\n                  <tr key={product.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <div className=\"flex-shrink-0 h-16 w-16\">\n                          <Image\n                            src={product.image}\n                            alt={product.name}\n                            width={64}\n                            height={64}\n                            className=\"h-16 w-16 rounded-lg object-cover\"\n                          />\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {product.name}\n                          </div>\n                          <div className=\"text-sm text-gray-500 line-clamp-1\">\n                            {product.description}\n                          </div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800\">\n                        {getProductTypeName(product.productTypeId)}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      ¥{product.price}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <button\n                        onClick={() => toggleAvailability(product)}\n                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${\n                          product.available\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-red-100 text-red-800'\n                        }`}\n                      >\n                        {product.available ? '有货' : '缺货'}\n                      </button>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                      <div className=\"flex justify-end space-x-2\">\n                        <Link\n                          href={`/admin/products/edit/${product.id}`}\n                          className=\"text-orange-600 hover:text-orange-900\"\n                        >\n                          编辑\n                        </Link>\n                        <button\n                          onClick={() => deleteProduct(product.id)}\n                          className=\"text-red-600 hover:text-red-900\"\n                        >\n                          删除\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {filteredProducts.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">🍜</div>\n            <h2 className=\"text-xl text-gray-600\">暂无菜品</h2>\n            <p className=\"text-gray-500 mt-2\">\n              {filter !== 'all' ? '该分类下暂无菜品' : '请添加新菜品'}\n            </p>\n          </div>\n        )}\n      </main>\n      </div>\n    </ClientOnly>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;;;AAPA;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,gBAAgB,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,WAAW;QACb;sCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,SAAS;YACd;YACA;QACF;sCAAG;QAAC;KAAQ;IAEZ,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,YACE,eACA;YACE,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,AAAC,iBAAmB,OAAH,KAAM;oBAClD,QAAQ;gBACV;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC1C,iBAAiB,UAAU;gBAC7B,OAAO;oBACL,iBAAiB,QAAQ;gBAC3B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,iBAAiB,QAAQ;YAC3B;QACF;IAEJ;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,iBAA2B,OAAX,QAAQ,EAAE,GAAI;gBAC1D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,OAAO;oBACV,WAAW,CAAC,QAAQ,SAAS;gBAC/B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,iBAAiB,MAAM,SAAS,IAAI;gBAC1C,YAAY,SAAS,GAAG,CAAC,CAAA,IACvB,EAAE,EAAE,KAAK,QAAQ,EAAE,GAAG,iBAAiB;YAE3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,IAAI,WAAW,OAAO,OAAO;QAC7B,OAAO,QAAQ,aAAa,KAAK;IACnC;IAEA,WAAW;IACX,MAAM,aAAqC;QACzC,KAAK;QACL,GAAG,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,CAAC;gBACrC,GAAG,GAAG;gBACN,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,IAAI;YACtB,CAAC,GAAG,CAAC,EAAE;IACT;IAEA,WAAW;IACX,MAAM,qBAAqB,CAAC;QAC1B,MAAM,OAAO,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,OAAO,OAAO,KAAK,IAAI,GAAG;IAC5B;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC,mIAAA,CAAA,UAAU;QAAC,wBACV,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;kBAItC,cAAA,6LAAC;YAAI,WAAU;;8BACf,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAA2D;;;;;;sDAGzF,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAElC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;8BAOP,6LAAC;oBAAK,WAAU;;sCACd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC;wCAAC,CAAC,KAAK,MAAM;yDAC3C,6LAAC;wCAEC,SAAS,IAAM,UAAU;wCACzB,WAAW,AAAC,0CAIX,OAHC,WAAW,MACP,6BACA;kDAGL;uCARI;;;;;;;;;;;;;;;;sCAcb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;;;;;;;;;;;;sDAKpG,6LAAC;4CAAM,WAAU;sDACd,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;oDAAoB,WAAU;;sEAC7B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4EACJ,KAAK,QAAQ,KAAK;4EAClB,KAAK,QAAQ,IAAI;4EACjB,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FACZ,QAAQ,IAAI;;;;;;0FAEf,6LAAC;gFAAI,WAAU;0FACZ,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;sEAK5B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAU;0EACb,mBAAmB,QAAQ,aAAa;;;;;;;;;;;sEAG7C,6LAAC;4DAAG,WAAU;;gEAAoD;gEAC9D,QAAQ,KAAK;;;;;;;sEAEjB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEACC,SAAS,IAAM,mBAAmB;gEAClC,WAAW,AAAC,iEAIX,OAHC,QAAQ,SAAS,GACb,gCACA;0EAGL,QAAQ,SAAS,GAAG,OAAO;;;;;;;;;;;sEAGhC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAM,AAAC,wBAAkC,OAAX,QAAQ,EAAE;wEACxC,WAAU;kFACX;;;;;;kFAGD,6LAAC;wEACC,SAAS,IAAM,cAAc,QAAQ,EAAE;wEACvC,WAAU;kFACX;;;;;;;;;;;;;;;;;;mDArDE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;wBAiE5B,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,6LAAC;oCAAE,WAAU;8CACV,WAAW,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;GAvRwB;;QAMoB,qIAAA,CAAA,kBAAe;;;KANnC", "debugId": null}}]}