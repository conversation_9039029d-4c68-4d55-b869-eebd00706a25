import { NextRequest } from 'next/server';
import { toggleProductStatus } from '@/lib/database';

interface RouteContext {
  params: Promise<{ id: string }>;
}

// PUT - 切换产品上下架状态
export async function PUT(_request: NextRequest, context: RouteContext) {
  try {
    const { id } = await context.params;
    
    const product = await toggleProductStatus(id);
    
    return Response.json({
      ...product,
      category: product.productTypeId,
      ingredients: product.ingredients ? JSON.parse(product.ingredients) : [],
    });
  } catch (error: any) {
    console.error('Failed to toggle product status:', error);
    if (error?.message === '产品不存在') {
      return Response.json({ error: 'Product not found' }, { status: 404 });
    }
    return Response.json({ error: 'Failed to toggle product status' }, { status: 500 });
  }
}
