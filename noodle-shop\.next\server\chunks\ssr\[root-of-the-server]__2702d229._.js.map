{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/lib/data.ts"], "sourcesContent": ["import { Product, Store } from '@/types';\nimport productsData from '@/data/products.json';\nimport storesData from '@/data/stores.json';\nimport fs from 'fs/promises';\nimport path from 'path';\n\nconst PRODUCTS_FILE = path.join(process.cwd(), 'src/data/products.json');\nconst STORES_FILE = path.join(process.cwd(), 'src/data/stores.json');\n\nexport async function getProducts(): Promise<Product[]> {\n  return productsData as Product[];\n}\n\nexport async function getProductById(id: string): Promise<Product | null> {\n  const products = await getProducts();\n  return products.find(product => product.id === id) || null;\n}\n\nexport async function getProductsByCategory(category: string): Promise<Product[]> {\n  const products = await getProducts();\n  return products.filter(product => product.category === category);\n}\n\nexport async function getStores(): Promise<Store[]> {\n  return storesData as Store[];\n}\n\nexport async function getStoreById(id: string): Promise<Store | null> {\n  const stores = await getStores();\n  return stores.find(store => store.id === id) || null;\n}\n\nexport async function saveProducts(products: Product[]): Promise<void> {\n  await fs.writeFile(PRODUCTS_FILE, JSON.stringify(products, null, 2));\n}\n\nexport async function saveStores(stores: Store[]): Promise<void> {\n  await fs.writeFile(STORES_FILE, JSON.stringify(stores, null, 2));\n}\n\nexport async function addProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> {\n  const products = await getProducts();\n  const newProduct: Product = {\n    ...product,\n    id: Date.now().toString(),\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  };\n  products.push(newProduct);\n  await saveProducts(products);\n  return newProduct;\n}\n\nexport async function updateProduct(id: string, updates: Partial<Product>): Promise<Product | null> {\n  const products = await getProducts();\n  const index = products.findIndex(product => product.id === id);\n  if (index === -1) return null;\n  \n  products[index] = {\n    ...products[index],\n    ...updates,\n    updatedAt: new Date().toISOString(),\n  };\n  await saveProducts(products);\n  return products[index];\n}\n\nexport async function deleteProduct(id: string): Promise<boolean> {\n  const products = await getProducts();\n  const index = products.findIndex(product => product.id === id);\n  if (index === -1) return false;\n  \n  products.splice(index, 1);\n  await saveProducts(products);\n  return true;\n}\n\nexport async function addStore(store: Omit<Store, 'id' | 'createdAt' | 'updatedAt'>): Promise<Store> {\n  const stores = await getStores();\n  const newStore: Store = {\n    ...store,\n    id: Date.now().toString(),\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  };\n  stores.push(newStore);\n  await saveStores(stores);\n  return newStore;\n}\n\nexport async function updateStore(id: string, updates: Partial<Store>): Promise<Store | null> {\n  const stores = await getStores();\n  const index = stores.findIndex(store => store.id === id);\n  if (index === -1) return null;\n  \n  stores[index] = {\n    ...stores[index],\n    ...updates,\n    updatedAt: new Date().toISOString(),\n  };\n  await saveStores(stores);\n  return stores[index];\n}\n\nexport async function deleteStore(id: string): Promise<boolean> {\n  const stores = await getStores();\n  const index = stores.findIndex(store => store.id === id);\n  if (index === -1) return false;\n  \n  stores.splice(index, 1);\n  await saveStores(stores);\n  return true;\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC/C,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAEtC,eAAe;IACpB,OAAO,+FAAA,CAAA,UAAY;AACrB;AAEO,eAAe,eAAe,EAAU;IAC7C,MAAM,WAAW,MAAM;IACvB,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK,OAAO;AACxD;AAEO,eAAe,sBAAsB,QAAgB;IAC1D,MAAM,WAAW,MAAM;IACvB,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACzD;AAEO,eAAe;IACpB,OAAO,6FAAA,CAAA,UAAU;AACnB;AAEO,eAAe,aAAa,EAAU;IAC3C,MAAM,SAAS,MAAM;IACrB,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,OAAO;AAClD;AAEO,eAAe,aAAa,QAAmB;IACpD,MAAM,qHAAA,CAAA,UAAE,CAAC,SAAS,CAAC,eAAe,KAAK,SAAS,CAAC,UAAU,MAAM;AACnE;AAEO,eAAe,WAAW,MAAe;IAC9C,MAAM,qHAAA,CAAA,UAAE,CAAC,SAAS,CAAC,aAAa,KAAK,SAAS,CAAC,QAAQ,MAAM;AAC/D;AAEO,eAAe,WAAW,OAAwD;IACvF,MAAM,WAAW,MAAM;IACvB,MAAM,aAAsB;QAC1B,GAAG,OAAO;QACV,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,SAAS,IAAI,CAAC;IACd,MAAM,aAAa;IACnB,OAAO;AACT;AAEO,eAAe,cAAc,EAAU,EAAE,OAAyB;IACvE,MAAM,WAAW,MAAM;IACvB,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,QAAQ,CAAC,MAAM,GAAG;QAChB,GAAG,QAAQ,CAAC,MAAM;QAClB,GAAG,OAAO;QACV,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,MAAM,aAAa;IACnB,OAAO,QAAQ,CAAC,MAAM;AACxB;AAEO,eAAe,cAAc,EAAU;IAC5C,MAAM,WAAW,MAAM;IACvB,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,SAAS,MAAM,CAAC,OAAO;IACvB,MAAM,aAAa;IACnB,OAAO;AACT;AAEO,eAAe,SAAS,KAAoD;IACjF,MAAM,SAAS,MAAM;IACrB,MAAM,WAAkB;QACtB,GAAG,KAAK;QACR,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,OAAO,IAAI,CAAC;IACZ,MAAM,WAAW;IACjB,OAAO;AACT;AAEO,eAAe,YAAY,EAAU,EAAE,OAAuB;IACnE,MAAM,SAAS,MAAM;IACrB,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACrD,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,MAAM,CAAC,MAAM,GAAG;QACd,GAAG,MAAM,CAAC,MAAM;QAChB,GAAG,OAAO;QACV,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,MAAM,WAAW;IACjB,OAAO,MAAM,CAAC,MAAM;AACtB;AAEO,eAAe,YAAY,EAAU;IAC1C,MAAM,SAAS,MAAM;IACrB,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACrD,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,OAAO,MAAM,CAAC,OAAO;IACrB,MAAM,WAAW;IACjB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/ProductForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ProductForm.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProductForm.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/ProductForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ProductForm.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProductForm.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/admin/products/edit/%5Bid%5D/page.tsx"], "sourcesContent": ["import { getProductById } from '@/lib/data';\nimport ProductForm from '@/components/ProductForm';\nimport { notFound } from 'next/navigation';\n\ninterface EditProductPageProps {\n  params: Promise<{ id: string }>;\n}\n\nexport default async function EditProductPage({ params }: EditProductPageProps) {\n  const { id } = await params;\n  const product = await getProductById(id);\n  \n  if (!product) {\n    notFound();\n  }\n\n  return <ProductForm product={product} isEdit={true} />;\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;;;;;AAMe,eAAe,gBAAgB,EAAE,MAAM,EAAwB;IAC5E,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IACrB,MAAM,UAAU,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD,EAAE;IAErC,IAAI,CAAC,SAAS;QACZ,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,qBAAO,8OAAC,iIAAA,CAAA,UAAW;QAAC,SAAS;QAAS,QAAQ;;;;;;AAChD", "debugId": null}}]}