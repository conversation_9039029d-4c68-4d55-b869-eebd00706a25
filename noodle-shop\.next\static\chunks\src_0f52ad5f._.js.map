{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/ProductCard.tsx"], "sourcesContent": ["import { Product } from '@/types';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nexport default function ProductCard({ product }: ProductCardProps) {\n  const spicyIcons = '🌶️'.repeat(product.spicyLevel);\n\n  // 获取类别显示文本，优先使用productType名称\n  const getCategoryText = () => {\n    if ('productType' in product && product.productType && typeof product.productType === 'object' && 'name' in product.productType) {\n      return (product.productType as any).name;\n    }\n\n    // 回退到硬编码的类别映射\n    const categoryText: Record<string, string> = {\n      'fresh-noodles': '手工鲜面条',\n      'dumpling-wrappers': '饺子皮',\n      'noodle-sheets': '面皮',\n      'shaobing': '烧饼',\n      'wonton-wrappers': '馄饨皮',\n      'steamed-buns': '馒头花卷',\n      noodles: '面条',\n      pasta: '面片',\n      soup: '汤面'\n    };\n\n    return categoryText[product.category] || product.category || '未分类';\n  };\n\n  // 获取库存状态\n  const getStockStatus = () => {\n    const stock = (product as any).stock || 0;\n    const minStock = (product as any).minStock || 5;\n\n    if (stock === 0) return { status: 'out_of_stock', label: '缺货', color: 'bg-red-100 text-red-800' };\n    if (stock <= minStock) return { status: 'low_stock', label: '库存不足', color: 'bg-yellow-100 text-yellow-800' };\n    return { status: 'in_stock', label: '库存充足', color: 'bg-green-100 text-green-800' };\n  };\n\n  const stockStatus = getStockStatus();\n  const isActive = (product as any).isActive !== false;\n\n  return (\n    <div className={`bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow ${!isActive ? 'opacity-60' : ''}`}>\n      <div className=\"relative h-48\">\n        <Image\n          src={product.image}\n          alt={product.name}\n          fill\n          className=\"object-cover\"\n        />\n        <div className=\"absolute top-2 left-2 flex flex-col gap-1\">\n          {spicyIcons && (\n            <span className=\"bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs\">\n              {spicyIcons}\n            </span>\n          )}\n          <span className={`px-2 py-1 rounded-full text-xs font-medium ${stockStatus.color}`}>\n            {stockStatus.label}\n          </span>\n        </div>\n        <div className=\"absolute top-2 right-2 flex flex-col gap-1\">\n          <span className=\"bg-orange-500 text-white px-2 py-1 rounded-full text-xs\">\n            {getCategoryText()}\n          </span>\n          {!isActive && (\n            <span className=\"bg-gray-500 text-white px-2 py-1 rounded-full text-xs\">\n              已下架\n            </span>\n          )}\n        </div>\n      </div>\n      <div className=\"p-4\">\n        <h3 className=\"text-xl font-semibold text-gray-800 mb-2\">{product.name}</h3>\n        <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">{product.description}</p>\n        \n        <div className=\"flex items-center justify-between mb-3\">\n          <span className=\"text-2xl font-bold text-orange-600\">¥{product.price}</span>\n          <div className=\"flex items-center space-x-1\">\n            <span className=\"text-gray-500 text-sm\">辣度:</span>\n            <span className=\"text-sm\">{spicyIcons || '不辣'}</span>\n          </div>\n        </div>\n\n        <div className=\"flex items-center justify-between mb-3\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-gray-500 text-sm\">库存:</span>\n            <span className={`text-sm font-medium ${\n              (product as any).stock === 0 ? 'text-red-600' :\n              (product as any).stock <= (product as any).minStock ? 'text-yellow-600' : 'text-green-600'\n            }`}>\n              {(product as any).stock || 0} 件\n            </span>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            <span className={`px-2 py-1 rounded-full text-xs ${\n              product.available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n            }`}>\n              {product.available ? '有货' : '缺货'}\n            </span>\n          </div>\n        </div>\n\n        {/* 发布日期 */}\n        {(product as any).publishedAt && (\n          <div className=\"flex items-center justify-between mb-3 text-xs text-gray-500\">\n            <span>发布时间:</span>\n            <span>{new Date((product as any).publishedAt).toLocaleDateString('zh-CN')}</span>\n          </div>\n        )}\n\n        <div className=\"mb-3\">\n          <div className=\"flex flex-wrap gap-1\">\n            {product.ingredients.slice(0, 3).map((ingredient, index) => (\n              <span\n                key={index}\n                className=\"bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs\"\n              >\n                {ingredient}\n              </span>\n            ))}\n            {product.ingredients.length > 3 && (\n              <span className=\"text-gray-500 text-xs\">...</span>\n            )}\n          </div>\n        </div>\n\n        <div className=\"flex justify-between items-center\">\n          <Link\n            href={`/products/${product.id}`}\n            className=\"text-orange-500 hover:text-orange-600 text-sm font-medium\"\n          >\n            查看详情 →\n          </Link>\n          <span className={`text-sm ${product.available ? 'text-green-600' : 'text-red-600'}`}>\n            {product.available ? '有货' : '暂缺'}\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAMe,SAAS,YAAY,KAA6B;QAA7B,EAAE,OAAO,EAAoB,GAA7B;IAClC,MAAM,aAAa,MAAM,MAAM,CAAC,QAAQ,UAAU;IAElD,6BAA6B;IAC7B,MAAM,kBAAkB;QACtB,IAAI,iBAAiB,WAAW,QAAQ,WAAW,IAAI,OAAO,QAAQ,WAAW,KAAK,YAAY,UAAU,QAAQ,WAAW,EAAE;YAC/H,OAAO,AAAC,QAAQ,WAAW,CAAS,IAAI;QAC1C;QAEA,cAAc;QACd,MAAM,eAAuC;YAC3C,iBAAiB;YACjB,qBAAqB;YACrB,iBAAiB;YACjB,YAAY;YACZ,mBAAmB;YACnB,gBAAgB;YAChB,SAAS;YACT,OAAO;YACP,MAAM;QACR;QAEA,OAAO,YAAY,CAAC,QAAQ,QAAQ,CAAC,IAAI,QAAQ,QAAQ,IAAI;IAC/D;IAEA,SAAS;IACT,MAAM,iBAAiB;QACrB,MAAM,QAAQ,AAAC,QAAgB,KAAK,IAAI;QACxC,MAAM,WAAW,AAAC,QAAgB,QAAQ,IAAI;QAE9C,IAAI,UAAU,GAAG,OAAO;YAAE,QAAQ;YAAgB,OAAO;YAAM,OAAO;QAA0B;QAChG,IAAI,SAAS,UAAU,OAAO;YAAE,QAAQ;YAAa,OAAO;YAAQ,OAAO;QAAgC;QAC3G,OAAO;YAAE,QAAQ;YAAY,OAAO;YAAQ,OAAO;QAA8B;IACnF;IAEA,MAAM,cAAc;IACpB,MAAM,WAAW,AAAC,QAAgB,QAAQ,KAAK;IAE/C,qBACE,6LAAC;QAAI,WAAW,AAAC,mFAAgH,OAA9B,CAAC,WAAW,eAAe;;0BAC5H,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,KAAK;wBAClB,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;4BACZ,4BACC,6LAAC;gCAAK,WAAU;0CACb;;;;;;0CAGL,6LAAC;gCAAK,WAAW,AAAC,8CAA+D,OAAlB,YAAY,KAAK;0CAC7E,YAAY,KAAK;;;;;;;;;;;;kCAGtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CACb;;;;;;4BAEF,CAAC,0BACA,6LAAC;gCAAK,WAAU;0CAAwD;;;;;;;;;;;;;;;;;;0BAM9E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4C,QAAQ,IAAI;;;;;;kCACtE,6LAAC;wBAAE,WAAU;kCAA2C,QAAQ,WAAW;;;;;;kCAE3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;oCAAqC;oCAAE,QAAQ,KAAK;;;;;;;0CACpE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;kDAAW,cAAc;;;;;;;;;;;;;;;;;;kCAI7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAW,AAAC,uBAGjB,OAFC,AAAC,QAAgB,KAAK,KAAK,IAAI,iBAC/B,AAAC,QAAgB,KAAK,IAAI,AAAC,QAAgB,QAAQ,GAAG,oBAAoB;;4CAExE,QAAgB,KAAK,IAAI;4CAAE;;;;;;;;;;;;;0CAGjC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAW,AAAC,kCAEjB,OADC,QAAQ,SAAS,GAAG,gCAAgC;8CAEnD,QAAQ,SAAS,GAAG,OAAO;;;;;;;;;;;;;;;;;oBAMhC,QAAgB,WAAW,kBAC3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAM,IAAI,KAAK,AAAC,QAAgB,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;kCAIrE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,QAAQ,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,YAAY,sBAChD,6LAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;gCAMR,QAAQ,WAAW,CAAC,MAAM,GAAG,mBAC5B,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAK9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,AAAC,aAAuB,OAAX,QAAQ,EAAE;gCAC7B,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCAAK,WAAW,AAAC,WAAgE,OAAtD,QAAQ,SAAS,GAAG,mBAAmB;0CAChE,QAAQ,SAAS,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;KAzIwB", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/ProductSearch.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\n\ninterface ProductSearchProps {\n  categories: Array<{ id: string; name: string }>;\n  onSearch?: (results: any) => void;\n}\n\nexport default function ProductSearch({ categories, onSearch }: ProductSearchProps) {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  \n  const [query, setQuery] = useState(searchParams.get('q') || '');\n  const [category, setCategory] = useState(searchParams.get('category') || 'all');\n  const [sortBy, setSortBy] = useState(searchParams.get('sortBy') || 'createdAt');\n  const [sortOrder, setSortOrder] = useState(searchParams.get('sortOrder') || 'desc');\n  const [loading, setLoading] = useState(false);\n\n  const handleSearch = async () => {\n    setLoading(true);\n\n    try {\n      const params = new URLSearchParams();\n      if (query.trim()) params.set('q', query.trim());\n      if (category !== 'all') params.set('category', category);\n      params.set('sortBy', sortBy);\n      params.set('sortOrder', sortOrder);\n\n      const response = await fetch(`/api/products/search?${params.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}`);\n      }\n\n      const data = await response.json();\n\n      if (onSearch) {\n        onSearch(data);\n      }\n\n      // 更新URL参数\n      const newParams = new URLSearchParams(window.location.search);\n      if (query.trim()) {\n        newParams.set('q', query.trim());\n      } else {\n        newParams.delete('q');\n      }\n      if (category !== 'all') {\n        newParams.set('category', category);\n      } else {\n        newParams.delete('category');\n      }\n      newParams.set('sortBy', sortBy);\n      newParams.set('sortOrder', sortOrder);\n\n      router.push(`${window.location.pathname}?${newParams.toString()}`);\n    } catch (error) {\n      console.error('Search failed:', error);\n      if (onSearch) {\n        onSearch({ products: [], pagination: { total: 0 } });\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReset = () => {\n    setQuery('');\n    setCategory('all');\n    setSortBy('createdAt');\n    setSortOrder('desc');\n    router.push(window.location.pathname);\n    if (onSearch) {\n      onSearch({ products: [], pagination: { total: 0 } });\n    }\n  };\n\n  useEffect(() => {\n    const urlQuery = searchParams.get('q');\n    const urlCategory = searchParams.get('category');\n    const urlSortBy = searchParams.get('sortBy');\n    const urlSortOrder = searchParams.get('sortOrder');\n\n    if (urlQuery) setQuery(urlQuery);\n    if (urlCategory) setCategory(urlCategory);\n    if (urlSortBy) setSortBy(urlSortBy);\n    if (urlSortOrder) setSortOrder(urlSortOrder);\n\n    // 如果URL中有搜索参数，自动执行搜索\n    if (urlQuery || urlCategory) {\n      setTimeout(() => handleSearch(), 100);\n    }\n  }, [searchParams]);\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n      <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">搜索产品</h2>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4\">\n        {/* 搜索关键词 */}\n        <div>\n          <label htmlFor=\"search-query\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            搜索关键词\n          </label>\n          <input\n            id=\"search-query\"\n            type=\"text\"\n            value={query}\n            onChange={(e) => setQuery(e.target.value)}\n            placeholder=\"输入产品名称、描述或配料...\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}\n          />\n        </div>\n\n        {/* 产品分类 */}\n        <div>\n          <label htmlFor=\"search-category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            产品分类\n          </label>\n          <select\n            id=\"search-category\"\n            value={category}\n            onChange={(e) => setCategory(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n          >\n            <option value=\"all\">全部分类</option>\n            {categories.map((cat) => (\n              <option key={cat.id} value={cat.id}>\n                {cat.name}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        {/* 排序方式 */}\n        <div>\n          <label htmlFor=\"search-sort\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            排序方式\n          </label>\n          <select\n            id=\"search-sort\"\n            value={sortBy}\n            onChange={(e) => setSortBy(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n          >\n            <option value=\"createdAt\">创建时间</option>\n            <option value=\"publishedAt\">发布时间</option>\n            <option value=\"name\">产品名称</option>\n            <option value=\"price\">价格</option>\n          </select>\n        </div>\n\n        {/* 排序顺序 */}\n        <div>\n          <label htmlFor=\"search-order\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            排序顺序\n          </label>\n          <select\n            id=\"search-order\"\n            value={sortOrder}\n            onChange={(e) => setSortOrder(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\n          >\n            <option value=\"desc\">降序</option>\n            <option value=\"asc\">升序</option>\n          </select>\n        </div>\n      </div>\n\n      <div className=\"flex flex-col sm:flex-row gap-3\">\n        <button\n          onClick={handleSearch}\n          disabled={loading}\n          className=\"flex-1 bg-orange-500 text-white py-2 px-6 rounded-md hover:bg-orange-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {loading ? (\n            <span className=\"flex items-center justify-center\">\n              <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n              </svg>\n              搜索中...\n            </span>\n          ) : (\n            '🔍 搜索'\n          )}\n        </button>\n        \n        <button\n          onClick={handleReset}\n          className=\"bg-gray-500 text-white py-2 px-6 rounded-md hover:bg-gray-600 transition-colors\"\n        >\n          🔄 重置\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,cAAc,KAA4C;QAA5C,EAAE,UAAU,EAAE,QAAQ,EAAsB,GAA5C;;IACpC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAEnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,GAAG,CAAC,QAAQ;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,GAAG,CAAC,eAAe;IACzE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,GAAG,CAAC,aAAa;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,GAAG,CAAC,gBAAgB;IAC5E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe;QACnB,WAAW;QAEX,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,MAAM,IAAI,IAAI,OAAO,GAAG,CAAC,KAAK,MAAM,IAAI;YAC5C,IAAI,aAAa,OAAO,OAAO,GAAG,CAAC,YAAY;YAC/C,OAAO,GAAG,CAAC,UAAU;YACrB,OAAO,GAAG,CAAC,aAAa;YAExB,MAAM,WAAW,MAAM,MAAM,AAAC,wBAAyC,OAAlB,OAAO,QAAQ;YAEpE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,QAAuB,OAAhB,SAAS,MAAM;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,UAAU;gBACZ,SAAS;YACX;YAEA,UAAU;YACV,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;YAC5D,IAAI,MAAM,IAAI,IAAI;gBAChB,UAAU,GAAG,CAAC,KAAK,MAAM,IAAI;YAC/B,OAAO;gBACL,UAAU,MAAM,CAAC;YACnB;YACA,IAAI,aAAa,OAAO;gBACtB,UAAU,GAAG,CAAC,YAAY;YAC5B,OAAO;gBACL,UAAU,MAAM,CAAC;YACnB;YACA,UAAU,GAAG,CAAC,UAAU;YACxB,UAAU,GAAG,CAAC,aAAa;YAE3B,OAAO,IAAI,CAAC,AAAC,GAA8B,OAA5B,OAAO,QAAQ,CAAC,QAAQ,EAAC,KAAwB,OAArB,UAAU,QAAQ;QAC/D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,IAAI,UAAU;gBACZ,SAAS;oBAAE,UAAU,EAAE;oBAAE,YAAY;wBAAE,OAAO;oBAAE;gBAAE;YACpD;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,YAAY;QACZ,UAAU;QACV,aAAa;QACb,OAAO,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ;QACpC,IAAI,UAAU;YACZ,SAAS;gBAAE,UAAU,EAAE;gBAAE,YAAY;oBAAE,OAAO;gBAAE;YAAE;QACpD;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,WAAW,aAAa,GAAG,CAAC;YAClC,MAAM,cAAc,aAAa,GAAG,CAAC;YACrC,MAAM,YAAY,aAAa,GAAG,CAAC;YACnC,MAAM,eAAe,aAAa,GAAG,CAAC;YAEtC,IAAI,UAAU,SAAS;YACvB,IAAI,aAAa,YAAY;YAC7B,IAAI,WAAW,UAAU;YACzB,IAAI,cAAc,aAAa;YAE/B,qBAAqB;YACrB,IAAI,YAAY,aAAa;gBAC3B;+CAAW,IAAM;8CAAgB;YACnC;QACF;kCAAG;QAAC;KAAa;IAEjB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA2C;;;;;;0BAEzD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAA+C;;;;;;0CAGvF,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,aAAY;gCACZ,WAAU;gCACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;kCAK5C,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAkB,WAAU;0CAA+C;;;;;;0CAG1F,6LAAC;gCACC,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;oCACnB,WAAW,GAAG,CAAC,CAAC,oBACf,6LAAC;4CAAoB,OAAO,IAAI,EAAE;sDAC/B,IAAI,IAAI;2CADE,IAAI,EAAE;;;;;;;;;;;;;;;;;kCAQzB,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAA+C;;;;;;0CAGtF,6LAAC;gCACC,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAA+C;;;;;;0CAGvF,6LAAC;gCACC,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAM;;;;;;;;;;;;;;;;;;;;;;;;0BAK1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,wBACC,6LAAC;4BAAK,WAAU;;8CACd,6LAAC;oCAAI,WAAU;oCAA6C,OAAM;oCAA6B,MAAK;oCAAO,SAAQ;;sDACjH,6LAAC;4CAAO,WAAU;4CAAa,IAAG;4CAAK,IAAG;4CAAK,GAAE;4CAAK,QAAO;4CAAe,aAAY;;;;;;sDACxF,6LAAC;4CAAK,WAAU;4CAAa,MAAK;4CAAe,GAAE;;;;;;;;;;;;gCAC/C;;;;;;mCAIR;;;;;;kCAIJ,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;GA9LwB;;QACP,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAFd", "debugId": null}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, ReactNode } from 'react';\r\n\r\ninterface ClientOnlyProps {\r\n  children: ReactNode;\r\n  fallback?: ReactNode;\r\n}\r\n\r\nexport default function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\r\n  const [hasMounted, setHasMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setHasMounted(true);\r\n  }, []);\r\n\r\n  if (!hasMounted) {\r\n    return <>{fallback}</>;\r\n  }\r\n\r\n  return <>{children}</>;\r\n}"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASe,SAAS,WAAW,KAA8C;QAA9C,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB,GAA9C;;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,cAAc;QAChB;+BAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;GAZwB;KAAA", "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/WeatherWidget.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { WeatherInfo, LocationInfo } from '@/types';\nimport ClientOnly from './ClientOnly';\n\nfunction WeatherWidgetContent() {\n  const [weather, setWeather] = useState<WeatherInfo | null>(null);\n  const [location, setLocation] = useState<LocationInfo | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useEffect(() => {\n    if (!mounted) return;\n    \n    const fetchLocationAndWeather = async () => {\n      try {\n        const locationResponse = await fetch('/api/location');\n        const locationData = await locationResponse.json();\n        setLocation(locationData);\n\n        const weatherResponse = await fetch(`/api/weather?lat=${locationData.latitude}&lon=${locationData.longitude}`);\n        const weatherData = await weatherResponse.json();\n        setWeather(weatherData);\n      } catch (error) {\n        console.error('Failed to fetch location or weather:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchLocationAndWeather();\n  }, [mounted]);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center space-x-2 text-gray-500\">\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600\"></div>\n        <span>加载中...</span>\n      </div>\n    );\n  }\n\n  if (!weather || !location) {\n    return (\n      <div className=\"text-gray-500 text-sm\">\n        天气信息暂不可用\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex items-center space-x-4 text-sm\">\n      <div className=\"flex items-center space-x-1\">\n        <span className=\"text-gray-600\">IP:</span>\n        <span className=\"font-mono text-orange-600\">{location.ip}</span>\n      </div>\n      <div className=\"flex items-center space-x-2 bg-blue-50 px-3 py-1 rounded-full\">\n        <span className=\"text-blue-600\">{location.city}</span>\n        <span className=\"text-gray-500\">|</span>\n        <span className=\"text-blue-600\">{weather.temperature}°C</span>\n        <span className=\"text-gray-600\">{weather.description}</span>\n      </div>\n    </div>\n  );\n}\n\nexport default function WeatherWidget() {\n  return (\n    <ClientOnly fallback={\n      <div className=\"flex items-center space-x-2 text-gray-500\">\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600\"></div>\n        <span>加载中...</span>\n      </div>\n    }>\n      <WeatherWidgetContent />\n    </ClientOnly>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAMA,SAAS;;IACP,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,WAAW;QACb;yCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,CAAC,SAAS;YAEd,MAAM;0EAA0B;oBAC9B,IAAI;wBACF,MAAM,mBAAmB,MAAM,MAAM;wBACrC,MAAM,eAAe,MAAM,iBAAiB,IAAI;wBAChD,YAAY;wBAEZ,MAAM,kBAAkB,MAAM,MAAM,AAAC,oBAAgD,OAA7B,aAAa,QAAQ,EAAC,SAA8B,OAAvB,aAAa,SAAS;wBAC3G,MAAM,cAAc,MAAM,gBAAgB,IAAI;wBAC9C,WAAW;oBACb,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wCAAwC;oBACxD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;yCAAG;QAAC;KAAQ;IAEZ,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;8BAAK;;;;;;;;;;;;IAGZ;IAEA,IAAI,CAAC,WAAW,CAAC,UAAU;QACzB,qBACE,6LAAC;YAAI,WAAU;sBAAwB;;;;;;IAI3C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;kCAChC,6LAAC;wBAAK,WAAU;kCAA6B,SAAS,EAAE;;;;;;;;;;;;0BAE1D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAiB,SAAS,IAAI;;;;;;kCAC9C,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;kCAChC,6LAAC;wBAAK,WAAU;;4BAAiB,QAAQ,WAAW;4BAAC;;;;;;;kCACrD,6LAAC;wBAAK,WAAU;kCAAiB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;AAI5D;GA/DS;KAAA;AAiEM,SAAS;IACtB,qBACE,6LAAC,mIAAA,CAAA,UAAU;QAAC,wBACV,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;8BAAK;;;;;;;;;;;;kBAGR,cAAA,6LAAC;;;;;;;;;;AAGP;MAXwB", "debugId": null}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/products/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport ProductCard from '@/components/ProductCard';\nimport ProductSearch from '@/components/ProductSearch';\nimport WeatherWidget from '@/components/WeatherWidget';\nimport ClientOnly from '@/components/ClientOnly';\nimport Link from 'next/link';\n\ninterface Product {\n  id: string;\n  name: string;\n  description: string;\n  price: number;\n  category: string;\n  image: string;\n  ingredients: string[];\n  spicyLevel: number;\n  available: boolean;\n  stock: number;\n  minStock: number;\n  isActive: boolean;\n  publishedAt: string | null;\n  createdAt: string;\n  updatedAt: string;\n  productType: {\n    name: string;\n  };\n}\n\ninterface ProductType {\n  id: string;\n  name: string;\n}\n\nexport default function ProductsPage() {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [productTypes, setProductTypes] = useState<ProductType[]>([]);\n  const [searchResults, setSearchResults] = useState<Product[]>([]);\n  const [isSearching, setIsSearching] = useState(false);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchProducts();\n    fetchProductTypes();\n  }, []);\n\n  const fetchProducts = async () => {\n    try {\n      const response = await fetch('/api/products');\n      const data = await response.json();\n      setProducts(data);\n    } catch (error) {\n      console.error('Failed to fetch products:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchProductTypes = async () => {\n    try {\n      const response = await fetch('/api/product-types');\n      const data = await response.json();\n      setProductTypes(data);\n    } catch (error) {\n      console.error('Failed to fetch product types:', error);\n    }\n  };\n\n  const handleSearch = (results: any) => {\n    console.log('Search results:', results); // 调试日志\n    if (results && results.products) {\n      setSearchResults(results.products);\n      setIsSearching(true);\n    } else {\n      setSearchResults([]);\n      setIsSearching(false);\n    }\n  };\n\n  const displayProducts = isSearching ? searchResults : products;\n\n  if (loading) {\n    return (\n      <ClientOnly fallback={<div>加载中...</div>}>\n        <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">加载中...</p>\n          </div>\n        </div>\n      </ClientOnly>\n    );\n  }\n\n  return (\n    <ClientOnly fallback={<div>加载中...</div>}>\n      <div className=\"min-h-screen bg-gray-50\">\n        <header className=\"bg-white shadow-sm\">\n          <div className=\"container mx-auto px-4 py-4\">\n            <div className=\"flex justify-between items-center\">\n              <div className=\"flex items-center space-x-4\">\n                <Link href=\"/\" className=\"text-2xl font-bold text-orange-600 hover:text-orange-700\">\n                  香香面条店\n                </Link>\n                <span className=\"text-gray-600\">菜品列表</span>\n              </div>\n              <WeatherWidget />\n            </div>\n          </div>\n        </header>\n\n        <main className=\"container mx-auto px-4 py-8\">\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-800 mb-4\">所有菜品</h1>\n            <p className=\"text-gray-600\">精选传统面食，每一道都是用心之作</p>\n          </div>\n\n          {/* 搜索组件 */}\n          <ProductSearch\n            categories={productTypes}\n            onSearch={handleSearch}\n          />\n\n          {/* 搜索结果提示 */}\n          {isSearching && (\n            <div className=\"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n              <p className=\"text-blue-800\">\n                找到 {searchResults.length} 个搜索结果\n                <button\n                  onClick={() => setIsSearching(false)}\n                  className=\"ml-4 text-blue-600 hover:text-blue-800 underline\"\n                >\n                  查看全部产品\n                </button>\n              </p>\n            </div>\n          )}\n\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {displayProducts.map((product) => (\n              <ProductCard key={product.id} product={product} />\n            ))}\n          </div>\n\n          {displayProducts.length === 0 && !loading && (\n            <div className=\"text-center py-12\">\n              <div className=\"text-6xl mb-4\">🍜</div>\n              <h2 className=\"text-xl text-gray-600\">\n                {isSearching ? '没有找到匹配的产品' : '暂无菜品'}\n              </h2>\n              {isSearching && (\n                <p className=\"text-gray-500 mt-2\">\n                  请尝试调整搜索条件或\n                  <button\n                    onClick={() => setIsSearching(false)}\n                    className=\"text-orange-600 hover:text-orange-800 underline ml-1\"\n                  >\n                    查看全部产品\n                  </button>\n                </p>\n              )}\n            </div>\n          )}\n        </main>\n      </div>\n    </ClientOnly>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAmCe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;YACA;QACF;iCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,QAAQ,GAAG,CAAC,mBAAmB,UAAU,OAAO;QAChD,IAAI,WAAW,QAAQ,QAAQ,EAAE;YAC/B,iBAAiB,QAAQ,QAAQ;YACjC,eAAe;QACjB,OAAO;YACL,iBAAiB,EAAE;YACnB,eAAe;QACjB;IACF;IAEA,MAAM,kBAAkB,cAAc,gBAAgB;IAEtD,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,UAAU;YAAC,wBAAU,6LAAC;0BAAI;;;;;;sBACzB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;IAKvC;IAEA,qBACE,6LAAC,mIAAA,CAAA,UAAU;QAAC,wBAAU,6LAAC;sBAAI;;;;;;kBACzB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAA2D;;;;;;sDAGpF,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAElC,6LAAC,sIAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;8BAKpB,6LAAC;oBAAK,WAAU;;sCACd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAI/B,6LAAC,sIAAA,CAAA,UAAa;4BACZ,YAAY;4BACZ,UAAU;;;;;;wBAIX,6BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCAAgB;oCACvB,cAAc,MAAM;oCAAC;kDACzB,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOP,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,wBACpB,6LAAC,oIAAA,CAAA,UAAW;oCAAkB,SAAS;mCAArB,QAAQ,EAAE;;;;;;;;;;wBAI/B,gBAAgB,MAAM,KAAK,KAAK,CAAC,yBAChC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CACX,cAAc,cAAc;;;;;;gCAE9B,6BACC,6LAAC;oCAAE,WAAU;;wCAAqB;sDAEhC,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GArIwB;KAAA", "debugId": null}}]}