'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

interface Product {
  id: string;
  name: string;
  available: boolean;
}

interface Store {
  id: string;
  name: string;
}

export default function AdminDashboard() {
  const [products, setProducts] = useState<Product[]>([]);
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // 检查身份验证
    const isAuth = localStorage.getItem('adminAuth');
    if (!isAuth) {
      router.push('/admin/login');
      return;
    }

    // 加载数据 - 直接导入静态数据
    const loadData = async () => {
      try {
        // 直接导入静态数据文件
        const productsModule = await import('@/data/products.json');
        const storesModule = await import('@/data/stores.json');
        
        setProducts(productsModule.default);
        setStores(storesModule.default);
      } catch (error) {
        console.error('加载数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [router]);

  const handleLogout = () => {
    localStorage.removeItem('adminAuth');
    router.push('/admin/login');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">🍜</div>
          <div className="text-lg text-gray-600">加载中...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-orange-600">管理后台</h1>
          <button
            onClick={handleLogout}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors"
          >
            退出登录
          </button>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-800">菜品总数</h3>
                <p className="text-3xl font-bold text-orange-600">{products.length}</p>
              </div>
              <div className="text-4xl text-orange-500">🍜</div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-800">门店数量</h3>
                <p className="text-3xl font-bold text-green-600">{stores.length}</p>
              </div>
              <div className="text-4xl text-green-500">🏪</div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-800">在售菜品</h3>
                <p className="text-3xl font-bold text-blue-600">
                  {products.filter(p => p.available).length}
                </p>
              </div>
              <div className="text-4xl text-blue-500">✅</div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <span className="mr-2">🍜</span>
              菜品管理
            </h2>
            <p className="text-gray-600 mb-4">管理菜品信息，包括添加、编辑和删除菜品</p>
            <div className="space-y-2">
              <Link
                href="/admin/products"
                className="block w-full bg-orange-500 text-white text-center py-2 px-4 rounded hover:bg-orange-600 transition-colors"
              >
                管理菜品
              </Link>
              <Link
                href="/admin/products/new"
                className="block w-full bg-green-500 text-white text-center py-2 px-4 rounded hover:bg-green-600 transition-colors"
              >
                添加新菜品
              </Link>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <span className="mr-2">📦</span>
              库存管理
            </h2>
            <p className="text-gray-600 mb-4">管理产品库存数量和上下架状态</p>
            <div className="space-y-2">
              <Link
                href="/admin/inventory"
                className="block w-full bg-blue-500 text-white text-center py-2 px-4 rounded hover:bg-blue-600 transition-colors"
              >
                库存管理
              </Link>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <span className="mr-2">📋</span>
              产品类型管理
            </h2>
            <p className="text-gray-600 mb-4">管理产品分类，如手工面条、饺子皮等</p>
            <div className="space-y-2">
              <Link
                href="/admin/product-types"
                className="block w-full bg-purple-500 text-white text-center py-2 px-4 rounded hover:bg-purple-600 transition-colors"
              >
                管理产品类型
              </Link>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <span className="mr-2">🏪</span>
              门店管理
            </h2>
            <p className="text-gray-600 mb-4">管理门店信息，包括位置、营业时间等</p>
            <div className="space-y-2">
              <Link
                href="/admin/stores"
                className="block w-full bg-blue-500 text-white text-center py-2 px-4 rounded hover:bg-blue-600 transition-colors"
              >
                管理门店
              </Link>
              <Link
                href="/admin/stores/new"
                className="block w-full bg-green-500 text-white text-center py-2 px-4 rounded hover:bg-green-600 transition-colors"
              >
                添加新门店
              </Link>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">快速操作</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link
              href="/"
              className="text-center p-4 border border-gray-200 rounded-lg hover:border-orange-500 hover:text-orange-600 transition-colors"
            >
              <div className="text-2xl mb-2">🏠</div>
              <div className="text-sm">返回首页</div>
            </Link>
            <Link
              href="/products"
              className="text-center p-4 border border-gray-200 rounded-lg hover:border-orange-500 hover:text-orange-600 transition-colors"
            >
              <div className="text-2xl mb-2">📱</div>
              <div className="text-sm">查看网站</div>
            </Link>
            <button className="text-center p-4 border border-gray-200 rounded-lg hover:border-red-500 hover:text-red-600 transition-colors">
              <div className="text-2xl mb-2">🗑️</div>
              <div className="text-sm">清理缓存</div>
            </button>
            <button className="text-center p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:text-green-600 transition-colors">
              <div className="text-2xl mb-2">💾</div>
              <div className="text-sm">备份数据</div>
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}