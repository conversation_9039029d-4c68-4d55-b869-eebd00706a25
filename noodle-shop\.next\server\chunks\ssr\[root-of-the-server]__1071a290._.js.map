{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/WeatherWidget.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { WeatherInfo, LocationInfo } from '@/types';\n\nexport default function WeatherWidget() {\n  const [weather, setWeather] = useState<WeatherInfo | null>(null);\n  const [location, setLocation] = useState<LocationInfo | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchLocationAndWeather = async () => {\n      try {\n        const locationResponse = await fetch('/api/location');\n        const locationData = await locationResponse.json();\n        setLocation(locationData);\n\n        const weatherResponse = await fetch(`/api/weather?lat=${locationData.latitude}&lon=${locationData.longitude}`);\n        const weatherData = await weatherResponse.json();\n        setWeather(weatherData);\n      } catch (error) {\n        console.error('Failed to fetch location or weather:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchLocationAndWeather();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center space-x-2 text-gray-500\">\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600\"></div>\n        <span>加载中...</span>\n      </div>\n    );\n  }\n\n  if (!weather || !location) {\n    return (\n      <div className=\"text-gray-500 text-sm\">\n        天气信息暂不可用\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex items-center space-x-4 text-sm\">\n      <div className=\"flex items-center space-x-1\">\n        <span className=\"text-gray-600\">IP:</span>\n        <span className=\"font-mono text-orange-600\">{location.ip}</span>\n      </div>\n      <div className=\"flex items-center space-x-2 bg-blue-50 px-3 py-1 rounded-full\">\n        <span className=\"text-blue-600\">{location.city}</span>\n        <span className=\"text-gray-500\">|</span>\n        <span className=\"text-blue-600\">{weather.temperature}°C</span>\n        <span className=\"text-gray-600\">{weather.description}</span>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,0BAA0B;YAC9B,IAAI;gBACF,MAAM,mBAAmB,MAAM,MAAM;gBACrC,MAAM,eAAe,MAAM,iBAAiB,IAAI;gBAChD,YAAY;gBAEZ,MAAM,kBAAkB,MAAM,MAAM,CAAC,iBAAiB,EAAE,aAAa,QAAQ,CAAC,KAAK,EAAE,aAAa,SAAS,EAAE;gBAC7G,MAAM,cAAc,MAAM,gBAAgB,IAAI;gBAC9C,WAAW;YACb,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;YACxD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;8BAAK;;;;;;;;;;;;IAGZ;IAEA,IAAI,CAAC,WAAW,CAAC,UAAU;QACzB,qBACE,8OAAC;YAAI,WAAU;sBAAwB;;;;;;IAI3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAgB;;;;;;kCAChC,8OAAC;wBAAK,WAAU;kCAA6B,SAAS,EAAE;;;;;;;;;;;;0BAE1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAiB,SAAS,IAAI;;;;;;kCAC9C,8OAAC;wBAAK,WAAU;kCAAgB;;;;;;kCAChC,8OAAC;wBAAK,WAAU;;4BAAiB,QAAQ,WAAW;4BAAC;;;;;;;kCACrD,8OAAC;wBAAK,WAAU;kCAAiB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;AAI5D", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/StoreLocator.tsx"], "sourcesContent": ["'use client';\n\nimport { Store } from '@/types';\nimport { useState } from 'react';\n\ninterface StoreLocatorProps {\n  stores: Store[];\n}\n\nexport default function StoreLocator({ stores }: StoreLocatorProps) {\n  const [selectedStore, setSelectedStore] = useState<Store | null>(null);\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-2\">\n        <div className=\"p-6\">\n          <h3 className=\"text-xl font-semibold mb-4\">门店列表</h3>\n          <div className=\"space-y-4\">\n            {stores.map((store) => (\n              <div\n                key={store.id}\n                className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                  selectedStore?.id === store.id\n                    ? 'border-orange-500 bg-orange-50'\n                    : 'border-gray-200 hover:border-gray-300'\n                }`}\n                onClick={() => setSelectedStore(store)}\n              >\n                <h4 className=\"font-semibold text-gray-800\">{store.name}</h4>\n                <p className=\"text-gray-600 text-sm mt-1\">{store.address}</p>\n                <div className=\"flex justify-between items-center mt-2 text-sm\">\n                  <span className=\"text-gray-500\">📞 {store.phone}</span>\n                  <span className={`${store.isOpen ? 'text-green-600' : 'text-red-600'}`}>\n                    {store.isOpen ? '营业中' : '已打烊'}\n                  </span>\n                </div>\n                <div className=\"text-xs text-gray-500 mt-1\">\n                  营业时间: {store.openTime} - {store.closeTime}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n        \n        <div className=\"bg-gray-100 p-6 flex items-center justify-center\">\n          {selectedStore ? (\n            <div className=\"text-center\">\n              <div className=\"text-6xl mb-4\">🗺️</div>\n              <h3 className=\"text-xl font-semibold mb-2\">{selectedStore.name}</h3>\n              <p className=\"text-gray-600 mb-2\">{selectedStore.address}</p>\n              <p className=\"text-sm text-gray-500\">\n                纬度: {selectedStore.latitude.toFixed(4)}, \n                经度: {selectedStore.longitude.toFixed(4)}\n              </p>\n              <button className=\"mt-4 bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 transition-colors\">\n                导航到此店\n              </button>\n            </div>\n          ) : (\n            <div className=\"text-center text-gray-500\">\n              <div className=\"text-6xl mb-4\">📍</div>\n              <p>请选择门店查看位置信息</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AASe,SAAS,aAAa,EAAE,MAAM,EAAqB;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;oCAEC,WAAW,CAAC,uDAAuD,EACjE,eAAe,OAAO,MAAM,EAAE,GAC1B,mCACA,yCACJ;oCACF,SAAS,IAAM,iBAAiB;;sDAEhC,8OAAC;4CAAG,WAAU;sDAA+B,MAAM,IAAI;;;;;;sDACvD,8OAAC;4CAAE,WAAU;sDAA8B,MAAM,OAAO;;;;;;sDACxD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAgB;wDAAI,MAAM,KAAK;;;;;;;8DAC/C,8OAAC;oDAAK,WAAW,GAAG,MAAM,MAAM,GAAG,mBAAmB,gBAAgB;8DACnE,MAAM,MAAM,GAAG,QAAQ;;;;;;;;;;;;sDAG5B,8OAAC;4CAAI,WAAU;;gDAA6B;gDACnC,MAAM,QAAQ;gDAAC;gDAAI,MAAM,SAAS;;;;;;;;mCAjBtC,MAAM,EAAE;;;;;;;;;;;;;;;;8BAwBrB,8OAAC;oBAAI,WAAU;8BACZ,8BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CAA8B,cAAc,IAAI;;;;;;0CAC9D,8OAAC;gCAAE,WAAU;0CAAsB,cAAc,OAAO;;;;;;0CACxD,8OAAC;gCAAE,WAAU;;oCAAwB;oCAC9B,cAAc,QAAQ,CAAC,OAAO,CAAC;oCAAG;oCAClC,cAAc,SAAS,CAAC,OAAO,CAAC;;;;;;;0CAEvC,8OAAC;gCAAO,WAAU;0CAAwF;;;;;;;;;;;6CAK5G,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}]}