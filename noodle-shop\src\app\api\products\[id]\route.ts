import { NextRequest } from 'next/server';
import { getProductById, updateProduct, deleteProduct, parseIngredients } from '@/lib/database';

interface RouteContext {
  params: Promise<{ id: string }>;
}

export async function GET(request: NextRequest, context: RouteContext) {
  try {
    const { id } = await context.params;
    const product = await getProductById(id);

    if (!product) {
      return Response.json({ error: 'Product not found' }, { status: 404 });
    }

    // 转换数据格式以保持与前端的兼容性
    const formattedProduct = {
      ...product,
      category: product.productTypeId,
      ingredients: parseIngredients(product.ingredients),
    };

    return Response.json(formattedProduct);
  } catch (error) {
    console.error('Failed to fetch product:', error);
    return Response.json({ error: 'Failed to fetch product' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, context: RouteContext) {
  try {
    const { id } = await context.params;
    const body = await request.json();

    // 数据验证和转换
    const updateData: any = {};
    if (body.name !== undefined) updateData.name = body.name;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.price !== undefined) updateData.price = Number(body.price);
    if (body.productTypeId !== undefined || body.category !== undefined) {
      updateData.productTypeId = body.productTypeId || body.category;
    }
    if (body.image !== undefined) updateData.image = body.image;
    if (body.ingredients !== undefined) updateData.ingredients = body.ingredients;
    if (body.spicyLevel !== undefined) updateData.spicyLevel = Number(body.spicyLevel);
    if (body.available !== undefined) updateData.available = body.available;
    if (body.stock !== undefined) updateData.stock = Number(body.stock);
    if (body.minStock !== undefined) updateData.minStock = Number(body.minStock);
    if (body.isActive !== undefined) updateData.isActive = body.isActive;

    const product = await updateProduct(id, updateData);

    // 转换返回数据格式
    const formattedProduct = {
      ...product,
      category: product.productTypeId,
      ingredients: parseIngredients(product.ingredients),
    };

    return Response.json(formattedProduct);
  } catch (error) {
    console.error('Failed to update product:', error);
    return Response.json({ error: 'Failed to update product' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, context: RouteContext) {
  try {
    const { id } = await context.params;
    await deleteProduct(id);

    return Response.json({ message: 'Product deleted successfully' });
  } catch (error: any) {
    console.error('Failed to delete product:', error);
    if (error?.code === 'P2025') {
      return Response.json({ error: 'Product not found' }, { status: 404 });
    }
    return Response.json({ error: 'Failed to delete product' }, { status: 500 });
  }
}