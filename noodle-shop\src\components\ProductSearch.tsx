'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

interface ProductSearchProps {
  categories: Array<{ id: string; name: string }>;
  onSearch?: (results: any) => void;
}

export default function ProductSearch({ categories, onSearch }: ProductSearchProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [query, setQuery] = useState(searchParams.get('q') || '');
  const [category, setCategory] = useState(searchParams.get('category') || 'all');
  const [sortBy, setSortBy] = useState(searchParams.get('sortBy') || 'createdAt');
  const [sortOrder, setSortOrder] = useState(searchParams.get('sortOrder') || 'desc');
  const [loading, setLoading] = useState(false);

  const handleSearch = async () => {
    setLoading(true);

    try {
      const params = new URLSearchParams();
      if (query.trim()) params.set('q', query.trim());
      if (category !== 'all') params.set('category', category);
      params.set('sortBy', sortBy);
      params.set('sortOrder', sortOrder);

      const response = await fetch(`/api/products/search?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();

      if (onSearch) {
        onSearch(data);
      }

      // 更新URL参数
      const newParams = new URLSearchParams(window.location.search);
      if (query.trim()) {
        newParams.set('q', query.trim());
      } else {
        newParams.delete('q');
      }
      if (category !== 'all') {
        newParams.set('category', category);
      } else {
        newParams.delete('category');
      }
      newParams.set('sortBy', sortBy);
      newParams.set('sortOrder', sortOrder);

      router.push(`${window.location.pathname}?${newParams.toString()}`);
    } catch (error) {
      console.error('Search failed:', error);
      if (onSearch) {
        onSearch({ products: [], pagination: { total: 0 } });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setQuery('');
    setCategory('all');
    setSortBy('createdAt');
    setSortOrder('desc');
    router.push(window.location.pathname);
    if (onSearch) {
      onSearch({ products: [], pagination: { total: 0 } });
    }
  };

  useEffect(() => {
    const urlQuery = searchParams.get('q');
    const urlCategory = searchParams.get('category');
    const urlSortBy = searchParams.get('sortBy');
    const urlSortOrder = searchParams.get('sortOrder');

    if (urlQuery) setQuery(urlQuery);
    if (urlCategory) setCategory(urlCategory);
    if (urlSortBy) setSortBy(urlSortBy);
    if (urlSortOrder) setSortOrder(urlSortOrder);

    // 如果URL中有搜索参数，自动执行搜索
    if (urlQuery || urlCategory) {
      setTimeout(() => handleSearch(), 100);
    }
  }, [searchParams]);

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">搜索产品</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        {/* 搜索关键词 */}
        <div>
          <label htmlFor="search-query" className="block text-sm font-medium text-gray-700 mb-2">
            搜索关键词
          </label>
          <input
            id="search-query"
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="输入产品名称、描述或配料..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
        </div>

        {/* 产品分类 */}
        <div>
          <label htmlFor="search-category" className="block text-sm font-medium text-gray-700 mb-2">
            产品分类
          </label>
          <select
            id="search-category"
            value={category}
            onChange={(e) => setCategory(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value="all">全部分类</option>
            {categories.map((cat) => (
              <option key={cat.id} value={cat.id}>
                {cat.name}
              </option>
            ))}
          </select>
        </div>

        {/* 排序方式 */}
        <div>
          <label htmlFor="search-sort" className="block text-sm font-medium text-gray-700 mb-2">
            排序方式
          </label>
          <select
            id="search-sort"
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value="createdAt">创建时间</option>
            <option value="publishedAt">发布时间</option>
            <option value="name">产品名称</option>
            <option value="price">价格</option>
          </select>
        </div>

        {/* 排序顺序 */}
        <div>
          <label htmlFor="search-order" className="block text-sm font-medium text-gray-700 mb-2">
            排序顺序
          </label>
          <select
            id="search-order"
            value={sortOrder}
            onChange={(e) => setSortOrder(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value="desc">降序</option>
            <option value="asc">升序</option>
          </select>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-3">
        <button
          onClick={handleSearch}
          disabled={loading}
          className="flex-1 bg-orange-500 text-white py-2 px-6 rounded-md hover:bg-orange-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              搜索中...
            </span>
          ) : (
            '🔍 搜索'
          )}
        </button>
        
        <button
          onClick={handleReset}
          className="bg-gray-500 text-white py-2 px-6 rounded-md hover:bg-gray-600 transition-colors"
        >
          🔄 重置
        </button>
      </div>
    </div>
  );
}
