module.exports = {

"[project]/.next-internal/server/app/api/products/search/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/errors.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// 错误处理工具类
__turbopack_context__.s({
    "AppError": ()=>AppError,
    "ConflictError": ()=>ConflictError,
    "NotFoundError": ()=>NotFoundError,
    "ValidationError": ()=>ValidationError,
    "asyncHandler": ()=>asyncHandler,
    "formatValidationErrors": ()=>formatValidationErrors,
    "handleApiError": ()=>handleApiError,
    "handleClientError": ()=>handleClientError
});
class AppError extends Error {
    statusCode;
    isOperational;
    constructor(message, statusCode = 500, isOperational = true){
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        Error.captureStackTrace(this, this.constructor);
    }
}
class ValidationError extends AppError {
    constructor(message){
        super(message, 400);
    }
}
class NotFoundError extends AppError {
    constructor(resource = 'Resource'){
        super(`${resource} not found`, 404);
    }
}
class ConflictError extends AppError {
    constructor(message){
        super(message, 409);
    }
}
function handleApiError(error) {
    console.error('API Error:', error);
    if (error instanceof AppError) {
        return Response.json({
            error: error.message
        }, {
            status: error.statusCode
        });
    }
    if (error instanceof Error) {
        return Response.json({
            error: error.message
        }, {
            status: 500
        });
    }
    return Response.json({
        error: 'Internal server error'
    }, {
        status: 500
    });
}
function asyncHandler(fn) {
    return async (...args)=>{
        try {
            return await fn(...args);
        } catch (error) {
            throw error;
        }
    };
}
function handleClientError(error) {
    if (error instanceof Error) {
        return error.message;
    }
    return '发生未知错误';
}
function formatValidationErrors(errors) {
    const messages = [];
    for (const [field, fieldErrors] of Object.entries(errors)){
        messages.push(`${field}: ${fieldErrors.join(', ')}`);
    }
    return messages.join('; ');
}
}),
"[project]/src/lib/validation.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// 数据验证工具
__turbopack_context__.s({
    "ProductTypeValidationSchema": ()=>ProductTypeValidationSchema,
    "ProductValidationSchema": ()=>ProductValidationSchema,
    "SearchParamsSchema": ()=>SearchParamsSchema,
    "StoreValidationSchema": ()=>StoreValidationSchema,
    "baseValidation": ()=>baseValidation,
    "customValidators": ()=>customValidators,
    "safeValidateData": ()=>safeValidateData,
    "validateData": ()=>validateData,
    "validatePartialData": ()=>validatePartialData
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$errors$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/errors.ts [app-route] (ecmascript)");
;
;
const baseValidation = {
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, 'ID不能为空'),
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, '名称不能为空').max(100, '名称不能超过100个字符'),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().max(500, '描述不能超过500个字符').optional(),
    price: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive('价格必须大于0'),
    phone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().regex(/^1[3-9]\d{9}$/, '请输入有效的手机号码').optional(),
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().email('请输入有效的邮箱地址').optional(),
    url: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url('请输入有效的URL').optional()
};
const ProductValidationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: baseValidation.id.optional(),
    name: baseValidation.name,
    description: baseValidation.description,
    price: baseValidation.price,
    productTypeId: baseValidation.id,
    image: baseValidation.url.optional(),
    ingredients: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).default([]),
    spicyLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().min(0).max(5).default(0),
    available: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(true),
    stock: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().min(0).default(0),
    minStock: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().min(0).default(5),
    isActive: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(true),
    publishedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().datetime().optional().nullable()
});
const ProductTypeValidationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: baseValidation.id.optional(),
    name: baseValidation.name,
    description: baseValidation.description,
    displayOrder: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().min(0).default(0),
    isActive: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(true)
});
const StoreValidationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: baseValidation.id.optional(),
    name: baseValidation.name,
    address: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, '地址不能为空').max(200, '地址不能超过200个字符'),
    phone: baseValidation.phone,
    latitude: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(-90).max(90).optional().nullable(),
    longitude: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(-180).max(180).optional().nullable(),
    openTime: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式(HH:MM)').optional(),
    closeTime: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式(HH:MM)').optional(),
    isOpen: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(true),
    image: baseValidation.url.optional()
});
const SearchParamsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    q: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().max(100, '搜索关键词不能超过100个字符').optional(),
    category: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    sortBy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        'createdAt',
        'publishedAt',
        'name',
        'price'
    ]).default('createdAt'),
    sortOrder: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        'asc',
        'desc'
    ]).default('desc'),
    page: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().min(1).default(1),
    limit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().min(1).max(100).default(12)
});
function validateData(schema, data) {
    try {
        return schema.parse(data);
    } catch (error) {
        if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].ZodError) {
            const messages = error.errors.map((err)=>`${err.path.join('.')}: ${err.message}`);
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$errors$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ValidationError"](messages.join(', '));
        }
        throw error;
    }
}
function safeValidateData(schema, data) {
    try {
        const result = schema.safeParse(data);
        if (result.success) {
            return {
                success: true,
                data: result.data
            };
        } else {
            const errors = result.error.errors.map((err)=>`${err.path.join('.')}: ${err.message}`);
            return {
                success: false,
                errors
            };
        }
    } catch (error) {
        return {
            success: false,
            errors: [
                '验证过程中发生错误'
            ]
        };
    }
}
function validatePartialData(schema, data) {
    const partialSchema = schema.partial();
    return validateData(partialSchema, data);
}
const customValidators = {
    // 验证营业时间
    validateBusinessHours: (openTime, closeTime)=>{
        if (!openTime || !closeTime) return true;
        const open = new Date(`2000-01-01 ${openTime}`);
        const close = new Date(`2000-01-01 ${closeTime}`);
        return open < close;
    },
    // 验证库存逻辑
    validateStock: (stock, minStock)=>{
        return stock >= 0 && minStock >= 0 && minStock <= stock;
    },
    // 验证价格范围
    validatePriceRange: (price, min = 0, max = 10000)=>{
        return price >= min && price <= max;
    },
    // 验证坐标
    validateCoordinates: (latitude, longitude)=>{
        if (latitude === undefined || longitude === undefined) return true;
        return latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180;
    }
};
}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ProductSchema": ()=>ProductSchema,
    "ProductTypeSchema": ()=>ProductTypeSchema,
    "StoreSchema": ()=>StoreSchema,
    "createProduct": ()=>createProduct,
    "createProductType": ()=>createProductType,
    "createStore": ()=>createStore,
    "deleteProduct": ()=>deleteProduct,
    "deleteProductType": ()=>deleteProductType,
    "deleteStore": ()=>deleteStore,
    "getActiveProducts": ()=>getActiveProducts,
    "getLowStockProducts": ()=>getLowStockProducts,
    "getProductById": ()=>getProductById,
    "getProductTypeById": ()=>getProductTypeById,
    "getProductTypes": ()=>getProductTypes,
    "getProducts": ()=>getProducts,
    "getProductsByCategory": ()=>getProductsByCategory,
    "getStockStatus": ()=>getStockStatus,
    "getStoreById": ()=>getStoreById,
    "getStores": ()=>getStores,
    "parseIngredients": ()=>parseIngredients,
    "prisma": ()=>prisma,
    "toggleProductStatus": ()=>toggleProductStatus,
    "updateProduct": ()=>updateProduct,
    "updateProductStock": ()=>updateProductStock,
    "updateProductType": ()=>updateProductType,
    "updateStore": ()=>updateStore
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$errors$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/errors.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation.ts [app-route] (ecmascript)");
;
;
;
;
;
// 全局Prisma客户端实例
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
    log: ("TURBOPACK compile-time truthy", 1) ? [
        'query',
        'error',
        'warn'
    ] : "TURBOPACK unreachable"
});
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
const ProductTypeSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    displayOrder: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().default(0),
    isActive: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(true)
});
const ProductSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    price: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    productTypeId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    image: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    ingredients: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).optional(),
    spicyLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().min(0).max(5).default(0),
    available: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(true),
    stock: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().min(0).default(0),
    minStock: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().min(0).default(5),
    isActive: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(true)
});
const StoreSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    address: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1),
    phone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    latitude: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    longitude: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    openTime: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    closeTime: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    isOpen: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(true),
    image: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
});
async function getProductTypes() {
    return await prisma.productType.findMany({
        where: {
            isActive: true
        },
        orderBy: {
            displayOrder: 'asc'
        }
    });
}
async function getProductTypeById(id) {
    return await prisma.productType.findUnique({
        where: {
            id
        }
    });
}
async function createProductType(data) {
    const validatedData = ProductTypeSchema.parse(data);
    return await prisma.productType.create({
        data: validatedData
    });
}
async function updateProductType(id, data) {
    return await prisma.productType.update({
        where: {
            id
        },
        data
    });
}
async function deleteProductType(id) {
    // 检查是否有关联的产品
    const productCount = await prisma.product.count({
        where: {
            productTypeId: id
        }
    });
    if (productCount > 0) {
        throw new Error('无法删除：该产品类型下还有产品');
    }
    return await prisma.productType.delete({
        where: {
            id
        }
    });
}
async function getProducts() {
    return await prisma.product.findMany({
        include: {
            productType: true
        },
        orderBy: {
            createdAt: 'desc'
        }
    });
}
async function getProductById(id) {
    return await prisma.product.findUnique({
        where: {
            id
        },
        include: {
            productType: true
        }
    });
}
async function getProductsByCategory(productTypeId) {
    return await prisma.product.findMany({
        where: {
            productTypeId
        },
        include: {
            productType: true
        },
        orderBy: {
            createdAt: 'desc'
        }
    });
}
async function createProduct(data) {
    const validatedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateData"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ProductValidationSchema"].omit({
        id: true
    }), data);
    // 检查产品类型是否存在
    const productType = await prisma.productType.findUnique({
        where: {
            id: validatedData.productTypeId
        }
    });
    if (!productType) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$errors$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NotFoundError"]('产品类型');
    }
    // 生成新ID
    const newId = Date.now().toString();
    return await prisma.product.create({
        data: {
            ...validatedData,
            id: newId,
            ingredients: validatedData.ingredients ? JSON.stringify(validatedData.ingredients) : null,
            publishedAt: validatedData.isActive ? new Date() : null
        },
        include: {
            productType: true
        }
    });
}
async function updateProduct(id, data) {
    const updateData = {
        ...data
    };
    if (data.ingredients) {
        updateData.ingredients = JSON.stringify(data.ingredients);
    }
    return await prisma.product.update({
        where: {
            id
        },
        data: updateData,
        include: {
            productType: true
        }
    });
}
async function deleteProduct(id) {
    return await prisma.product.delete({
        where: {
            id
        }
    });
}
async function getStores() {
    return await prisma.store.findMany({
        orderBy: {
            createdAt: 'desc'
        }
    });
}
async function getStoreById(id) {
    return await prisma.store.findUnique({
        where: {
            id
        }
    });
}
async function createStore(data) {
    const validatedData = StoreSchema.omit({
        id: true
    }).parse(data);
    // 生成新ID
    const newId = Date.now().toString();
    return await prisma.store.create({
        data: {
            ...validatedData,
            id: newId
        }
    });
}
async function updateStore(id, data) {
    return await prisma.store.update({
        where: {
            id
        },
        data
    });
}
async function deleteStore(id) {
    return await prisma.store.delete({
        where: {
            id
        }
    });
}
async function updateProductStock(id, quantity) {
    const product = await prisma.product.findUnique({
        where: {
            id
        }
    });
    if (!product) throw new Error('产品不存在');
    const newStock = Math.max(0, product.stock + quantity);
    return await prisma.product.update({
        where: {
            id
        },
        data: {
            stock: newStock,
            available: newStock > 0
        },
        include: {
            productType: true
        }
    });
}
async function toggleProductStatus(id) {
    const product = await prisma.product.findUnique({
        where: {
            id
        }
    });
    if (!product) throw new Error('产品不存在');
    return await prisma.product.update({
        where: {
            id
        },
        data: {
            isActive: !product.isActive
        },
        include: {
            productType: true
        }
    });
}
async function getLowStockProducts() {
    return await prisma.product.findMany({
        where: {
            OR: [
                {
                    stock: {
                        lte: prisma.product.fields.minStock
                    }
                },
                {
                    stock: 0
                }
            ]
        },
        include: {
            productType: true
        },
        orderBy: {
            stock: 'asc'
        }
    });
}
async function getActiveProducts() {
    return await prisma.product.findMany({
        where: {
            isActive: true,
            available: true
        },
        include: {
            productType: true
        },
        orderBy: {
            createdAt: 'desc'
        }
    });
}
function parseIngredients(ingredients) {
    if (!ingredients) return [];
    try {
        return JSON.parse(ingredients);
    } catch  {
        return [];
    }
}
function getStockStatus(stock, minStock) {
    if (stock === 0) return {
        status: 'out_of_stock',
        label: '缺货',
        color: 'red'
    };
    if (stock <= minStock) return {
        status: 'low_stock',
        label: '库存不足',
        color: 'yellow'
    };
    return {
        status: 'in_stock',
        label: '库存充足',
        color: 'green'
    };
}
}),
"[project]/src/app/api/products/search/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const query = searchParams.get('q') || '';
        const category = searchParams.get('category') || '';
        const sortBy = searchParams.get('sortBy') || 'createdAt';
        const sortOrder = searchParams.get('sortOrder') || 'desc';
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '12');
        console.log('Search params:', {
            query,
            category,
            sortBy,
            sortOrder,
            page,
            limit
        });
        const skip = (page - 1) * limit;
        // 构建搜索条件
        const where = {
            isActive: true
        };
        // 添加搜索关键词条件
        if (query) {
            where.OR = [
                {
                    name: {
                        contains: query
                    }
                },
                {
                    description: {
                        contains: query
                    }
                },
                {
                    ingredients: {
                        contains: query
                    }
                }
            ];
        }
        // 添加分类筛选
        if (category && category !== 'all') {
            where.productTypeId = category;
        }
        // 构建排序条件
        const orderBy = {};
        if (sortBy === 'price') {
            orderBy.price = sortOrder;
        } else if (sortBy === 'name') {
            orderBy.name = sortOrder;
        } else if (sortBy === 'publishedAt') {
            orderBy.publishedAt = sortOrder;
        } else {
            orderBy.createdAt = sortOrder;
        }
        console.log('Search where condition:', JSON.stringify(where, null, 2));
        console.log('Search orderBy:', orderBy);
        // 执行搜索
        const [products, total] = await Promise.all([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.findMany({
                where,
                include: {
                    productType: true
                },
                orderBy,
                skip,
                take: limit
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.count({
                where
            })
        ]);
        console.log(`Found ${products.length} products out of ${total} total`);
        // 转换数据格式
        const formattedProducts = products.map((product)=>({
                ...product,
                category: product.productTypeId,
                ingredients: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseIngredients"])(product.ingredients),
                stock: product.stock || 0,
                minStock: product.minStock || 5,
                isActive: product.isActive !== false,
                publishedAt: product.publishedAt?.toISOString() || null,
                createdAt: product.createdAt.toISOString(),
                updatedAt: product.updatedAt.toISOString()
            }));
        return Response.json({
            products: formattedProducts,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                hasNext: page * limit < total,
                hasPrev: page > 1
            }
        });
    } catch (error) {
        console.error('Failed to search products:', error);
        return Response.json({
            error: 'Failed to search products'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__b5e4dfc6._.js.map