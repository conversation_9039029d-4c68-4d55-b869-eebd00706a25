import { getProducts, getStores, parseIngredients } from '@/lib/database';
import ProductCard from '@/components/ProductCard';
import WeatherWidget from '@/components/WeatherWidget';
import StoreLocator from '@/components/StoreLocator';
import Link from 'next/link';

export default async function Home() {
  const rawProducts = await getProducts();
  const rawStores = await getStores();

  // 转换产品数据格式以保持兼容性，只显示上架的产品
  const products = rawProducts
    .filter(product => (product as any).isActive !== false) // 只显示上架的产品
    .map(product => ({
      ...product,
      category: product.productTypeId,
      description: product.description || '',
      ingredients: parseIngredients(product.ingredients),
      image: product.image || '',
      stock: (product as any).stock || 0,
      minStock: (product as any).minStock || 5,
      isActive: (product as any).isActive !== false,
      publishedAt: (product as any).publishedAt?.toISOString() || null,
      createdAt: product.createdAt.toISOString(),
      updatedAt: product.updatedAt.toISOString(),
    }));

  // 转换门店数据格式以保持兼容性
  const stores = rawStores.map(store => ({
    ...store,
    phone: store.phone || '',
    latitude: store.latitude || 0,
    longitude: store.longitude || 0,
    openTime: store.openTime || '',
    closeTime: store.closeTime || '',
    image: store.image || '',
    createdAt: store.createdAt.toISOString(),
    updatedAt: store.updatedAt.toISOString(),
  }));

  const featuredProducts = products.slice(0, 6);

  return (
    <div className="min-h-screen bg-gradient-to-b from-orange-50 to-white">
      <header className="bg-white shadow-lg">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <h1 className="text-3xl font-bold text-orange-600">香香手工面食店</h1>
              <span className="text-gray-600">纯手工制作 · 新鲜现做</span>
            </div>
            <div className="flex items-center space-x-4">
              <WeatherWidget />
              <Link
                href="/admin/login"
                className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors text-sm font-medium"
              >
                管理登录
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <section className="mb-12 text-center">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">传统手工 · 品质保证</h2>
          <p className="text-xl text-gray-600 mb-6">专注手工面食制作，为您提供最新鲜的产品</p>

          {/* 搜索入口 */}
          <div className="max-w-md mx-auto mb-8">
            <Link
              href="/products"
              className="flex items-center justify-center w-full bg-orange-500 text-white py-3 px-6 rounded-lg hover:bg-orange-600 transition-colors text-lg font-medium"
            >
              🔍 搜索产品
            </Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="text-orange-500 text-4xl mb-4">👐</div>
              <h3 className="text-xl font-semibold mb-2">纯手工制作</h3>
              <p className="text-gray-600">拒绝机器生产，坚持传统手工工艺</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="text-orange-500 text-4xl mb-4">🌾</div>
              <h3 className="text-xl font-semibold mb-2">优质原料</h3>
              <p className="text-gray-600">精选高筋面粉，不添加任何防腐剂</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="text-orange-500 text-4xl mb-4">🕐</div>
              <h3 className="text-xl font-semibold mb-2">新鲜现做</h3>
              <p className="text-gray-600">当日制作当日售，保证产品新鲜度</p>
            </div>
          </div>
        </section>

        <section className="mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">热销产品</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
          <div className="text-center mt-8">
            <Link
              href="/products"
              className="inline-block bg-orange-500 text-white px-8 py-3 rounded-lg hover:bg-orange-600 transition-colors text-lg font-semibold"
            >
              查看全部产品
            </Link>
          </div>
        </section>

        <section className="mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">门店位置</h2>
          <StoreLocator stores={stores} />
          <div className="text-center mt-6">
            <Link
              href="/stores"
              className="inline-block bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors"
            >
              查看所有门店地图
            </Link>
          </div>
        </section>
      </main>

      <footer className="bg-gray-800 text-white py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">香香手工面食店</h3>
              <p className="text-gray-300">传承手工技艺，品味家的温暖</p>
              <p className="text-gray-300 mt-2">主营：手工面条、饺子皮、馄饨皮、烧饼等</p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">联系我们</h3>
              <p className="text-gray-300">服务热线：400-888-8888</p>
              <p className="text-gray-300">营业时间：6:00-20:00</p>
              <p className="text-gray-300">支持批发零售，欢迎来电咨询</p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">关注我们</h3>
              <p className="text-gray-300">微信公众号：香香手工面食</p>
              <p className="text-gray-300">添加微信，了解每日新品</p>
            </div>
          </div>
          <div className="text-center mt-8 pt-8 border-t border-gray-700">
            <p className="text-gray-300">&copy; 2024 香香手工面食店. 保留所有权利.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
