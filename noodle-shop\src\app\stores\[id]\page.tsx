import { getStoreById } from '@/lib/database';
import WeatherWidget from '@/components/WeatherWidget';
import Link from 'next/link';
import Image from 'next/image';
import { notFound } from 'next/navigation';

interface StoreDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function StoreDetailPage({ params }: StoreDetailPageProps) {
  const { id } = await params;
  const rawStore = await getStoreById(id);
  
  if (!rawStore) {
    notFound();
  }

  // 转换门店数据格式以保持兼容性
  const store = {
    ...rawStore,
    phone: rawStore.phone || '',
    latitude: rawStore.latitude || 0,
    longitude: rawStore.longitude || 0,
    openTime: rawStore.openTime || '',
    closeTime: rawStore.closeTime || '',
    image: rawStore.image || '/images/default-store.svg',
    createdAt: rawStore.createdAt.toISOString(),
    updatedAt: rawStore.updatedAt.toISOString(),
  };

  const isCurrentlyOpen = () => {
    if (!store.isOpen || !store.openTime || !store.closeTime) return false;
    
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    const [openHour, openMin] = store.openTime.split(':').map(Number);
    const [closeHour, closeMin] = store.closeTime.split(':').map(Number);
    const openTime = openHour * 60 + openMin;
    const closeTime = closeHour * 60 + closeMin;
    
    return currentTime >= openTime && currentTime <= closeTime;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-2xl font-bold text-orange-600 hover:text-orange-700">
                香香面条店
              </Link>
              <span className="text-gray-600">门店详情</span>
            </div>
            <div className="flex items-center space-x-4">
              <WeatherWidget />
              <Link
                href="/stores"
                className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors text-sm font-medium"
              >
                所有门店
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="relative h-64 md:h-80">
              <Image
                src={store.image}
                alt={store.name}
                fill
                className="object-cover"
              />
              <div className="absolute top-4 right-4">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  isCurrentlyOpen() 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {isCurrentlyOpen() ? '🟢 营业中' : '🔴 已打烊'}
                </span>
              </div>
            </div>
            
            <div className="p-8">
              <div className="flex items-center justify-between mb-6">
                <h1 className="text-3xl font-bold text-gray-800">{store.name}</h1>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  store.isOpen 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {store.isOpen ? '正常营业' : '暂停营业'}
                </span>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                <div className="space-y-6">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="text-lg font-semibold mb-3 flex items-center">
                      📍 门店地址
                    </h3>
                    <p className="text-gray-700">{store.address}</p>
                  </div>
                  
                  {store.phone && (
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-3 flex items-center">
                        📞 联系电话
                      </h3>
                      <a 
                        href={`tel:${store.phone}`}
                        className="text-orange-600 hover:text-orange-700 font-medium"
                      >
                        {store.phone}
                      </a>
                    </div>
                  )}
                </div>
                
                <div className="space-y-6">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="text-lg font-semibold mb-3 flex items-center">
                      🕒 营业时间
                    </h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">开门时间：</span>
                        <span className="font-medium">{store.openTime || '未设置'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">关门时间：</span>
                        <span className="font-medium">{store.closeTime || '未设置'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">当前状态：</span>
                        <span className={`font-medium ${
                          isCurrentlyOpen() ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {isCurrentlyOpen() ? '营业中' : '已打烊'}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {(store.latitude !== 0 && store.longitude !== 0) && (
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-3 flex items-center">
                        🗺️ 地理位置
                      </h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">纬度：</span>
                          <span className="font-mono">{store.latitude}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">经度：</span>
                          <span className="font-mono">{store.longitude}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="bg-blue-50 p-4 rounded-lg mb-6">
                <h4 className="font-semibold text-blue-800 mb-2">🍜 门店特色</h4>
                <p className="text-blue-700 text-sm">
                  本店专注于手工制作各类新鲜面食，采用传统工艺，现做现卖，
                  确保每一份面食都保持最佳的口感和营养。欢迎您的光临！
                </p>
              </div>
              
              <div className="border-t pt-6 mb-6">
                <h4 className="font-semibold text-gray-800 mb-3">门店信息</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">开店时间：</span>
                    <span className="text-gray-700">{new Date(store.createdAt).toLocaleDateString('zh-CN')}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">信息更新：</span>
                    <span className="text-gray-700">{new Date(store.updatedAt).toLocaleDateString('zh-CN')}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                {(store.latitude !== 0 && store.longitude !== 0) && (
                  <a
                    href={`https://maps.google.com/?q=${store.latitude},${store.longitude}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 bg-blue-500 text-white py-3 px-6 rounded-lg hover:bg-blue-600 transition-colors text-center font-semibold"
                  >
                    📍 在地图中查看
                  </a>
                )}
                
                {store.phone && (
                  <a
                    href={`tel:${store.phone}`}
                    className="flex-1 bg-green-500 text-white py-3 px-6 rounded-lg hover:bg-green-600 transition-colors text-center font-semibold"
                  >
                    📞 拨打电话
                  </a>
                )}
                
                <Link
                  href="/stores"
                  className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg hover:border-orange-500 hover:text-orange-600 transition-colors text-center font-semibold"
                >
                  返回门店列表
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
