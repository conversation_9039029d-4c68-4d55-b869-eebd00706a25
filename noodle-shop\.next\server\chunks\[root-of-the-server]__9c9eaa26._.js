module.exports = {

"[project]/.next-internal/server/app/api/products/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[project]/src/data/products.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("[{\"id\":\"1\",\"name\":\"手工拉面\",\"description\":\"纯手工拉制，面条劲道有弹性，可选粗细\",\"price\":12,\"category\":\"fresh-noodles\",\"productTypeId\":\"fresh-noodles\",\"image\":\"/images/lanzhou-beef-noodles.svg\",\"ingredients\":[\"高筋面粉\",\"食用盐\",\"碱水\"],\"spicyLevel\":0,\"available\":true,\"createdAt\":\"2024-01-01T00:00:00Z\",\"updatedAt\":\"2024-01-01T00:00:00Z\"},{\"id\":\"2\",\"name\":\"手工刀削面\",\"description\":\"传统刀削工艺，面片厚薄均匀，口感独特\",\"price\":15,\"category\":\"fresh-noodles\",\"productTypeId\":\"fresh-noodles\",\"image\":\"/images/dao-xiao-noodles.svg\",\"ingredients\":[\"高筋面粉\",\"鸡蛋\",\"食用盐\"],\"spicyLevel\":0,\"available\":true,\"createdAt\":\"2024-01-01T00:00:00Z\",\"updatedAt\":\"2024-01-01T00:00:00Z\"},{\"id\":\"3\",\"name\":\"手工饺子皮\",\"description\":\"现擀现卖，薄厚适中，包饺子不破皮，每斤约80张\",\"price\":18,\"category\":\"dumpling-wrappers\",\"productTypeId\":\"dumpling-wrappers\",\"image\":\"/images/sour-soup-pasta.svg\",\"ingredients\":[\"中筋面粉\",\"温水\",\"食用盐\"],\"spicyLevel\":0,\"available\":true,\"createdAt\":\"2024-01-01T00:00:00Z\",\"updatedAt\":\"2024-01-01T00:00:00Z\"},{\"id\":\"4\",\"name\":\"手工宽面条\",\"description\":\"宽度2-3厘米，适合炒制或凉拌，口感爽滑\",\"price\":13,\"category\":\"fresh-noodles\",\"productTypeId\":\"fresh-noodles\",\"image\":\"/images/oil-splashed-noodles.svg\",\"ingredients\":[\"高筋面粉\",\"鸡蛋\",\"食用油\"],\"spicyLevel\":0,\"available\":true,\"createdAt\":\"2024-01-01T00:00:00Z\",\"updatedAt\":\"2024-01-01T00:00:00Z\"},{\"id\":\"5\",\"name\":\"传统烧饼\",\"description\":\"老面发酵，芝麻香酥，外脆内软，可夹肉夹菜\",\"price\":5,\"category\":\"shaobing\",\"productTypeId\":\"shaobing\",\"image\":\"/images/mutton-pita-bread-soup.svg\",\"ingredients\":[\"面粉\",\"老面\",\"芝麻\",\"食用油\"],\"spicyLevel\":0,\"available\":true,\"createdAt\":\"2024-01-01T00:00:00Z\",\"updatedAt\":\"2024-01-01T00:00:00Z\"},{\"id\":\"6\",\"name\":\"手工细面条\",\"description\":\"细如发丝，适合做汤面，易消化\",\"price\":14,\"category\":\"fresh-noodles\",\"productTypeId\":\"fresh-noodles\",\"image\":\"/images/saozi-noodles.svg\",\"ingredients\":[\"高筋面粉\",\"食用盐\",\"鸡蛋清\"],\"spicyLevel\":0,\"available\":true,\"createdAt\":\"2024-01-01T00:00:00Z\",\"updatedAt\":\"2024-01-01T00:00:00Z\"},{\"id\":\"7\",\"name\":\"手工馄饨皮\",\"description\":\"薄如纸，透光可见，每斤约120张\",\"price\":20,\"category\":\"wonton-wrappers\",\"productTypeId\":\"wonton-wrappers\",\"image\":\"/images/sour-soup-pasta.svg\",\"ingredients\":[\"中筋面粉\",\"淀粉\",\"鸡蛋\"],\"spicyLevel\":0,\"available\":true,\"createdAt\":\"2024-01-01T00:00:00Z\",\"updatedAt\":\"2024-01-01T00:00:00Z\"},{\"id\":\"8\",\"name\":\"手工面片\",\"description\":\"可做烩面片、炒面片，厚度适中，不易烂\",\"price\":12,\"category\":\"noodle-sheets\",\"productTypeId\":\"noodle-sheets\",\"image\":\"/images/dao-xiao-noodles.svg\",\"ingredients\":[\"中筋面粉\",\"食用盐\",\"温水\"],\"spicyLevel\":0,\"available\":true,\"createdAt\":\"2024-01-01T00:00:00Z\",\"updatedAt\":\"2024-01-01T00:00:00Z\"},{\"id\":\"9\",\"name\":\"花卷馒头\",\"description\":\"手工发面，松软香甜，有原味、葱花、糖等口味\",\"price\":3,\"category\":\"steamed-buns\",\"productTypeId\":\"steamed-buns\",\"image\":\"/images/mutton-pita-bread-soup.svg\",\"ingredients\":[\"面粉\",\"酵母\",\"白糖\",\"葱花\"],\"spicyLevel\":0,\"available\":true,\"createdAt\":\"2024-01-01T00:00:00Z\",\"updatedAt\":\"2024-01-01T00:00:00Z\"},{\"id\":\"10\",\"name\":\"手工凉皮\",\"description\":\"爽滑筋道，可凉拌可热炒，夏季热销\",\"price\":10,\"category\":\"noodle-sheets\",\"productTypeId\":\"noodle-sheets\",\"image\":\"/images/oil-splashed-noodles.svg\",\"ingredients\":[\"面粉\",\"淀粉\",\"食用油\"],\"spicyLevel\":0,\"available\":true,\"createdAt\":\"2024-01-01T00:00:00Z\",\"updatedAt\":\"2024-01-01T00:00:00Z\"}]"));}),
"[project]/src/data/stores.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("[{\"id\":\"1\",\"name\":\"香香手工面食店总店\",\"address\":\"北京市朝阳区建国路88号\",\"phone\":\"010-88888888\",\"latitude\":39.9042,\"longitude\":116.4074,\"openTime\":\"06:00\",\"closeTime\":\"20:00\",\"isOpen\":true,\"createdAt\":\"2024-01-01T00:00:00Z\",\"updatedAt\":\"2024-01-01T00:00:00Z\"},{\"id\":\"2\",\"name\":\"香香手工面食店朝阳分店\",\"address\":\"北京市朝阳区望京西路66号\",\"phone\":\"010-66666666\",\"latitude\":39.9961,\"longitude\":116.4761,\"openTime\":\"06:00\",\"closeTime\":\"19:30\",\"isOpen\":true,\"createdAt\":\"2024-01-01T00:00:00Z\",\"updatedAt\":\"2024-01-01T00:00:00Z\"},{\"id\":\"3\",\"name\":\"香香手工面食店海淀分店\",\"address\":\"北京市海淀区中关村大街100号\",\"phone\":\"010-55555555\",\"latitude\":39.9861,\"longitude\":116.3061,\"openTime\":\"06:30\",\"closeTime\":\"20:00\",\"isOpen\":true,\"createdAt\":\"2024-01-01T00:00:00Z\",\"updatedAt\":\"2024-01-01T00:00:00Z\"}]"));}),
"[externals]/fs/promises [external] (fs/promises, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs/promises", () => require("fs/promises"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/lib/data.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "addProduct": ()=>addProduct,
    "addStore": ()=>addStore,
    "deleteProduct": ()=>deleteProduct,
    "deleteStore": ()=>deleteStore,
    "getProductById": ()=>getProductById,
    "getProducts": ()=>getProducts,
    "getProductsByCategory": ()=>getProductsByCategory,
    "getStoreById": ()=>getStoreById,
    "getStores": ()=>getStores,
    "saveProducts": ()=>saveProducts,
    "saveStores": ()=>saveStores,
    "updateProduct": ()=>updateProduct,
    "updateStore": ()=>updateStore
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$products$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/products.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$stores$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/stores.json (json)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs/promises [external] (fs/promises, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
;
const PRODUCTS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'src/data/products.json');
const STORES_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'src/data/stores.json');
async function getProducts() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$products$2e$json__$28$json$29$__["default"];
}
async function getProductById(id) {
    const products = await getProducts();
    return products.find((product)=>product.id === id) || null;
}
async function getProductsByCategory(category) {
    const products = await getProducts();
    return products.filter((product)=>product.category === category);
}
async function getStores() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$stores$2e$json__$28$json$29$__["default"];
}
async function getStoreById(id) {
    const stores = await getStores();
    return stores.find((store)=>store.id === id) || null;
}
async function saveProducts(products) {
    await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].writeFile(PRODUCTS_FILE, JSON.stringify(products, null, 2));
}
async function saveStores(stores) {
    await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].writeFile(STORES_FILE, JSON.stringify(stores, null, 2));
}
async function addProduct(product) {
    const products = await getProducts();
    const newProduct = {
        ...product,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    products.push(newProduct);
    await saveProducts(products);
    return newProduct;
}
async function updateProduct(id, updates) {
    const products = await getProducts();
    const index = products.findIndex((product)=>product.id === id);
    if (index === -1) return null;
    products[index] = {
        ...products[index],
        ...updates,
        updatedAt: new Date().toISOString()
    };
    await saveProducts(products);
    return products[index];
}
async function deleteProduct(id) {
    const products = await getProducts();
    const index = products.findIndex((product)=>product.id === id);
    if (index === -1) return false;
    products.splice(index, 1);
    await saveProducts(products);
    return true;
}
async function addStore(store) {
    const stores = await getStores();
    const newStore = {
        ...store,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    stores.push(newStore);
    await saveStores(stores);
    return newStore;
}
async function updateStore(id, updates) {
    const stores = await getStores();
    const index = stores.findIndex((store)=>store.id === id);
    if (index === -1) return null;
    stores[index] = {
        ...stores[index],
        ...updates,
        updatedAt: new Date().toISOString()
    };
    await saveStores(stores);
    return stores[index];
}
async function deleteStore(id) {
    const stores = await getStores();
    const index = stores.findIndex((store)=>store.id === id);
    if (index === -1) return false;
    stores.splice(index, 1);
    await saveStores(stores);
    return true;
}
}),
"[project]/src/app/api/products/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/data.ts [app-route] (ecmascript)");
;
async function GET() {
    try {
        const products = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getProducts"])();
        return Response.json(products);
    } catch (error) {
        console.error('Failed to fetch products:', error);
        return Response.json({
            error: 'Failed to fetch products'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const product = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addProduct"])(body);
        return Response.json(product, {
            status: 201
        });
    } catch (error) {
        console.error('Failed to create product:', error);
        return Response.json({
            error: 'Failed to create product'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__9c9eaa26._.js.map