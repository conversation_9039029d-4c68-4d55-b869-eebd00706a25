{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, ReactNode } from 'react';\r\n\r\ninterface ClientOnlyProps {\r\n  children: ReactNode;\r\n  fallback?: ReactNode;\r\n}\r\n\r\nexport default function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\r\n  const [hasMounted, setHasMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setHasMounted(true);\r\n  }, []);\r\n\r\n  if (!hasMounted) {\r\n    return <>{fallback}</>;\r\n  }\r\n\r\n  return <>{children}</>;\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;IAC/E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/StoreMap.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useRef, useState } from 'react';\r\nimport { Store } from '@/types';\r\nimport ClientOnly from './ClientOnly';\r\n\r\ninterface StoreMapProps {\r\n  stores: Store[];\r\n  selectedStore?: Store | null;\r\n  onStoreSelect?: (store: Store) => void;\r\n  height?: string;\r\n  enableSearch?: boolean;\r\n  showCurrentLocation?: boolean;\r\n}\r\n\r\ndeclare global {\r\n  interface Window {\r\n    BMap: any;\r\n    BMAP_STATUS_SUCCESS: any;\r\n    initBaiduMap?: () => void;\r\n  }\r\n}\r\n\r\nfunction StoreMapContent({\r\n  stores,\r\n  selectedStore,\r\n  onStoreSelect,\r\n  height = '400px',\r\n  enableSearch = false,\r\n  showCurrentLocation = true\r\n}: StoreMapProps) {\r\n  const mapContainerRef = useRef<HTMLDivElement>(null);\r\n  const mapRef = useRef<any>(null);\r\n  const markersRef = useRef<any[]>([]);\r\n  const [isMapLoaded, setIsMapLoaded] = useState(false);\r\n  const [searchKeyword, setSearchKeyword] = useState('');\r\n  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // 加载百度地图脚本\r\n  useEffect(() => {\r\n    if (!mounted) return;\r\n    \r\n    if (typeof window !== 'undefined' && !window.BMap) {\r\n      const script = document.createElement('script');\r\n      script.src = `//api.map.baidu.com/api?v=3.0&ak=YOUR_BAIDU_MAP_KEY&callback=initBaiduMap`;\r\n      script.async = true;\r\n      \r\n      window.initBaiduMap = () => {\r\n        setIsMapLoaded(true);\r\n      };\r\n      \r\n      document.head.appendChild(script);\r\n      \r\n      return () => {\r\n        delete window.initBaiduMap;\r\n        if (document.head.contains(script)) {\r\n          document.head.removeChild(script);\r\n        }\r\n      };\r\n    } else if (window.BMap) {\r\n      setIsMapLoaded(true);\r\n    }\r\n  }, [mounted]);\r\n\r\n  // 初始化地图\r\n  useEffect(() => {\r\n    if (!mounted || !isMapLoaded || !mapContainerRef.current || !window.BMap) return;\r\n\r\n    const map = new window.BMap.Map(mapContainerRef.current);\r\n    const defaultCenter = new window.BMap.Point(116.404, 39.915); // 北京天安门\r\n    map.centerAndZoom(defaultCenter, 12);\r\n    map.enableScrollWheelZoom(true);\r\n    \r\n    // 添加地图控件\r\n    map.addControl(new window.BMap.NavigationControl());\r\n    map.addControl(new window.BMap.ScaleControl());\r\n    \r\n    mapRef.current = map;\r\n\r\n    // 获取用户当前位置\r\n    if (showCurrentLocation && navigator.geolocation) {\r\n      navigator.geolocation.getCurrentPosition(\r\n        (position) => {\r\n          const { latitude, longitude } = position.coords;\r\n          setUserLocation({ lat: latitude, lng: longitude });\r\n          \r\n          const userPoint = new window.BMap.Point(longitude, latitude);\r\n          map.centerAndZoom(userPoint, 14);\r\n          \r\n          // 添加用户位置标记\r\n          const userMarker = new window.BMap.Marker(userPoint, {\r\n            icon: new window.BMap.Icon(\r\n              '/images/user-location.png',\r\n              new window.BMap.Size(30, 30)\r\n            )\r\n          });\r\n          map.addOverlay(userMarker);\r\n        },\r\n        (error) => {\r\n          console.error('获取位置失败:', error);\r\n        }\r\n      );\r\n    }\r\n  }, [mounted, isMapLoaded, showCurrentLocation]);\r\n\r\n  // 添加店铺标记\r\n  useEffect(() => {\r\n    if (!mapRef.current || !window.BMap) return;\r\n\r\n    // 清除旧标记\r\n    markersRef.current.forEach(marker => {\r\n      mapRef.current.removeOverlay(marker);\r\n    });\r\n    markersRef.current = [];\r\n\r\n    // 添加新标记\r\n    stores.forEach((store, index) => {\r\n      const point = new window.BMap.Point(store.longitude, store.latitude);\r\n      const marker = new window.BMap.Marker(point);\r\n      \r\n      // 自定义标记图标\r\n      const icon = new window.BMap.Icon(\r\n        store.isOpen ? '/images/store-open.png' : '/images/store-closed.png',\r\n        new window.BMap.Size(40, 40)\r\n      );\r\n      marker.setIcon(icon);\r\n      \r\n      // 添加标记点击事件\r\n      marker.addEventListener('click', () => {\r\n        if (onStoreSelect) {\r\n          onStoreSelect(store);\r\n        }\r\n        \r\n        // 显示信息窗口\r\n        const infoWindow = new window.BMap.InfoWindow(`\r\n          <div style=\"padding: 10px;\">\r\n            <h3 style=\"margin: 0 0 10px 0;\">${store.name}</h3>\r\n            <p style=\"margin: 5px 0;\">地址：${store.address}</p>\r\n            <p style=\"margin: 5px 0;\">电话：${store.phone}</p>\r\n            <p style=\"margin: 5px 0;\">营业时间：${store.openTime} - ${store.closeTime}</p>\r\n            <p style=\"margin: 5px 0;\">状态：${store.isOpen ? '营业中' : '已打烊'}</p>\r\n          </div>\r\n        `);\r\n        mapRef.current.openInfoWindow(infoWindow, point);\r\n      });\r\n      \r\n      mapRef.current.addOverlay(marker);\r\n      markersRef.current.push(marker);\r\n      \r\n      // 如果是第一个店铺，设置为地图中心\r\n      if (index === 0 && !userLocation) {\r\n        mapRef.current.centerAndZoom(point, 14);\r\n      }\r\n    });\r\n\r\n    // 如果有选中的店铺，居中显示\r\n    if (selectedStore) {\r\n      const point = new window.BMap.Point(selectedStore.longitude, selectedStore.latitude);\r\n      mapRef.current.centerAndZoom(point, 16);\r\n    }\r\n  }, [stores, selectedStore, onStoreSelect, userLocation]);\r\n\r\n  // 搜索店铺\r\n  const handleSearch = () => {\r\n    if (!searchKeyword.trim()) return;\r\n    \r\n    const foundStore = stores.find(store => \r\n      store.name.includes(searchKeyword) || \r\n      store.address.includes(searchKeyword)\r\n    );\r\n    \r\n    if (foundStore && onStoreSelect) {\r\n      onStoreSelect(foundStore);\r\n    }\r\n  };\r\n\r\n  // 查找最近的店铺\r\n  const findNearestStore = () => {\r\n    if (!userLocation || stores.length === 0) return;\r\n    \r\n    let nearestStore = stores[0];\r\n    let minDistance = Number.MAX_VALUE;\r\n    \r\n    stores.forEach(store => {\r\n      const distance = Math.sqrt(\r\n        Math.pow(store.latitude - userLocation.lat, 2) + \r\n        Math.pow(store.longitude - userLocation.lng, 2)\r\n      );\r\n      \r\n      if (distance < minDistance) {\r\n        minDistance = distance;\r\n        nearestStore = store;\r\n      }\r\n    });\r\n    \r\n    if (onStoreSelect) {\r\n      onStoreSelect(nearestStore);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {enableSearch && (\r\n        <div className=\"absolute top-4 left-4 z-10 bg-white rounded-lg shadow-md p-3\">\r\n          <div className=\"flex gap-2\">\r\n            <input\r\n              type=\"text\"\r\n              value={searchKeyword}\r\n              onChange={(e) => setSearchKeyword(e.target.value)}\r\n              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}\r\n              placeholder=\"搜索店铺名称或地址\"\r\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\"\r\n            />\r\n            <button\r\n              onClick={handleSearch}\r\n              className=\"px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors\"\r\n            >\r\n              搜索\r\n            </button>\r\n            {showCurrentLocation && userLocation && (\r\n              <button\r\n                onClick={findNearestStore}\r\n                className=\"px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors\"\r\n              >\r\n                最近店铺\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      <div \r\n        ref={mapContainerRef} \r\n        style={{ height, width: '100%' }}\r\n        className=\"rounded-lg overflow-hidden\"\r\n      />\r\n      \r\n      {!isMapLoaded && (\r\n        <div className=\"absolute inset-0 flex items-center justify-center bg-gray-100\">\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4\"></div>\r\n            <p className=\"text-gray-600\">地图加载中...</p>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      <div className=\"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\r\n        <p className=\"text-sm text-yellow-800\">\r\n          <strong>提示：</strong>请在实际部署时替换百度地图 API Key。\r\n          当前使用的是演示地图，某些功能可能无法正常工作。\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function StoreMap(props: StoreMapProps) {\r\n  return (\r\n    <ClientOnly fallback={\r\n      <div className=\"relative\">\r\n        <div\r\n          style={{ height: props.height || '400px', width: '100%' }}\r\n          className=\"rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center\"\r\n        >\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4\"></div>\r\n            <p className=\"text-gray-600\">地图加载中...</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    }>\r\n      <StoreMapContent {...props} />\r\n    </ClientOnly>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAuBA,SAAS,gBAAgB,EACvB,MAAM,EACN,aAAa,EACb,aAAa,EACb,SAAS,OAAO,EAChB,eAAe,KAAK,EACpB,sBAAsB,IAAI,EACZ;IACd,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC/C,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC3B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuC;IACtF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,WAAW;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd;;aAiBO,IAAI,OAAO,IAAI,EAAE;YACtB,eAAe;QACjB;IACF,GAAG;QAAC;KAAQ;IAEZ,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,gBAAgB,OAAO,IAAI,CAAC,OAAO,IAAI,EAAE;QAE1E,MAAM,MAAM,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,gBAAgB,OAAO;QACvD,MAAM,gBAAgB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,SAAS,QAAQ;QACtE,IAAI,aAAa,CAAC,eAAe;QACjC,IAAI,qBAAqB,CAAC;QAE1B,SAAS;QACT,IAAI,UAAU,CAAC,IAAI,OAAO,IAAI,CAAC,iBAAiB;QAChD,IAAI,UAAU,CAAC,IAAI,OAAO,IAAI,CAAC,YAAY;QAE3C,OAAO,OAAO,GAAG;QAEjB,WAAW;QACX,IAAI,uBAAuB,UAAU,WAAW,EAAE;YAChD,UAAU,WAAW,CAAC,kBAAkB,CACtC,CAAC;gBACC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,SAAS,MAAM;gBAC/C,gBAAgB;oBAAE,KAAK;oBAAU,KAAK;gBAAU;gBAEhD,MAAM,YAAY,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW;gBACnD,IAAI,aAAa,CAAC,WAAW;gBAE7B,WAAW;gBACX,MAAM,aAAa,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW;oBACnD,MAAM,IAAI,OAAO,IAAI,CAAC,IAAI,CACxB,6BACA,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;gBAE7B;gBACA,IAAI,UAAU,CAAC;YACjB,GACA,CAAC;gBACC,QAAQ,KAAK,CAAC,WAAW;YAC3B;QAEJ;IACF,GAAG;QAAC;QAAS;QAAa;KAAoB;IAE9C,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,OAAO,IAAI,EAAE;QAErC,QAAQ;QACR,WAAW,OAAO,CAAC,OAAO,CAAC,CAAA;YACzB,OAAO,OAAO,CAAC,aAAa,CAAC;QAC/B;QACA,WAAW,OAAO,GAAG,EAAE;QAEvB,QAAQ;QACR,OAAO,OAAO,CAAC,CAAC,OAAO;YACrB,MAAM,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,SAAS,EAAE,MAAM,QAAQ;YACnE,MAAM,SAAS,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;YAEtC,UAAU;YACV,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,IAAI,CAC/B,MAAM,MAAM,GAAG,2BAA2B,4BAC1C,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;YAE3B,OAAO,OAAO,CAAC;YAEf,WAAW;YACX,OAAO,gBAAgB,CAAC,SAAS;gBAC/B,IAAI,eAAe;oBACjB,cAAc;gBAChB;gBAEA,SAAS;gBACT,MAAM,aAAa,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC;;4CAEX,EAAE,MAAM,IAAI,CAAC;yCAChB,EAAE,MAAM,OAAO,CAAC;yCAChB,EAAE,MAAM,KAAK,CAAC;2CACZ,EAAE,MAAM,QAAQ,CAAC,GAAG,EAAE,MAAM,SAAS,CAAC;yCACxC,EAAE,MAAM,MAAM,GAAG,QAAQ,MAAM;;QAEhE,CAAC;gBACD,OAAO,OAAO,CAAC,cAAc,CAAC,YAAY;YAC5C;YAEA,OAAO,OAAO,CAAC,UAAU,CAAC;YAC1B,WAAW,OAAO,CAAC,IAAI,CAAC;YAExB,mBAAmB;YACnB,IAAI,UAAU,KAAK,CAAC,cAAc;gBAChC,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO;YACtC;QACF;QAEA,gBAAgB;QAChB,IAAI,eAAe;YACjB,MAAM,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,SAAS,EAAE,cAAc,QAAQ;YACnF,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO;QACtC;IACF,GAAG;QAAC;QAAQ;QAAe;QAAe;KAAa;IAEvD,OAAO;IACP,MAAM,eAAe;QACnB,IAAI,CAAC,cAAc,IAAI,IAAI;QAE3B,MAAM,aAAa,OAAO,IAAI,CAAC,CAAA,QAC7B,MAAM,IAAI,CAAC,QAAQ,CAAC,kBACpB,MAAM,OAAO,CAAC,QAAQ,CAAC;QAGzB,IAAI,cAAc,eAAe;YAC/B,cAAc;QAChB;IACF;IAEA,UAAU;IACV,MAAM,mBAAmB;QACvB,IAAI,CAAC,gBAAgB,OAAO,MAAM,KAAK,GAAG;QAE1C,IAAI,eAAe,MAAM,CAAC,EAAE;QAC5B,IAAI,cAAc,OAAO,SAAS;QAElC,OAAO,OAAO,CAAC,CAAA;YACb,MAAM,WAAW,KAAK,IAAI,CACxB,KAAK,GAAG,CAAC,MAAM,QAAQ,GAAG,aAAa,GAAG,EAAE,KAC5C,KAAK,GAAG,CAAC,MAAM,SAAS,GAAG,aAAa,GAAG,EAAE;YAG/C,IAAI,WAAW,aAAa;gBAC1B,cAAc;gBACd,eAAe;YACjB;QACF;QAEA,IAAI,eAAe;YACjB,cAAc;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4BAChD,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;4BACxC,aAAY;4BACZ,WAAU;;;;;;sCAEZ,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;wBAGA,uBAAuB,8BACtB,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAQT,8OAAC;gBACC,KAAK;gBACL,OAAO;oBAAE;oBAAQ,OAAO;gBAAO;gBAC/B,WAAU;;;;;;YAGX,CAAC,6BACA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAKnC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC;sCAAO;;;;;;wBAAY;;;;;;;;;;;;;;;;;;AAM9B;AAEe,SAAS,SAAS,KAAoB;IACnD,qBACE,8OAAC,gIAAA,CAAA,UAAU;QAAC,wBACV,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBACC,OAAO;oBAAE,QAAQ,MAAM,MAAM,IAAI;oBAAS,OAAO;gBAAO;gBACxD,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;kBAKnC,cAAA,8OAAC;YAAiB,GAAG,KAAK;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/components/ImageUpload.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, DragEvent } from 'react';\r\nimport Image from 'next/image';\r\n\r\ninterface ImageUploadProps {\r\n  value?: string;\r\n  onChange: (url: string) => void;\r\n  placeholder?: string;\r\n  className?: string;\r\n}\r\n\r\nexport default function ImageUpload({\r\n  value,\r\n  onChange,\r\n  placeholder = '点击或拖拽图片到此处上传',\r\n  className = ''\r\n}: ImageUploadProps) {\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [preview, setPreview] = useState<string>(value || '');\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(true);\r\n  };\r\n\r\n  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n  };\r\n\r\n  const handleDrop = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n\r\n    const files = e.dataTransfer.files;\r\n    if (files && files[0]) {\r\n      handleFile(files[0]);\r\n    }\r\n  };\r\n\r\n  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = e.target.files;\r\n    if (files && files[0]) {\r\n      handleFile(files[0]);\r\n    }\r\n  };\r\n\r\n  const handleFile = async (file: File) => {\r\n    // 验证文件类型\r\n    if (!file.type.startsWith('image/')) {\r\n      alert('请上传图片文件');\r\n      return;\r\n    }\r\n\r\n    // 验证文件大小（最大 5MB）\r\n    if (file.size > 5 * 1024 * 1024) {\r\n      alert('图片大小不能超过 5MB');\r\n      return;\r\n    }\r\n\r\n    setIsUploading(true);\r\n\r\n    try {\r\n      // 创建本地预览\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        const result = e.target?.result as string;\r\n        setPreview(result);\r\n      };\r\n      reader.readAsDataURL(file);\r\n\r\n      // 模拟上传到图床\r\n      // 实际项目中，这里应该调用真实的图床 API\r\n      const formData = new FormData();\r\n      formData.append('image', file);\r\n\r\n      // 模拟上传延迟\r\n      await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n      // 模拟返回的图片 URL\r\n      // 实际项目中，这里应该是从图床 API 返回的 URL\r\n      const mockImageUrl = URL.createObjectURL(file);\r\n      \r\n      onChange(mockImageUrl);\r\n      alert('图片上传成功！（注：这是模拟上传，实际项目需要配置图床服务）');\r\n    } catch (error) {\r\n      console.error('Upload failed:', error);\r\n      alert('图片上传失败，请重试');\r\n    } finally {\r\n      setIsUploading(false);\r\n    }\r\n  };\r\n\r\n  const handleRemove = () => {\r\n    setPreview('');\r\n    onChange('');\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <div\r\n        className={`relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all ${\r\n          isDragging\r\n            ? 'border-orange-500 bg-orange-50'\r\n            : 'border-gray-300 hover:border-gray-400'\r\n        }`}\r\n        onDragOver={handleDragOver}\r\n        onDragLeave={handleDragLeave}\r\n        onDrop={handleDrop}\r\n        onClick={() => fileInputRef.current?.click()}\r\n      >\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={handleFileSelect}\r\n          className=\"hidden\"\r\n        />\r\n\r\n        {isUploading ? (\r\n          <div className=\"py-8\">\r\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4\"></div>\r\n            <p className=\"text-gray-600\">上传中...</p>\r\n          </div>\r\n        ) : preview ? (\r\n          <div className=\"relative\">\r\n            <div className=\"relative w-full h-48 mb-4\">\r\n              <Image\r\n                src={preview}\r\n                alt=\"Preview\"\r\n                fill\r\n                className=\"object-contain rounded\"\r\n              />\r\n            </div>\r\n            <button\r\n              type=\"button\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                handleRemove();\r\n              }}\r\n              className=\"absolute top-2 right-2 bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors\"\r\n            >\r\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </button>\r\n            <p className=\"text-sm text-gray-600\">点击重新上传</p>\r\n          </div>\r\n        ) : (\r\n          <div className=\"py-8\">\r\n            <svg\r\n              className=\"mx-auto h-12 w-12 text-gray-400\"\r\n              stroke=\"currentColor\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 48 48\"\r\n            >\r\n              <path\r\n                d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\"\r\n                strokeWidth={2}\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n              />\r\n            </svg>\r\n            <p className=\"mt-2 text-sm text-gray-600\">{placeholder}</p>\r\n            <p className=\"mt-1 text-xs text-gray-500\">支持 JPG、PNG、GIF 格式，最大 5MB</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"mt-2 text-xs text-gray-500\">\r\n        <p>提示：实际项目中需要配置图床服务（如七牛云、阿里云OSS等）</p>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYe,SAAS,YAAY,EAClC,KAAK,EACL,QAAQ,EACR,cAAc,cAAc,EAC5B,YAAY,EAAE,EACG;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,SAAS;IACxD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QAEd,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;YACrB,WAAW,KAAK,CAAC,EAAE;QACrB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;YACrB,WAAW,KAAK,CAAC,EAAE;QACrB;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,SAAS;QACT,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,MAAM;YACN;QACF;QAEA,iBAAiB;QACjB,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,MAAM;YACN;QACF;QAEA,eAAe;QAEf,IAAI;YACF,SAAS;YACT,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,SAAS,EAAE,MAAM,EAAE;gBACzB,WAAW;YACb;YACA,OAAO,aAAa,CAAC;YAErB,UAAU;YACV,wBAAwB;YACxB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS;YAEzB,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,cAAc;YACd,6BAA6B;YAC7B,MAAM,eAAe,IAAI,eAAe,CAAC;YAEzC,SAAS;YACT,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,WAAW;QACX,SAAS;QACT,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBACC,WAAW,CAAC,yFAAyF,EACnG,aACI,mCACA,yCACJ;gBACF,YAAY;gBACZ,aAAa;gBACb,QAAQ;gBACR,SAAS,IAAM,aAAa,OAAO,EAAE;;kCAErC,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAO;wBACP,UAAU;wBACV,WAAU;;;;;;oBAGX,4BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;+BAE7B,wBACF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK;oCACL,KAAI;oCACJ,IAAI;oCACJ,WAAU;;;;;;;;;;;0CAGd,8OAAC;gCACC,MAAK;gCACL,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;6CAGvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,QAAO;gCACP,MAAK;gCACL,SAAQ;0CAER,cAAA,8OAAC;oCACC,GAAE;oCACF,aAAa;oCACb,eAAc;oCACd,gBAAe;;;;;;;;;;;0CAGnB,8OAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAC3C,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;0BAKhD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;8BAAE;;;;;;;;;;;;;;;;;AAIX", "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/src/app/admin/stores/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport { Store } from '@/types';\r\nimport StoreMap from '@/components/StoreMap';\r\nimport ImageUpload from '@/components/ImageUpload';\r\nimport { useNotification } from '@/components/Notification';\r\nimport ClientOnly from '@/components/ClientOnly';\r\n\r\nexport default function AdminStores() {\r\n  const [stores, setStores] = useState<Store[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [showForm, setShowForm] = useState(false);\r\n  const [editingStore, setEditingStore] = useState<Store | null>(null);\r\n  const [selectedStore, setSelectedStore] = useState<Store | null>(null);\r\n  const [showMap, setShowMap] = useState(false);\r\n  const [mounted, setMounted] = useState(false);\r\n  const router = useRouter();\r\n  const { showNotification, showConfirm } = useNotification();\r\n\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    address: '',\r\n    phone: '',\r\n    latitude: 0,\r\n    longitude: 0,\r\n    openTime: '08:00',\r\n    closeTime: '22:00',\r\n    isOpen: true,\r\n    image: ''\r\n  });\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (!mounted) return;\r\n    \r\n    const auth = localStorage.getItem('adminAuth');\r\n    if (!auth) {\r\n      router.push('/admin/login');\r\n      return;\r\n    }\r\n    fetchStores();\r\n  }, [router, mounted]);\r\n\r\n  const fetchStores = async () => {\r\n    try {\r\n      const response = await fetch('/api/stores');\r\n      const data = await response.json();\r\n      setStores(data);\r\n    } catch (error) {\r\n      console.error('Failed to fetch stores:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    try {\r\n      const url = editingStore ? `/api/stores/${editingStore.id}` : '/api/stores';\r\n      const method = editingStore ? 'PUT' : 'POST';\r\n      \r\n      const response = await fetch(url, {\r\n        method,\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(formData),\r\n      });\r\n\r\n      if (response.ok) {\r\n        await fetchStores();\r\n        setShowForm(false);\r\n        setEditingStore(null);\r\n        setFormData({\r\n          name: '',\r\n          address: '',\r\n          phone: '',\r\n          latitude: 0,\r\n          longitude: 0,\r\n          openTime: '08:00',\r\n          closeTime: '22:00',\r\n          isOpen: true,\r\n          image: ''\r\n        });\r\n        showNotification(editingStore ? '店铺更新成功' : '店铺添加成功', 'success');\r\n      } else {\r\n        const error = await response.json();\r\n        showNotification(error.error || '操作失败', 'error');\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to save store:', error);\r\n      showNotification('操作失败', 'error');\r\n    }\r\n  };\r\n\r\n  const handleEdit = (store: Store) => {\r\n    setEditingStore(store);\r\n    setFormData({\r\n      name: store.name,\r\n      address: store.address,\r\n      phone: store.phone,\r\n      latitude: store.latitude,\r\n      longitude: store.longitude,\r\n      openTime: store.openTime,\r\n      closeTime: store.closeTime,\r\n      isOpen: store.isOpen,\r\n      image: store.image || ''\r\n    });\r\n    setShowForm(true);\r\n  };\r\n\r\n  const handleDelete = async (id: string) => {\r\n    showConfirm(\r\n      '确定要删除这个店铺吗？',\r\n      async () => {\r\n        try {\r\n          const response = await fetch(`/api/stores/${id}`, {\r\n            method: 'DELETE',\r\n          });\r\n          if (response.ok) {\r\n            await fetchStores();\r\n            showNotification('店铺删除成功', 'success');\r\n          } else {\r\n            showNotification('删除失败', 'error');\r\n          }\r\n        } catch (error) {\r\n          console.error('Failed to delete store:', error);\r\n          showNotification('删除失败', 'error');\r\n        }\r\n      }\r\n    );\r\n  };\r\n\r\n  const handleMapView = (store: Store) => {\r\n    setSelectedStore(store);\r\n    setShowMap(true);\r\n  };\r\n\r\n  if (!mounted) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto\"></div>\r\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto\"></div>\r\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <ClientOnly fallback={\r\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto\"></div>\r\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\r\n        </div>\r\n      </div>\r\n    }>\r\n      <div className=\"min-h-screen bg-gray-50\">\r\n      <header className=\"bg-white shadow\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex justify-between items-center py-6\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <Link href=\"/admin\" className=\"text-orange-600 hover:text-orange-700\">\r\n                ← 返回管理后台\r\n              </Link>\r\n              <h1 className=\"text-3xl font-bold text-gray-900\">店铺管理</h1>\r\n            </div>\r\n            <div className=\"flex gap-2\">\r\n              <button\r\n                onClick={() => {\r\n                  setShowMap(!showMap);\r\n                  setSelectedStore(null);\r\n                }}\r\n                className=\"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors\"\r\n              >\r\n                {showMap ? '列表视图' : '地图视图'}\r\n              </button>\r\n              <button\r\n                onClick={() => setShowForm(true)}\r\n                className=\"bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors\"\r\n              >\r\n                添加店铺\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\r\n        <div className=\"px-4 py-6 sm:px-0\">\r\n          {showMap ? (\r\n            <div className=\"bg-white shadow rounded-lg p-6\">\r\n              <h2 className=\"text-lg font-medium text-gray-900 mb-4\">店铺地图</h2>\r\n              <StoreMap\r\n                stores={stores}\r\n                selectedStore={selectedStore}\r\n                onStoreSelect={setSelectedStore}\r\n                height=\"500px\"\r\n                enableSearch={true}\r\n                showCurrentLocation={true}\r\n              />\r\n              {selectedStore && (\r\n                <div className=\"mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg\">\r\n                  <h3 className=\"font-medium text-gray-900 mb-2\">选中店铺信息</h3>\r\n                  <p className=\"text-sm text-gray-600\">名称：{selectedStore.name}</p>\r\n                  <p className=\"text-sm text-gray-600\">地址：{selectedStore.address}</p>\r\n                  <p className=\"text-sm text-gray-600\">电话：{selectedStore.phone}</p>\r\n                  <p className=\"text-sm text-gray-600\">\r\n                    营业时间：{selectedStore.openTime} - {selectedStore.closeTime}\r\n                  </p>\r\n                  <div className=\"mt-3 flex gap-2\">\r\n                    <button\r\n                      onClick={() => handleEdit(selectedStore)}\r\n                      className=\"text-sm text-orange-600 hover:text-orange-900\"\r\n                    >\r\n                      编辑\r\n                    </button>\r\n                    <button\r\n                      onClick={() => handleDelete(selectedStore.id)}\r\n                      className=\"text-sm text-red-600 hover:text-red-900\"\r\n                    >\r\n                      删除\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            <div className=\"bg-white shadow rounded-lg\">\r\n              <div className=\"px-6 py-4 border-b border-gray-200\">\r\n                <h2 className=\"text-lg font-medium text-gray-900\">店铺列表</h2>\r\n              </div>\r\n              <div className=\"overflow-x-auto\">\r\n                <table className=\"min-w-full divide-y divide-gray-200\">\r\n                <thead className=\"bg-gray-50\">\r\n                  <tr>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      店铺名称\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      地址\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      电话\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      营业时间\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      状态\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      操作\r\n                    </th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                  {stores.map((store) => (\r\n                    <tr key={store.id}>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\r\n                        {store.name}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                        {store.address}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                        {store.phone}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                        {store.openTime} - {store.closeTime}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\r\n                          store.isOpen \r\n                            ? 'bg-green-100 text-green-800' \r\n                            : 'bg-red-100 text-red-800'\r\n                        }`}>\r\n                          {store.isOpen ? '营业中' : '已打烊'}\r\n                        </span>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\r\n                        <button\r\n                          onClick={() => handleMapView(store)}\r\n                          className=\"text-blue-600 hover:text-blue-900\"\r\n                        >\r\n                          地图\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleEdit(store)}\r\n                          className=\"text-orange-600 hover:text-orange-900\"\r\n                        >\r\n                          编辑\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDelete(store.id)}\r\n                          className=\"text-red-600 hover:text-red-900\"\r\n                        >\r\n                          删除\r\n                        </button>\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </main>\r\n\r\n      {showForm && (\r\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\r\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\r\n            <div className=\"mt-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\r\n                {editingStore ? '编辑店铺' : '添加店铺'}\r\n              </h3>\r\n              <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700\">店铺名称</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={formData.name}\r\n                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}\r\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700\">地址</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={formData.address}\r\n                    onChange={(e) => setFormData({ ...formData, address: e.target.value })}\r\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700\">电话</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={formData.phone}\r\n                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}\r\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div className=\"grid grid-cols-2 gap-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700\">纬度</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      step=\"any\"\r\n                      value={formData.latitude}\r\n                      onChange={(e) => setFormData({ ...formData, latitude: parseFloat(e.target.value) })}\r\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700\">经度</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      step=\"any\"\r\n                      value={formData.longitude}\r\n                      onChange={(e) => setFormData({ ...formData, longitude: parseFloat(e.target.value) })}\r\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">店铺图片</label>\r\n                  <ImageUpload\r\n                    value={formData.image}\r\n                    onChange={(url) => setFormData({ ...formData, image: url })}\r\n                    placeholder=\"上传店铺门面照片\"\r\n                  />\r\n                </div>\r\n                <div className=\"mt-4 p-3 bg-gray-50 rounded-md\">\r\n                  <p className=\"text-sm text-gray-600\">\r\n                    提示：您可以在地图上点击获取经纬度，或访问\r\n                    <a href=\"https://api.map.baidu.com/lbsapi/getpoint/index.html\"\r\n                       target=\"_blank\"\r\n                       rel=\"noopener noreferrer\"\r\n                       className=\"text-blue-600 hover:underline ml-1\">\r\n                      百度地图坐标拾取器\r\n                    </a>\r\n                  </p>\r\n                </div>\r\n                <div className=\"grid grid-cols-2 gap-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700\">开门时间</label>\r\n                    <input\r\n                      type=\"time\"\r\n                      value={formData.openTime}\r\n                      onChange={(e) => setFormData({ ...formData, openTime: e.target.value })}\r\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700\">关门时间</label>\r\n                    <input\r\n                      type=\"time\"\r\n                      value={formData.closeTime}\r\n                      onChange={(e) => setFormData({ ...formData, closeTime: e.target.value })}\r\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                </div>\r\n                <div>\r\n                  <label className=\"flex items-center\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={formData.isOpen}\r\n                      onChange={(e) => setFormData({ ...formData, isOpen: e.target.checked })}\r\n                      className=\"rounded border-gray-300 text-orange-600 focus:ring-orange-500\"\r\n                    />\r\n                    <span className=\"ml-2 text-sm text-gray-700\">当前营业中</span>\r\n                  </label>\r\n                </div>\r\n                <div className=\"flex justify-end space-x-3 pt-4\">\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => {\r\n                      setShowForm(false);\r\n                      setEditingStore(null);\r\n                    }}\r\n                    className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300\"\r\n                  >\r\n                    取消\r\n                  </button>\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"px-4 py-2 text-sm font-medium text-white bg-orange-500 rounded-md hover:bg-orange-600\"\r\n                  >\r\n                    {editingStore ? '更新' : '添加'}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n      </div>\r\n    </ClientOnly>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,gBAAgB,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAExD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,SAAS;QACT,OAAO;QACP,UAAU;QACV,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;QACR,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,OAAO,aAAa,OAAO,CAAC;QAClC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;YACZ;QACF;QACA;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,MAAM,eAAe,CAAC,YAAY,EAAE,aAAa,EAAE,EAAE,GAAG;YAC9D,MAAM,SAAS,eAAe,QAAQ;YAEtC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,YAAY;gBACZ,gBAAgB;gBAChB,YAAY;oBACV,MAAM;oBACN,SAAS;oBACT,OAAO;oBACP,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,WAAW;oBACX,QAAQ;oBACR,OAAO;gBACT;gBACA,iBAAiB,eAAe,WAAW,UAAU;YACvD,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,iBAAiB,MAAM,KAAK,IAAI,QAAQ;YAC1C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,iBAAiB,QAAQ;QAC3B;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,gBAAgB;QAChB,YAAY;YACV,MAAM,MAAM,IAAI;YAChB,SAAS,MAAM,OAAO;YACtB,OAAO,MAAM,KAAK;YAClB,UAAU,MAAM,QAAQ;YACxB,WAAW,MAAM,SAAS;YAC1B,UAAU,MAAM,QAAQ;YACxB,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,MAAM;YACpB,OAAO,MAAM,KAAK,IAAI;QACxB;QACA,YAAY;IACd;IAEA,MAAM,eAAe,OAAO;QAC1B,YACE,eACA;YACE,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,IAAI,EAAE;oBAChD,QAAQ;gBACV;gBACA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM;oBACN,iBAAiB,UAAU;gBAC7B,OAAO;oBACL,iBAAiB,QAAQ;gBAC3B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,iBAAiB,QAAQ;YAC3B;QACF;IAEJ;IAEA,MAAM,gBAAgB,CAAC;QACrB,iBAAiB;QACjB,WAAW;IACb;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC,gIAAA,CAAA,UAAU;QAAC,wBACV,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;kBAItC,cAAA,8OAAC;YAAI,WAAU;;8BACf,8OAAC;oBAAO,WAAU;8BAChB,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAwC;;;;;;sDAGtE,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;;;;;;;8CAEnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;gDACP,WAAW,CAAC;gDACZ,iBAAiB;4CACnB;4CACA,WAAU;sDAET,UAAU,SAAS;;;;;;sDAEtB,8OAAC;4CACC,SAAS,IAAM,YAAY;4CAC3B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQT,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;kCACZ,wBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC,8HAAA,CAAA,UAAQ;oCACP,QAAQ;oCACR,eAAe;oCACf,eAAe;oCACf,QAAO;oCACP,cAAc;oCACd,qBAAqB;;;;;;gCAEtB,+BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;;gDAAwB;gDAAI,cAAc,IAAI;;;;;;;sDAC3D,8OAAC;4CAAE,WAAU;;gDAAwB;gDAAI,cAAc,OAAO;;;;;;;sDAC9D,8OAAC;4CAAE,WAAU;;gDAAwB;gDAAI,cAAc,KAAK;;;;;;;sDAC5D,8OAAC;4CAAE,WAAU;;gDAAwB;gDAC7B,cAAc,QAAQ;gDAAC;gDAAI,cAAc,SAAS;;;;;;;sDAE1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,WAAW;oDAC1B,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS,IAAM,aAAa,cAAc,EAAE;oDAC5C,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;iDAQT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;;;;;;8CAEpD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAM,WAAU;;0DACjB,8OAAC;gDAAM,WAAU;0DACf,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;;;;;;;;;;;;0DAKnG,8OAAC;gDAAM,WAAU;0DACd,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACX,MAAM,IAAI;;;;;;0EAEb,8OAAC;gEAAG,WAAU;0EACX,MAAM,OAAO;;;;;;0EAEhB,8OAAC;gEAAG,WAAU;0EACX,MAAM,KAAK;;;;;;0EAEd,8OAAC;gEAAG,WAAU;;oEACX,MAAM,QAAQ;oEAAC;oEAAI,MAAM,SAAS;;;;;;;0EAErC,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAK,WAAW,CAAC,yDAAyD,EACzE,MAAM,MAAM,GACR,gCACA,2BACJ;8EACC,MAAM,MAAM,GAAG,QAAQ;;;;;;;;;;;0EAG5B,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEACC,SAAS,IAAM,cAAc;wEAC7B,WAAU;kFACX;;;;;;kFAGD,8OAAC;wEACC,SAAS,IAAM,WAAW;wEAC1B,WAAU;kFACX;;;;;;kFAGD,8OAAC;wEACC,SAAS,IAAM,aAAa,MAAM,EAAE;wEACpC,WAAU;kFACX;;;;;;;;;;;;;uDAtCI,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAoD9B,0BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,eAAe,SAAS;;;;;;8CAE3B,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA0C;;;;;;8DAC3D,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACjE,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAGZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA0C;;;;;;8DAC3D,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACpE,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAGZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA0C;;;;;;8DAC3D,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAClE,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAGZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA0C;;;;;;sEAC3D,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,QAAQ;4DACxB,UAAU,CAAC,IAAM,YAAY;oEAAE,GAAG,QAAQ;oEAAE,UAAU,WAAW,EAAE,MAAM,CAAC,KAAK;gEAAE;4DACjF,WAAU;4DACV,QAAQ;;;;;;;;;;;;8DAGZ,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA0C;;;;;;sEAC3D,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,SAAS;4DACzB,UAAU,CAAC,IAAM,YAAY;oEAAE,GAAG,QAAQ;oEAAE,WAAW,WAAW,EAAE,MAAM,CAAC,KAAK;gEAAE;4DAClF,WAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC,iIAAA,CAAA,UAAW;oDACV,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,MAAQ,YAAY;4DAAE,GAAG,QAAQ;4DAAE,OAAO;wDAAI;oDACzD,aAAY;;;;;;;;;;;;sDAGhB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;oDAAwB;kEAEnC,8OAAC;wDAAE,MAAK;wDACL,QAAO;wDACP,KAAI;wDACJ,WAAU;kEAAqC;;;;;;;;;;;;;;;;;sDAKtD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA0C;;;;;;sEAC3D,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,QAAQ;4DACxB,UAAU,CAAC,IAAM,YAAY;oEAAE,GAAG,QAAQ;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAC;4DACrE,WAAU;4DACV,QAAQ;;;;;;;;;;;;8DAGZ,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA0C;;;;;;sEAC3D,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,SAAS;4DACzB,UAAU,CAAC,IAAM,YAAY;oEAAE,GAAG,QAAQ;oEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gEAAC;4DACtE,WAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;;sDAId,8OAAC;sDACC,cAAA,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS,SAAS,MAAM;wDACxB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,QAAQ,EAAE,MAAM,CAAC,OAAO;4DAAC;wDACrE,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;;;;;;sDAGjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;wDACP,YAAY;wDACZ,gBAAgB;oDAClB;oDACA,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,MAAK;oDACL,WAAU;8DAET,eAAe,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3C", "debugId": null}}]}