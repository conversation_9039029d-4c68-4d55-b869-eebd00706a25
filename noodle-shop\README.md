# 香香面条店 - 在线面食订购平台

一个现代化的面条面片在线订购网站，使用 Next.js 14 构建，可以一键部署到 Cloudflare Pages。

## 功能特性

### 🍜 前端功能
- **响应式设计** - 支持移动端和桌面端
- **产品展示** - 精美的菜品展示页面
- **分类浏览** - 按面条、面片、汤面分类
- **详情页面** - 完整的菜品详情信息
- **店铺定位** - 实时显示门店位置
- **天气信息** - 显示用户IP和当地天气

### 🛠 后台管理
- **菜品管理** - 增删改查菜品信息
- **门店管理** - 管理门店信息和位置
- **实时更新** - 前端实时展示后台修改
- **图片管理** - 自动生成占位图片

### 📍 位置服务
- **IP定位** - 自动获取用户位置
- **天气API** - 免费天气信息显示
- **地图集成** - 店铺位置可视化

## 技术栈

- **前端框架**: Next.js 14 (App Router)
- **样式系统**: Tailwind CSS
- **类型检查**: TypeScript
- **数据存储**: 本地JSON文件 (可扩展至MySQL)
- **部署平台**: Cloudflare Pages
- **API服务**: Next.js API Routes

## 快速开始

### 本地开发

1. 进入项目目录
```bash
cd noodle-shop
```

2. 安装依赖
```bash
npm install
```

3. 启动开发服务器
```bash
npm run dev
```

4. 打开浏览器访问 [http://localhost:3000](http://localhost:3000)

### 部署到 Cloudflare Pages

1. 在 Cloudflare Pages 中创建新项目
2. 连接你的 Git 仓库
3. 设置构建配置：
   - **构建命令**: `npm run build`
   - **构建输出目录**: `out`
   - **Node.js 版本**: 18.x 或更高

4. 部署完成后即可访问

## 项目结构

```
noodle-shop/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── admin/           # 后台管理页面
│   │   ├── api/             # API路由
│   │   ├── products/        # 产品页面
│   │   └── globals.css      # 全局样式
│   ├── components/          # React组件
│   ├── data/               # JSON数据文件
│   ├── lib/                # 工具函数
│   └── types/              # TypeScript类型定义
├── public/                 # 静态资源
└── README.md
```

## 访问路径

- **首页**: `/`
- **产品列表**: `/products`
- **产品详情**: `/products/[id]`
- **管理后台**: `/admin`
- **产品管理**: `/admin/products`
- **门店管理**: `/admin/stores`
