{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;+BAAYA,sBAAAA;;;eAAAA;;;AAAL,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN", "RedirectStatusCode"], "mappings": ";;;;;;;;;;;;;;;IAEaA,mBAAmB,EAAA;eAAnBA;;IAEDC,YAAY,EAAA;eAAZA;;IAgBIC,eAAe,EAAA;eAAfA;;;oCApBmB;AAE5B,MAAMF,sBAAsB;AAE5B,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;;AAgBL,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,cAAcG,oBAAAA,kBAAkB;AAEpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/client/components/redirect.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n"], "names": ["getRedirectError", "getRedirectStatusCodeFromError", "getRedirectTypeFromError", "getURLFromRedirectError", "permanentRedirect", "redirect", "actionAsyncStorage", "window", "require", "undefined", "url", "type", "statusCode", "RedirectStatusCode", "TemporaryRedirect", "error", "Error", "REDIRECT_ERROR_CODE", "digest", "getStore", "isAction", "RedirectType", "push", "replace", "PermanentRedirect", "isRedirectError", "split", "slice", "join", "Number", "at"], "mappings": ";;;;;;;;;;;;;;;;;;IAegBA,gBAAgB,EAAA;eAAhBA;;IA6EAC,8BAA8B,EAAA;eAA9BA;;IARAC,wBAAwB,EAAA;eAAxBA;;IARAC,uBAAuB,EAAA;eAAvBA;;IAhBAC,iBAAiB,EAAA;eAAjBA;;IAvBAC,QAAQ,EAAA;eAARA;;;oCArCmB;+BAM5B;AAEP,MAAMC,qBACJ,OAAOC,WAAW,qBAEZC,QAAQ,2KACRF,kBAAkB,GACpBG;AAEC,SAAST,iBACdU,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,eAAAA,KAAAA,GAAAA,aAAiCC,oBAAAA,kBAAkB,CAACC,iBAAiB;IAErE,MAAMC,QAAQ,OAAA,cAA8B,CAA9B,IAAIC,MAAMC,eAAAA,mBAAmB,GAA7B,qBAAA;eAAA;oBAAA;sBAAA;IAA6B;IAC3CF,MAAMG,MAAM,GAAMD,eAAAA,mBAAmB,GAAC,MAAGN,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,OAAOG;AACT;AAcO,SAASV,SACd,2BAA2B,GAC3BK,GAAW,EACXC,IAAmB;QAEVL;IAATK,QAAAA,OAAAA,OAAAA,OAASL,CAAAA,sBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,+BAAAA,mBAAoBa,QAAQ,EAAA,KAAA,OAAA,KAAA,IAA5Bb,6BAAgCc,QAAQ,IAC7CC,eAAAA,YAAY,CAACC,IAAI,GACjBD,eAAAA,YAAY,CAACE,OAAO;IAExB,MAAMvB,iBAAiBU,KAAKC,MAAME,oBAAAA,kBAAkB,CAACC,iBAAiB;AACxE;AAaO,SAASV,kBACd,2BAA2B,GAC3BM,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,SAAAA,KAAAA,GAAAA,OAAqBU,eAAAA,YAAY,CAACE,OAAO;IAEzC,MAAMvB,iBAAiBU,KAAKC,MAAME,oBAAAA,kBAAkB,CAACW,iBAAiB;AACxE;AAUO,SAASrB,wBAAwBY,KAAc;IACpD,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAKC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;AACnD;AAEO,SAAS1B,yBAAyBa,KAAoB;IAC3D,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOD,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAASzB,+BAA+Bc,KAAoB;IACjE,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOa,OAAOd,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAKI,EAAE,CAAC,CAAC;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "HTTP_ERROR_FALLBACK_ERROR_CODE", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "status"], "mappings": ";;;;;;;;;;;;;;;;;IAAaA,qBAAqB,EAAA;eAArBA;;IAQAC,8BAA8B,EAAA;eAA9BA;;IAuCGC,kCAAkC,EAAA;eAAlCA;;IAPAC,2BAA2B,EAAA;eAA3BA;;IAnBAC,yBAAyB,EAAA;eAAzBA;;;AArBT,MAAMJ,wBAAwB;IACnCK,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB;AAEA,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACX;AAErC,MAAMC,iCAAiC;AAavC,SAASG,0BACdQ,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWb,kCACXO,cAAcS,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASZ,4BACdS,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASb,mCACdiB,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/client/components/not-found.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n"], "names": ["notFound", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "error", "Error", "digest"], "mappings": ";;;+BAsBgBA,YAAAA;;;eAAAA;;;oCAnBT;AAEP;;;;;;;;;;;;;CAaC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,4CAA4C;IAC5C,MAAMG,QAAQ,OAAA,cAAiB,CAAjB,IAAIC,MAAMH,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BE,MAAkCE,MAAM,GAAGJ;IAE7C,MAAME;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/client/components/forbidden.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["forbidden", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;+BAqBgBA,aAAAA;;;eAAAA;;;oCAlBT;AAEP,6BAA6B;AAC7B;;;;;;;;;;;CAWC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,IAAI,CAACG,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/client/components/unauthorized.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["unauthorized", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;+BAsBgBA,gBAAAA;;;eAAAA;;;oCAnBT;AAEP,gCAAgC;AAChC;;;;;;;;;;;;CAYC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,IAAI,CAACG,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n"], "names": ["isHangingPromiseRejectionError", "makeHangingPromise", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject"], "mappings": ";;;;;;;;;;;;;;IAAgBA,8BAA8B,EAAA;eAA9BA;;IAgCAC,kBAAkB,EAAA;eAAlBA;;;AAhCT,SAASD,+BACdE,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YAA4BC,UAAkB,CAAE;QAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,qJAAqJ,CAAC,GAAA,IAAA,CAFnRA,UAAAA,GAAAA,YAAAA,IAAAA,CAFZL,MAAAA,GAASC;IAMzB;AACF;AAGA,MAAMK,yBAAyB,IAAIC;AAS5B,SAAST,mBACdU,MAAmB,EACnBH,UAAkB;IAElB,IAAIG,OAAOC,OAAO,EAAE;QAClB,OAAOC,QAAQC,MAAM,CAAC,IAAIT,6BAA6BG;IACzD,OAAO;QACL,MAAMO,iBAAiB,IAAIF,QAAW,CAACG,GAAGF;YACxC,MAAMG,iBAAiBH,OAAOI,IAAI,CAChC,MACA,IAAIb,6BAA6BG;YAEnC,IAAIW,mBAAmBV,uBAAuBW,GAAG,CAACT;YAClD,IAAIQ,kBAAkB;gBACpBA,iBAAiBE,IAAI,CAACJ;YACxB,OAAO;gBACL,MAAMK,YAAY;oBAACL;iBAAe;gBAClCR,uBAAuBc,GAAG,CAACZ,QAAQW;gBACnCX,OAAOa,gBAAgB,CACrB,SACA;oBACE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,UAAUI,MAAM,EAAED,IAAK;wBACzCH,SAAS,CAACG,EAAE;oBACd;gBACF,GACA;oBAAEE,MAAM;gBAAK;YAEjB;QACF;QACA,2GAA2G;QAC3G,6GAA6G;QAC7G,yFAAyF;QACzFZ,eAAea,KAAK,CAACC;QACrB,OAAOd;IACT;AACF;AAEA,SAASc,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/server/lib/router-utils/is-postpone.ts"], "sourcesContent": ["const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n"], "names": ["isPostpone", "REACT_POSTPONE_TYPE", "Symbol", "for", "error", "$$typeof"], "mappings": ";;;+BAEgBA,cAAAA;;;eAAAA;;;AAFhB,MAAMC,sBAA8BC,OAAOC,GAAG,CAAC;AAExC,SAASH,WAAWI,KAAU;IACnC,OACE,OAAOA,UAAU,YACjBA,UAAU,QACVA,MAAMC,QAAQ,KAAKJ;AAEvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "Error", "constructor", "reason", "digest", "err"], "mappings": "AAAA,+GAA+G;;;;;;;;;;;;;;;IAIlGA,iBAAiB,EAAA;eAAjBA;;IASGC,mBAAmB,EAAA;eAAnBA;;;AAZhB,MAAMC,iBAAiB;AAGhB,MAAMF,0BAA0BG;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAE,wCAAqCA,SAAAA,IAAAA,CADlBA,MAAAA,GAAAA,QAAAA,IAAAA,CAFZC,MAAAA,GAASJ;IAIzB;AACF;AAGO,SAASD,oBAAoBM,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isNextRouterError", "error", "isRedirectError", "isHTTPAccessFallbackError"], "mappings": ";;;+BAWgBA,qBAAAA;;;eAAAA;;;oCART;+BAC6C;AAO7C,SAASA,kBACdC,KAAc;IAEd,OAAOC,CAAAA,GAAAA,eAAAA,eAAe,EAACD,UAAUE,CAAAA,GAAAA,oBAAAA,yBAAyB,EAACF;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "Error", "constructor", "description", "digest", "err"], "mappings": ";;;;;;;;;;;;;;IAEaA,kBAAkB,EAAA;eAAlBA;;IAQGC,oBAAoB,EAAA;eAApBA;;;AAVhB,MAAMC,qBAAqB;AAEpB,MAAMF,2BAA2BG;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCJ;IAIpC;AACF;AAEO,SAASD,qBAAqBM,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAID,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOC,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "Error", "code", "error"], "mappings": ";;;;;;;;;;;;;;IAEaA,qBAAqB,EAAA;eAArBA;;IAIGC,uBAAuB,EAAA;eAAvBA;;;AANhB,MAAMC,0BAA0B;AAEzB,MAAMF,8BAA8BG;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOF;;AACzB;AAEO,SAASD,wBACdI,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMD,IAAI,KAAKF;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/lib/metadata/metadata-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME"], "mappings": ";;;;;;;;;;;;;;;IAAaA,sBAAsB,EAAA;eAAtBA;;IAEAC,oBAAoB,EAAA;eAApBA;;IADAC,sBAAsB,EAAA;eAAtBA;;;AADN,MAAMF,yBAAyB;AAC/B,MAAME,yBAAyB;AAC/B,MAAMD,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = (cb: ScheduledFn<void>) => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = (cb: ScheduledFn<void>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "setImmediate", "r"], "mappings": ";;;;;;;;;;;;;;;;IA4CgBA,cAAc,EAAA;eAAdA;;IAbHC,iBAAiB,EAAA;eAAjBA;;IAtBAC,kBAAkB,EAAA;eAAlBA;;IAgDGC,6BAA6B,EAAA;eAA7BA;;;AAhDT,MAAMD,qBAAqB,CAACE;IACjC,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;aAElC;YACLF,QAAQI,QAAQ,CAACR;QACnB;IACF;AACF;AAQO,MAAMH,oBAAoB,CAACG;IAChC,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;SAElC;QACLG,aAAaT;IACf;AACF;AAOO,SAASJ;IACd,OAAO,IAAIK,QAAc,CAACC,UAAYL,kBAAkBK;AAC1D;AAWO,SAASH;IACd,IAAIK,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;SAElC;QACL,OAAO,IAAIL,QAAQ,CAACS,IAAMD,aAAaC;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicErrorWithStack: null | Error\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspenseAboveBody: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasAllowedDynamic: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspenseAboveBody: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasAllowedDynamic: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    // TODO: it makes no sense to have these work unit store types during a dev render.\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-client' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  // It is important that we set this tracking value after aborting. Aborts are executed\n  // synchronously except for the case where you abort during render itself. By setting this\n  // value late we can use it to determine if any of the aborted tasks are the task that\n  // called the sync IO expression in the first place.\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n    // It is important that we set this tracking value after aborting. Aborts are executed\n    // synchronously except for the case where you abort during render itself. By setting this\n    // value late we can use it to determine if any of the aborted tasks are the task that\n    // called the sync IO expression in the first place.\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n      }\n    }\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender-client') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasSuspenseAfterBodyOrHtmlRegex =\n  /\\n\\s+at (?:body|html) \\(<anonymous>\\)[\\s\\S]*?\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  workStore: WorkStore,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseAfterBodyOrHtmlRegex.test(componentStack)) {\n    // This prerender has a Suspense boundary above the body which\n    // effectively opts the page into allowing 100% dynamic rendering\n    dynamicValidation.hasAllowedDynamic = true\n    dynamicValidation.hasSuspenseAboveBody = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    // this error had a Suspense boundary above it so we don't need to report it as a source\n    // of disallowed\n    dynamicValidation.hasAllowedDynamic = true\n    return\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    // This task was the task that called the sync error.\n    dynamicValidation.dynamicErrors.push(\n      clientDynamic.syncDynamicErrorWithStack\n    )\n    return\n  } else {\n    const message = `Route \"${workStore.route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentOrOwnerStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\n/**\n * In dev mode, we prefer using the owner stack, otherwise the provided\n * component stack is used.\n */\nfunction createErrorWithComponentOrOwnerStack(\n  message: string,\n  componentStack: string\n) {\n  const ownerStack =\n    process.env.NODE_ENV !== 'production' && React.captureOwnerStack\n      ? React.captureOwnerStack()\n      : null\n\n  const error = new Error(message)\n  error.stack = error.name + ': ' + message + (ownerStack ?? componentStack)\n  return error\n}\n\nexport enum PreludeState {\n  Full = 0,\n  Empty = 1,\n  Errored = 2,\n}\n\nfunction logDisallowedDynamicError(workStore: WorkStore, error: Error): void {\n  console.error(error)\n\n  if (!workStore.dev) {\n    if (workStore.hasReadableErrorStacks) {\n      console.error(\n        `To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.`\n      )\n    } else {\n      console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:\n  - Start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.\n  - Rerun the production build with \\`next build --debug-prerender\\` to generate better stack traces.`)\n    }\n  }\n}\n\nexport function throwIfDisallowedDynamic(\n  workStore: WorkStore,\n  prelude: PreludeState,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState\n): void {\n  if (workStore.invalidDynamicUsageError) {\n    logDisallowedDynamicError(workStore, workStore.invalidDynamicUsageError)\n    throw new StaticGenBailoutError()\n  }\n\n  if (prelude !== PreludeState.Full) {\n    if (dynamicValidation.hasSuspenseAboveBody) {\n      // This route has opted into allowing fully dynamic rendering\n      // by including a Suspense boundary above the body. In this case\n      // a lack of a shell is not considered disallowed so we simply return\n      return\n    }\n\n    if (serverDynamic.syncDynamicErrorWithStack) {\n      // There is no shell and the server did something sync dynamic likely\n      // leading to an early termination of the prerender before the shell\n      // could be completed. We terminate the build/validating render.\n      logDisallowedDynamicError(\n        workStore,\n        serverDynamic.syncDynamicErrorWithStack\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    // We didn't have any sync bailouts but there may be user code which\n    // blocked the root. We would have captured these during the prerender\n    // and can log them here and then terminate the build/validating render\n    const dynamicErrors = dynamicValidation.dynamicErrors\n    if (dynamicErrors.length > 0) {\n      for (let i = 0; i < dynamicErrors.length; i++) {\n        logDisallowedDynamicError(workStore, dynamicErrors[i])\n      }\n\n      throw new StaticGenBailoutError()\n    }\n\n    // If we got this far then the only other thing that could be blocking\n    // the root is dynamic Viewport. If this is dynamic then\n    // you need to opt into that by adding a Suspense boundary above the body\n    // to indicate your are ok with fully dynamic rendering.\n    if (dynamicValidation.hasDynamicViewport) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    if (prelude === PreludeState.Empty) {\n      // If we ever get this far then we messed up the tracking of invalid dynamic.\n      // We still adhere to the constraint that you must produce a shell but invite the\n      // user to report this as a bug in Next.js.\n      console.error(\n        `Route \"${workStore.route}\" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`\n      )\n      throw new StaticGenBailoutError()\n    }\n  } else {\n    if (\n      dynamicValidation.hasAllowedDynamic === false &&\n      dynamicValidation.hasDynamicMetadata\n    ) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`\n      )\n      throw new StaticGenBailoutError()\n    }\n  }\n}\n"], "names": ["Postpone", "PreludeState", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicErrorWithStack", "hasSuspenseAboveBody", "hasDynamicMetadata", "hasDynamicViewport", "hasAllowedDynamic", "dynamicErrors", "trackingState", "expression", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "StaticGenBailoutError", "route", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "prerenderStore", "workUnitAsyncStorage", "getStore", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "undefined", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "signal", "aborted", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "length", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "x", "cacheSignal", "inputReady", "then", "scheduleOnNextTick", "workStore", "workAsyncStorage", "isStaticGeneration", "fallbackRouteParams", "size", "use", "makeHangingPromise", "renderSignal", "hasSuspenseRegex", "hasSuspenseAfterBodyOrHtmlRegex", "hasMetadataRegex", "RegExp", "METADATA_BOUNDARY_NAME", "hasViewportRegex", "VIEWPORT_BOUNDARY_NAME", "hasOutletRegex", "OUTLET_BOUNDARY_NAME", "componentStack", "dynamicValidation", "test", "createErrorWithComponentOrOwnerStack", "ownerStack", "captureOwnerStack", "name", "logDisallowedDynamicError", "console", "dev", "hasReadableErrorStacks", "prelude", "invalidDynamicUsageError", "i"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgVeA,QAAQ,EAAA;eAARA;;IA2TJC,YAAY,EAAA;eAAZA;;IApWIC,2CAA2C,EAAA;eAA3CA;;IArCAC,kCAAkC,EAAA;eAAlCA;;IAwKAC,mBAAmB,EAAA;eAAnBA;;IA4GAC,qBAAqB,EAAA;eAArBA;;IAtGAC,oBAAoB,EAAA;eAApBA;;IA/WAC,0BAA0B,EAAA;eAA1BA;;IAUAC,4BAA4B,EAAA;eAA5BA;;IAmbAC,6BAA6B,EAAA;eAA7BA;;IAjBAC,0BAA0B,EAAA;eAA1BA;;IAlDAC,wBAAwB,EAAA;eAAxBA;;IAtWAC,qBAAqB,EAAA;eAArBA;;IAgSAC,iBAAiB,EAAA;eAAjBA;;IAwCAC,2BAA2B,EAAA;eAA3BA;;IA3TAC,yBAAyB,EAAA;eAAzBA;;IAuPAC,oBAAoB,EAAA;eAApBA;;IAwUAC,wBAAwB,EAAA;eAAxBA;;IA/eAC,gCAAgC,EAAA;eAAhCA;;IA+ZAC,yBAAyB,EAAA;eAAzBA;;IAtYAC,+BAA+B,EAAA;eAA/BA;;IAzCAC,0BAA0B,EAAA;eAA1BA;;IAmHAC,qCAAqC,EAAA;eAArCA;;IAiDHC,sCAAsC,EAAA;eAAtCA;;IA+NGC,qBAAqB,EAAA;eAArBA;;;8DA1hBE;oCAEiB;yCACG;8CACD;0CACJ;uCACE;mCAK5B;2BAC4B;;;;;;AAEnC,MAAMC,cAAc,OAAOC,OAAAA,OAAK,CAACC,iBAAiB,KAAK;AAwChD,SAASpB,2BACdqB,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,2BAA2B;IAC7B;AACF;AAEO,SAAStB;IACd,OAAO;QACLuB,sBAAsB;QACtBC,oBAAoB;QACpBC,oBAAoB;QACpBC,mBAAmB;QACnBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASvB,sBACdwB,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcP,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCO,gCAAkCC,UAAU;AACrD;AASO,SAAStB,0BACduB,KAAgB,EAChBC,aAAuE,EACvEF,UAAkB;IAElB,IAAIE,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAIF,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEN,MAAMO,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIE,eAAe;QACjB,IAAIA,cAAcC,IAAI,KAAK,iBAAiB;YAC1CxB,qBACEsB,MAAMO,KAAK,EACXR,YACAE,cAAcO,eAAe;QAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;YACpDD,cAAcQ,UAAU,GAAG;YAE3B,uGAAuG;YACvG,MAAMC,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAC,MAAMY,uBAAuB,GAAGb;YAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;YAEnC,MAAMJ;QACR,OAAO,IACLK,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,iBACAA,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAUO,SAASnC,2BACdiB,KAAgB,EAChBD,UAAkB;IAElB,MAAMoB,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IAAI,CAACF,kBAAkBA,eAAejB,IAAI,KAAK,iBAAiB;IAEhExB,qBAAqBsB,MAAMO,KAAK,EAAER,YAAYoB,eAAeX,eAAe;AAC9E;AAQO,SAAS5B,iCACdmB,UAAkB,EAClBC,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAoB,eAAeV,UAAU,GAAG;IAE5BT,MAAMY,uBAAuB,GAAGb;IAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;IAEnC,MAAMJ;AACR;AASO,SAAS5B,gCACdwC,MAAiB,EACjBrB,aAAmC;IAEnC,IAAIA,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;QACA,mFAAmF;QACnF,IACED,cAAcC,IAAI,KAAK,eACvBD,cAAcC,IAAI,KAAK,sBACvBD,cAAcC,IAAI,KAAK,oBACvB;YACAD,cAAcQ,UAAU,GAAG;QAC7B;QACA,IACEM,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAEA,SAASK,oCACPhB,KAAa,EACbR,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMK,SAAS,CAAC,MAAM,EAAEjB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAM0B,QAAQC,gCAAgCF;IAE9CL,eAAeQ,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMjB,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBjB,eAAe,CAACsC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBlB,sBAAsB,GACzC,IAAIwC,QAAQhB,KAAK,GACjBiB;YACJhC;QACF;IACF;AACF;AAEO,SAASlC,mCACd0C,KAAa,EACbR,UAAkB,EAClBiC,cAAqB,EACrBb,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtDe,oCAAoChB,OAAOR,YAAYoB;IACvD,sFAAsF;IACtF,0FAA0F;IAC1F,sFAAsF;IACtF,oDAAoD;IACpD,IAAIX,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBhB,yBAAyB,GAAGwC;QAC9C;IACF;AACF;AAEO,SAAShD,sCACdiD,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAAStE,4CACd2C,KAAa,EACbR,UAAkB,EAClBiC,cAAqB,EACrBb,cAAoC;IAEpC,MAAMgB,kBAAkBhB,eAAeQ,UAAU,CAACS,MAAM;IACxD,IAAID,gBAAgBE,OAAO,KAAK,OAAO;QACrC,8FAA8F;QAC9F,mFAAmF;QACnF,wFAAwF;QACxF,4FAA4F;QAC5F,0BAA0B;QAC1Bd,oCAAoChB,OAAOR,YAAYoB;QACvD,sFAAsF;QACtF,0FAA0F;QAC1F,sFAAsF;QACtF,oDAAoD;QACpD,MAAMX,kBAAkBW,eAAeX,eAAe;QACtD,IAAIA,iBAAiB;YACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;gBACtDgB,gBAAgBhB,yBAAyB,GAAGwC;YAC9C;QACF;IACF;IACA,MAAMN,gCACJ,CAAC,MAAM,EAAEnB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AAGO,MAAMd,yCACXD;AASK,SAAStB,SAAS,EAAE8D,MAAM,EAAEjB,KAAK,EAAiB;IACvD,MAAMY,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,MAAMb,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACN9B,qBAAqB6B,OAAOiB,QAAQhB;AACtC;AAEO,SAAS9B,qBACd6B,KAAa,EACbR,UAAkB,EAClBS,eAA4C;IAE5C8B;IACA,IAAI9B,iBAAiB;QACnBA,gBAAgBjB,eAAe,CAACsC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBlB,sBAAsB,GACzC,IAAIwC,QAAQhB,KAAK,GACjBiB;YACJhC;QACF;IACF;IAEAX,OAAAA,OAAK,CAACC,iBAAiB,CAACkD,qBAAqBhC,OAAOR;AACtD;AAEA,SAASwC,qBAAqBhC,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAASxB,kBAAkBmC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAY8B,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyB/B,IAAY8B,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBjB,MAAc;IAC7C,OACEA,OAAOkB,QAAQ,CACb,sEAEFlB,OAAOkB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBF,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,OAAA,cAEL,CAFK,IAAIT,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMa,6BAA6B;AAEnC,SAASjB,gCAAgCc,OAAe;IACtD,MAAMf,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMU,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7Bf,MAAcmB,MAAM,GAAGD;IACzB,OAAOlB;AACT;AAMO,SAASjD,4BACdiD,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAcmB,MAAM,KAAKD,8BAC1B,UAAUlB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAAShE,oBACdyB,eAAqC;IAErC,OAAOA,gBAAgBsD,MAAM,GAAG;AAClC;AAEO,SAAS7E,qBACd8E,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAcvD,eAAe,CAACsC,IAAI,IAAIkB,cAAcxD,eAAe;IACnE,OAAOuD,cAAcvD,eAAe;AACtC;AAEO,SAASlB,yBACdkB,eAAqC;IAErC,OAAOA,gBACJyD,MAAM,CACL,CAACC,SACC,OAAOA,OAAOnC,KAAK,KAAK,YAAYmC,OAAOnC,KAAK,CAAC+B,MAAM,GAAG,GAE7DK,GAAG,CAAC,CAAC,EAAEnD,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLqC,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKX,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIW,KAAKX,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIW,KAAKX,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCY,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAEvD,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAASwB;IACP,IAAI,CAACnD,aAAa;QAChB,MAAM,OAAA,cAEL,CAFK,IAAI2C,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAAS1D,2BAA2BoD,MAAc;IACvDc;IACA,MAAMX,aAAa,IAAI4B;IACvB,qFAAqF;IACrF,IAAI;QACFnE,OAAAA,OAAK,CAACC,iBAAiB,CAACmC;IAC1B,EAAE,OAAOgC,GAAY;QACnB7B,WAAWC,KAAK,CAAC4B;IACnB;IACA,OAAO7B,WAAWS,MAAM;AAC1B;AAOO,SAASjE,8BACd8B,aAAmC;IAEnC,MAAM0B,aAAa,IAAI4B;IAEvB,IAAItD,cAAcwD,WAAW,EAAE;QAC7B,gFAAgF;QAChF,mFAAmF;QACnF,uCAAuC;QACvCxD,cAAcwD,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;YAC1ChC,WAAWC,KAAK;QAClB;IACF,OAAO;QACL,gFAAgF;QAChF,kFAAkF;QAClF,gFAAgF;QAChF,+EAA+E;QAC/E,0DAA0D;QAC1DgC,CAAAA,GAAAA,WAAAA,kBAAkB,EAAC,IAAMjC,WAAWC,KAAK;IAC3C;IAEA,OAAOD,WAAWS,MAAM;AAC1B;AAEO,SAASrE,sBACdgC,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBjB,eAAe,CAACsC,IAAI,CAAC;YACnCf,OAAON,gBAAgBlB,sBAAsB,GACzC,IAAIwC,QAAQhB,KAAK,GACjBiB;YACJhC;QACF;IACF;AACF;AAEO,SAASb,sBAAsBa,UAAkB;IACtD,MAAM8D,YAAYC,0BAAAA,gBAAgB,CAACzC,QAAQ;IAE3C,IACEwC,aACAA,UAAUE,kBAAkB,IAC5BF,UAAUG,mBAAmB,IAC7BH,UAAUG,mBAAmB,CAACC,IAAI,GAAG,GACrC;QACA,oEAAoE;QACpE,YAAY;QACZ,MAAMhE,gBAAgBmB,8BAAAA,oBAAoB,CAACC,QAAQ;QACnD,IAAIpB,eAAe;YACjB,mDAAmD;YACnD,IAAIA,cAAcC,IAAI,KAAK,oBAAoB;gBAC7C,iDAAiD;gBACjD,6EAA6E;gBAC7E,uDAAuD;gBACvDd,OAAAA,OAAK,CAAC8E,GAAG,CAACC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAAClE,cAAcmE,YAAY,EAAErE;YAC3D,OAAO,IAAIE,cAAcC,IAAI,KAAK,iBAAiB;gBACjD,8BAA8B;gBAC9BxB,qBACEmF,UAAUtD,KAAK,EACfR,YACAE,cAAcO,eAAe;YAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;gBACpDtB,iCAAiCmB,YAAY8D,WAAW5D;YAC1D;QACF;IACF;AACF;AAEA,MAAMoE,mBAAmB;AACzB,MAAMC,kCACJ;AACF,MAAMC,mBAAmB,IAAIC,OAC3B,CAAC,UAAU,EAAEC,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAEG,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,iBAAiB,IAAIJ,OAAO,CAAC,UAAU,EAAEK,mBAAAA,oBAAoB,CAAC,QAAQ,CAAC;AAEtE,SAAShG,0BACdgF,SAAoB,EACpBiB,cAAsB,EACtBC,iBAAyC,EACzChC,aAAmC;IAEnC,IAAI6B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIP,iBAAiBS,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBrF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIgF,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBpF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI2E,gCAAgCU,IAAI,CAACF,iBAAiB;QAC/D,8DAA8D;QAC9D,iEAAiE;QACjEC,kBAAkBnF,iBAAiB,GAAG;QACtCmF,kBAAkBtF,oBAAoB,GAAG;QACzC;IACF,OAAO,IAAI4E,iBAAiBW,IAAI,CAACF,iBAAiB;QAChD,wFAAwF;QACxF,gBAAgB;QAChBC,kBAAkBnF,iBAAiB,GAAG;QACtC;IACF,OAAO,IAAImD,cAAcvD,yBAAyB,EAAE;QAClD,qDAAqD;QACrDuF,kBAAkBlF,aAAa,CAACgC,IAAI,CAClCkB,cAAcvD,yBAAyB;QAEzC;IACF,OAAO;QACL,MAAMgD,UAAU,CAAC,OAAO,EAAEqB,UAAUtD,KAAK,CAAC,2NAA2N,CAAC;QACtQ,MAAMkB,QAAQwD,qCAAqCzC,SAASsC;QAC5DC,kBAAkBlF,aAAa,CAACgC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA;;;CAGC,GACD,SAASwD,qCACPzC,OAAe,EACfsC,cAAsB;IAEtB,MAAMI,aACJnE,QAAQC,GAAG,CAACC,QAAQ,gCAAK,gBAAgB7B,OAAAA,OAAK,CAAC+F,iBAAiB,GAC5D/F,OAAAA,OAAK,CAAC+F,iBAAiB,KACvB;IAEN,MAAM1D,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMU,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/Bf,MAAMX,KAAK,GAAGW,MAAM2D,IAAI,GAAG,OAAO5C,UAAW0C,CAAAA,cAAcJ,cAAa;IACxE,OAAOrD;AACT;AAEO,IAAK9D,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;;WAAAA;;AAMZ,SAAS0H,0BAA0BxB,SAAoB,EAAEpC,KAAY;IACnE6D,QAAQ7D,KAAK,CAACA;IAEd,IAAI,CAACoC,UAAU0B,GAAG,EAAE;QAClB,IAAI1B,UAAU2B,sBAAsB,EAAE;YACpCF,QAAQ7D,KAAK,CACX,CAAC,iIAAiI,EAAEoC,UAAUtD,KAAK,CAAC,2CAA2C,CAAC;QAEpM,OAAO;YACL+E,QAAQ7D,KAAK,CAAC,CAAC;0EACqD,EAAEoC,UAAUtD,KAAK,CAAC;qGACS,CAAC;QAClG;IACF;AACF;AAEO,SAAS5B,yBACdkF,SAAoB,EACpB4B,OAAqB,EACrBV,iBAAyC,EACzCjC,aAAmC;IAEnC,IAAIe,UAAU6B,wBAAwB,EAAE;QACtCL,0BAA0BxB,WAAWA,UAAU6B,wBAAwB;QACvE,MAAM,IAAIpF,yBAAAA,qBAAqB;IACjC;IAEA,IAAImF,YAAAA,GAA+B;QACjC,IAAIV,kBAAkBtF,oBAAoB,EAAE;YAC1C,6DAA6D;YAC7D,gEAAgE;YAChE,qEAAqE;YACrE;QACF;QAEA,IAAIqD,cAActD,yBAAyB,EAAE;YAC3C,qEAAqE;YACrE,oEAAoE;YACpE,gEAAgE;YAChE6F,0BACExB,WACAf,cAActD,yBAAyB;YAEzC,MAAM,IAAIc,yBAAAA,qBAAqB;QACjC;QAEA,oEAAoE;QACpE,sEAAsE;QACtE,uEAAuE;QACvE,MAAMT,gBAAgBkF,kBAAkBlF,aAAa;QACrD,IAAIA,cAAcgD,MAAM,GAAG,GAAG;YAC5B,IAAK,IAAI8C,IAAI,GAAGA,IAAI9F,cAAcgD,MAAM,EAAE8C,IAAK;gBAC7CN,0BAA0BxB,WAAWhE,aAAa,CAAC8F,EAAE;YACvD;YAEA,MAAM,IAAIrF,yBAAAA,qBAAqB;QACjC;QAEA,sEAAsE;QACtE,wDAAwD;QACxD,yEAAyE;QACzE,wDAAwD;QACxD,IAAIyE,kBAAkBpF,kBAAkB,EAAE;YACxC2F,QAAQ7D,KAAK,CACX,CAAC,OAAO,EAAEoC,UAAUtD,KAAK,CAAC,8QAA8Q,CAAC;YAE3S,MAAM,IAAID,yBAAAA,qBAAqB;QACjC;QAEA,IAAImF,YAAAA,GAAgC;YAClC,6EAA6E;YAC7E,iFAAiF;YACjF,2CAA2C;YAC3CH,QAAQ7D,KAAK,CACX,CAAC,OAAO,EAAEoC,UAAUtD,KAAK,CAAC,wGAAwG,CAAC;YAErI,MAAM,IAAID,yBAAAA,qBAAqB;QACjC;IACF,OAAO;QACL,IACEyE,kBAAkBnF,iBAAiB,KAAK,SACxCmF,kBAAkBrF,kBAAkB,EACpC;YACA4F,QAAQ7D,KAAK,CACX,CAAC,OAAO,EAAEoC,UAAUtD,KAAK,CAAC,8PAA8P,CAAC;YAE3R,MAAM,IAAID,yBAAAA,qBAAqB;QACjC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1356, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/client/components/unstable-rethrow.server.ts"], "sourcesContent": ["import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["unstable_rethrow", "error", "isNextRouterError", "isBailoutToCSRError", "isDynamicServerError", "isDynamicPostpone", "isPostpone", "isHangingPromiseRejectionError", "Error", "cause"], "mappings": ";;;+BAOg<PERSON>,oBAAAA;;;eAAAA;;;uCAP+B;4BACpB;8BACS;mCACF;kCACA;oCACG;AAE9B,SAASA,iBAAiBC,KAAc;IAC7C,IACEC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACD,UAClBE,CAAAA,GAAAA,cAAAA,mBAAmB,EAACF,UACpBG,CAAAA,GAAAA,oBAAAA,oBAAoB,EAACH,UACrBI,CAAAA,GAAAA,kBAAAA,iBAAiB,EAACJ,UAClBK,CAAAA,GAAAA,YAAAA,UAAU,EAACL,UACXM,CAAAA,GAAAA,uBAAAA,8BAA8B,EAACN,QAC/B;QACA,MAAMA;IACR;IAEA,IAAIA,iBAAiBO,SAAS,WAAWP,OAAO;QAC9CD,iBAAiBC,MAAMQ,KAAK;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1392, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/client/components/unstable-rethrow.ts"], "sourcesContent": ["/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n"], "names": ["unstable_rethrow", "window", "require"], "mappings": "AAAA;;;;;;CAMC;;;+BACY<PERSON>,oBAAAA;;;eAAAA;;;AAAN,MAAMA,mBACX,OAAOC,WAAW,qBAEZC,QAAQ,wHACRF,gBAAgB,GAEhBE,QAAQ,8BACRF,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1421, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/client/components/navigation.react-server.ts"], "sourcesContent": ["/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n"], "names": ["ReadonlyURLSearchParams", "RedirectType", "forbidden", "notFound", "permanentRedirect", "redirect", "unauthorized", "unstable_rethrow", "ReadonlyURLSearchParamsError", "Error", "constructor", "URLSearchParams", "append", "delete", "set", "sort"], "mappings": "AAAA,cAAc;;;;;;;;;;;;;;;;;;;;IAkCLA,uBAAuB,EAAA;eAAvBA;;IALAC,YAAY,EAAA;eAAZA,eAAAA,YAAY;;IAEZC,SAAS,EAAA;eAATA,WAAAA,SAAS;;IADTC,QAAQ,EAAA;eAARA,UAAAA,QAAQ;;IAFEC,iBAAiB,EAAA;eAAjBA,UAAAA,iBAAiB;;IAA3BC,QAAQ,EAAA;eAARA,UAAAA,QAAQ;;IAIRC,YAAY,EAAA;eAAZA,cAAAA,YAAY;;IACZC,gBAAgB,EAAA;eAAhBA,iBAAAA,gBAAgB;;;0BALmB;+BACf;0BACJ;2BACC;8BACG;iCACI;AAhCjC,MAAMC,qCAAqCC;IACzCC,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AAEA,MAAMV,gCAAgCW;IACpC,wKAAwK,GACxKC,SAAS;QACP,MAAM,IAAIJ;IACZ;IACA,wKAAwK,GACxKK,SAAS;QACP,MAAM,IAAIL;IACZ;IACA,wKAAwK,GACxKM,MAAM;QACJ,MAAM,IAAIN;IACZ;IACA,wKAAwK,GACxKO,OAAO;QACL,MAAM,IAAIP;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/src/api/navigation.react-server.ts"], "sourcesContent": ["export * from '../client/components/navigation.react-server'\n"], "names": [], "mappings": ";AAAA,cAAc,+CAA8C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1517, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1539, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/instrumentation/utils.ts"], "sourcesContent": ["export function getRevalidateReason(params: {\n  isOnDemandRevalidate?: boolean\n  isRevalidate?: boolean\n}): 'on-demand' | 'stale' | undefined {\n  if (params.isOnDemandRevalidate) {\n    return 'on-demand'\n  }\n  if (params.isRevalidate) {\n    return 'stale'\n  }\n  return undefined\n}\n"], "names": ["getRevalidateReason", "params", "isOnDemandRevalidate", "isRevalidate", "undefined"], "mappings": ";;;AAAO,SAASA,oBAAoBC,MAGnC;IACC,IAAIA,OAAOC,oBAAoB,EAAE;QAC/B,OAAO;IACT;IACA,IAAID,OAAOE,YAAY,EAAE;QACvB,OAAO;IACT;IACA,OAAOC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/app-render/interop-default.ts"], "sourcesContent": ["/**\n * Interop between \"export default\" and \"module.exports\".\n */\nexport function interopDefault(mod: any) {\n  return mod.default || mod\n}\n"], "names": ["interopDefault", "mod", "default"], "mappings": "AAAA;;CAEC,GACD;;;AAAO,SAASA,eAAeC,GAAQ;IACrC,OAAOA,IAAIC,OAAO,IAAID;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1567, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/web/spec-extension/adapters/headers.ts"], "sourcesContent": ["import type { IncomingHttpHeaders } from 'http'\n\nimport { ReflectAdapter } from './reflect'\n\n/**\n * @internal\n */\nexport class ReadonlyHeadersError extends Error {\n  constructor() {\n    super(\n      'Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers'\n    )\n  }\n\n  public static callable() {\n    throw new ReadonlyHeadersError()\n  }\n}\n\nexport type ReadonlyHeaders = Headers & {\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  append(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  set(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  delete(...args: any[]): void\n}\nexport class HeadersAdapter extends Headers {\n  private readonly headers: IncomingHttpHeaders\n\n  constructor(headers: IncomingHttpHeaders) {\n    // We've already overridden the methods that would be called, so we're just\n    // calling the super constructor to ensure that the instanceof check works.\n    super()\n\n    this.headers = new Proxy(headers, {\n      get(target, prop, receiver) {\n        // Because this is just an object, we expect that all \"get\" operations\n        // are for properties. If it's a \"get\" for a symbol, we'll just return\n        // the symbol.\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return undefined.\n        if (typeof original === 'undefined') return\n\n        // If the original casing exists, return the value.\n        return ReflectAdapter.get(target, original, receiver)\n      },\n      set(target, prop, value, receiver) {\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.set(target, prop, value, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, use the prop as the key.\n        return ReflectAdapter.set(target, original ?? prop, value, receiver)\n      },\n      has(target, prop) {\n        if (typeof prop === 'symbol') return ReflectAdapter.has(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return false.\n        if (typeof original === 'undefined') return false\n\n        // If the original casing exists, return true.\n        return ReflectAdapter.has(target, original)\n      },\n      deleteProperty(target, prop) {\n        if (typeof prop === 'symbol')\n          return ReflectAdapter.deleteProperty(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return true.\n        if (typeof original === 'undefined') return true\n\n        // If the original casing exists, delete the property.\n        return ReflectAdapter.deleteProperty(target, original)\n      },\n    })\n  }\n\n  /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */\n  public static seal(headers: Headers): ReadonlyHeaders {\n    return new Proxy<ReadonlyHeaders>(headers, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'append':\n          case 'delete':\n          case 'set':\n            return ReadonlyHeadersError.callable\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n  }\n\n  /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */\n  private merge(value: string | string[]): string {\n    if (Array.isArray(value)) return value.join(', ')\n\n    return value\n  }\n\n  /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */\n  public static from(headers: IncomingHttpHeaders | Headers): Headers {\n    if (headers instanceof Headers) return headers\n\n    return new HeadersAdapter(headers)\n  }\n\n  public append(name: string, value: string): void {\n    const existing = this.headers[name]\n    if (typeof existing === 'string') {\n      this.headers[name] = [existing, value]\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      this.headers[name] = value\n    }\n  }\n\n  public delete(name: string): void {\n    delete this.headers[name]\n  }\n\n  public get(name: string): string | null {\n    const value = this.headers[name]\n    if (typeof value !== 'undefined') return this.merge(value)\n\n    return null\n  }\n\n  public has(name: string): boolean {\n    return typeof this.headers[name] !== 'undefined'\n  }\n\n  public set(name: string, value: string): void {\n    this.headers[name] = value\n  }\n\n  public forEach(\n    callbackfn: (value: string, name: string, parent: Headers) => void,\n    thisArg?: any\n  ): void {\n    for (const [name, value] of this.entries()) {\n      callbackfn.call(thisArg, value, name, this)\n    }\n  }\n\n  public *entries(): HeadersIterator<[string, string]> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(name) as string\n\n      yield [name, value] as [string, string]\n    }\n  }\n\n  public *keys(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      yield name\n    }\n  }\n\n  public *values(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(key) as string\n\n      yield value\n    }\n  }\n\n  public [Symbol.iterator](): HeadersIterator<[string, string]> {\n    return this.entries()\n  }\n}\n"], "names": ["ReflectAdapter", "ReadonlyHeadersError", "Error", "constructor", "callable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Headers", "headers", "Proxy", "get", "target", "prop", "receiver", "lowercased", "toLowerCase", "original", "Object", "keys", "find", "o", "set", "value", "has", "deleteProperty", "seal", "merge", "Array", "isArray", "join", "from", "append", "name", "existing", "push", "delete", "for<PERSON>ach", "callbackfn", "thisArg", "entries", "call", "key", "values", "Symbol", "iterator"], "mappings": ";;;;AAEA,SAASA,cAAc,QAAQ,YAAW;;AAKnC,MAAMC,6BAA6BC;IACxCC,aAAc;QACZ,KAAK,CACH;IAEJ;IAEA,OAAcC,WAAW;QACvB,MAAM,IAAIH;IACZ;AACF;AAUO,MAAMI,uBAAuBC;IAGlCH,YAAYI,OAA4B,CAAE;QACxC,2EAA2E;QAC3E,2EAA2E;QAC3E,KAAK;QAEL,IAAI,CAACA,OAAO,GAAG,IAAIC,MAAMD,SAAS;YAChCE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,sEAAsE;gBACtE,sEAAsE;gBACtE,cAAc;gBACd,IAAI,OAAOD,SAAS,UAAU;oBAC5B,wMAAOX,iBAAAA,CAAeS,GAAG,CAACC,QAAQC,MAAMC;gBAC1C;gBAEA,MAAMC,aAAaF,KAAKG,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACV,SAASW,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,0DAA0D;gBAC1D,IAAI,OAAOE,aAAa,aAAa;gBAErC,mDAAmD;gBACnD,wMAAOf,iBAAAA,CAAeS,GAAG,CAACC,QAAQK,UAAUH;YAC9C;YACAQ,KAAIV,MAAM,EAAEC,IAAI,EAAEU,KAAK,EAAET,QAAQ;gBAC/B,IAAI,OAAOD,SAAS,UAAU;oBAC5B,wMAAOX,iBAAAA,CAAeoB,GAAG,CAACV,QAAQC,MAAMU,OAAOT;gBACjD;gBAEA,MAAMC,aAAaF,KAAKG,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACV,SAASW,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,iEAAiE;gBACjE,wMAAOb,iBAAAA,CAAeoB,GAAG,CAACV,QAAQK,YAAYJ,MAAMU,OAAOT;YAC7D;YACAU,KAAIZ,MAAM,EAAEC,IAAI;gBACd,IAAI,OAAOA,SAAS,UAAU,wMAAOX,iBAAAA,CAAesB,GAAG,CAACZ,QAAQC;gBAEhE,MAAME,aAAaF,KAAKG,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACV,SAASW,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,sDAAsD;gBACtD,IAAI,OAAOE,aAAa,aAAa,OAAO;gBAE5C,8CAA8C;gBAC9C,wMAAOf,iBAAAA,CAAesB,GAAG,CAACZ,QAAQK;YACpC;YACAQ,gBAAeb,MAAM,EAAEC,IAAI;gBACzB,IAAI,OAAOA,SAAS,UAClB,wMAAOX,iBAAAA,CAAeuB,cAAc,CAACb,QAAQC;gBAE/C,MAAME,aAAaF,KAAKG,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACV,SAASW,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,qDAAqD;gBACrD,IAAI,OAAOE,aAAa,aAAa,OAAO;gBAE5C,sDAAsD;gBACtD,wMAAOf,iBAAAA,CAAeuB,cAAc,CAACb,QAAQK;YAC/C;QACF;IACF;IAEA;;;GAGC,GACD,OAAcS,KAAKjB,OAAgB,EAAmB;QACpD,OAAO,IAAIC,MAAuBD,SAAS;YACzCE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH,OAAOV,qBAAqBG,QAAQ;oBACtC;wBACE,wMAAOJ,iBAAAA,CAAeS,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;IACF;IAEA;;;;;;GAMC,GACOa,MAAMJ,KAAwB,EAAU;QAC9C,IAAIK,MAAMC,OAAO,CAACN,QAAQ,OAAOA,MAAMO,IAAI,CAAC;QAE5C,OAAOP;IACT;IAEA;;;;;GAKC,GACD,OAAcQ,KAAKtB,OAAsC,EAAW;QAClE,IAAIA,mBAAmBD,SAAS,OAAOC;QAEvC,OAAO,IAAIF,eAAeE;IAC5B;IAEOuB,OAAOC,IAAY,EAAEV,KAAa,EAAQ;QAC/C,MAAMW,WAAW,IAAI,CAACzB,OAAO,CAACwB,KAAK;QACnC,IAAI,OAAOC,aAAa,UAAU;YAChC,IAAI,CAACzB,OAAO,CAACwB,KAAK,GAAG;gBAACC;gBAAUX;aAAM;QACxC,OAAO,IAAIK,MAAMC,OAAO,CAACK,WAAW;YAClCA,SAASC,IAAI,CAACZ;QAChB,OAAO;YACL,IAAI,CAACd,OAAO,CAACwB,KAAK,GAAGV;QACvB;IACF;IAEOa,OAAOH,IAAY,EAAQ;QAChC,OAAO,IAAI,CAACxB,OAAO,CAACwB,KAAK;IAC3B;IAEOtB,IAAIsB,IAAY,EAAiB;QACtC,MAAMV,QAAQ,IAAI,CAACd,OAAO,CAACwB,KAAK;QAChC,IAAI,OAAOV,UAAU,aAAa,OAAO,IAAI,CAACI,KAAK,CAACJ;QAEpD,OAAO;IACT;IAEOC,IAAIS,IAAY,EAAW;QAChC,OAAO,OAAO,IAAI,CAACxB,OAAO,CAACwB,KAAK,KAAK;IACvC;IAEOX,IAAIW,IAAY,EAAEV,KAAa,EAAQ;QAC5C,IAAI,CAACd,OAAO,CAACwB,KAAK,GAAGV;IACvB;IAEOc,QACLC,UAAkE,EAClEC,OAAa,EACP;QACN,KAAK,MAAM,CAACN,MAAMV,MAAM,IAAI,IAAI,CAACiB,OAAO,GAAI;YAC1CF,WAAWG,IAAI,CAACF,SAAShB,OAAOU,MAAM,IAAI;QAC5C;IACF;IAEA,CAAQO,UAA6C;QACnD,KAAK,MAAME,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACV,OAAO,EAAG;YAC3C,MAAMwB,OAAOS,IAAI1B,WAAW;YAC5B,kEAAkE;YAClE,4BAA4B;YAC5B,MAAMO,QAAQ,IAAI,CAACZ,GAAG,CAACsB;YAEvB,MAAM;gBAACA;gBAAMV;aAAM;QACrB;IACF;IAEA,CAAQJ,OAAgC;QACtC,KAAK,MAAMuB,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACV,OAAO,EAAG;YAC3C,MAAMwB,OAAOS,IAAI1B,WAAW;YAC5B,MAAMiB;QACR;IACF;IAEA,CAAQU,SAAkC;QACxC,KAAK,MAAMD,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACV,OAAO,EAAG;YAC3C,kEAAkE;YAClE,4BAA4B;YAC5B,MAAMc,QAAQ,IAAI,CAACZ,GAAG,CAAC+B;YAEvB,MAAMnB;QACR;IACF;IAEO,CAACqB,OAAOC,QAAQ,CAAC,GAAsC;QAC5D,OAAO,IAAI,CAACL,OAAO;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1744, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/api-utils/index.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { BaseNextRequest } from '../base-http'\nimport type { CookieSerializeOptions } from 'next/dist/compiled/cookie'\nimport type { NextApiResponse } from '../../shared/lib/utils'\n\nimport { HeadersAdapter } from '../web/spec-extension/adapters/headers'\nimport {\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n} from '../../lib/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { NodeSpan } from '../lib/trace/constants'\n\nexport type NextApiRequestCookies = Partial<{ [key: string]: string }>\nexport type NextApiRequestQuery = Partial<{ [key: string]: string | string[] }>\n\nexport type __ApiPreviewProps = {\n  previewModeId: string\n  previewModeEncryptionKey: string\n  previewModeSigningKey: string\n}\n\nexport function wrapApiHandler<T extends (...args: any[]) => any>(\n  page: string,\n  handler: T\n): T {\n  return ((...args) => {\n    getTracer().setRootSpanAttribute('next.route', page)\n    // Call API route method\n    return getTracer().trace(\n      NodeSpan.runHandler,\n      {\n        spanName: `executing api route (pages) ${page}`,\n      },\n      () => handler(...args)\n    )\n  }) as T\n}\n\n/**\n *\n * @param res response object\n * @param statusCode `HTTP` status code of response\n */\nexport function sendStatusCode(\n  res: NextApiResponse,\n  statusCode: number\n): NextApiResponse<any> {\n  res.statusCode = statusCode\n  return res\n}\n\n/**\n *\n * @param res response object\n * @param [statusOrUrl] `HTTP` status code of redirect\n * @param url URL of redirect\n */\nexport function redirect(\n  res: NextApiResponse,\n  statusOrUrl: string | number,\n  url?: string\n): NextApiResponse<any> {\n  if (typeof statusOrUrl === 'string') {\n    url = statusOrUrl\n    statusOrUrl = 307\n  }\n  if (typeof statusOrUrl !== 'number' || typeof url !== 'string') {\n    throw new Error(\n      `Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination').`\n    )\n  }\n  res.writeHead(statusOrUrl, { Location: url })\n  res.write(url)\n  res.end()\n  return res\n}\n\nexport function checkIsOnDemandRevalidate(\n  req: Request | IncomingMessage | BaseNextRequest,\n  previewProps: __ApiPreviewProps\n): {\n  isOnDemandRevalidate: boolean\n  revalidateOnlyGenerated: boolean\n} {\n  const headers = HeadersAdapter.from(req.headers)\n\n  const previewModeId = headers.get(PRERENDER_REVALIDATE_HEADER)\n  const isOnDemandRevalidate = previewModeId === previewProps.previewModeId\n\n  const revalidateOnlyGenerated = headers.has(\n    PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER\n  )\n\n  return { isOnDemandRevalidate, revalidateOnlyGenerated }\n}\n\nexport const COOKIE_NAME_PRERENDER_BYPASS = `__prerender_bypass`\nexport const COOKIE_NAME_PRERENDER_DATA = `__next_preview_data`\n\nexport const RESPONSE_LIMIT_DEFAULT = 4 * 1024 * 1024\n\nexport const SYMBOL_PREVIEW_DATA = Symbol(COOKIE_NAME_PRERENDER_DATA)\nexport const SYMBOL_CLEARED_COOKIES = Symbol(COOKIE_NAME_PRERENDER_BYPASS)\n\nexport function clearPreviewData<T>(\n  res: NextApiResponse<T>,\n  options: {\n    path?: string\n  } = {}\n): NextApiResponse<T> {\n  if (SYMBOL_CLEARED_COOKIES in res) {\n    return res\n  }\n\n  const { serialize } =\n    require('next/dist/compiled/cookie') as typeof import('next/dist/compiled/cookie')\n  const previous = res.getHeader('Set-Cookie')\n  res.setHeader(`Set-Cookie`, [\n    ...(typeof previous === 'string'\n      ? [previous]\n      : Array.isArray(previous)\n        ? previous\n        : []),\n    serialize(COOKIE_NAME_PRERENDER_BYPASS, '', {\n      // To delete a cookie, set `expires` to a date in the past:\n      // https://tools.ietf.org/html/rfc6265#section-4.1.1\n      // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n      expires: new Date(0),\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n    serialize(COOKIE_NAME_PRERENDER_DATA, '', {\n      // To delete a cookie, set `expires` to a date in the past:\n      // https://tools.ietf.org/html/rfc6265#section-4.1.1\n      // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n      expires: new Date(0),\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n  ])\n\n  Object.defineProperty(res, SYMBOL_CLEARED_COOKIES, {\n    value: true,\n    enumerable: false,\n  })\n  return res\n}\n\n/**\n * Custom error class\n */\nexport class ApiError extends Error {\n  readonly statusCode: number\n\n  constructor(statusCode: number, message: string) {\n    super(message)\n    this.statusCode = statusCode\n  }\n}\n\n/**\n * Sends error in `response`\n * @param res response object\n * @param statusCode of response\n * @param message of response\n */\nexport function sendError(\n  res: NextApiResponse,\n  statusCode: number,\n  message: string\n): void {\n  res.statusCode = statusCode\n  res.statusMessage = message\n  res.end(message)\n}\n\ninterface LazyProps {\n  req: IncomingMessage\n}\n\n/**\n * Execute getter function only if its needed\n * @param LazyProps `req` and `params` for lazyProp\n * @param prop name of property\n * @param getter function to get data\n */\nexport function setLazyProp<T>(\n  { req }: LazyProps,\n  prop: string,\n  getter: () => T\n): void {\n  const opts = { configurable: true, enumerable: true }\n  const optsReset = { ...opts, writable: true }\n\n  Object.defineProperty(req, prop, {\n    ...opts,\n    get: () => {\n      const value = getter()\n      // we set the property on the object to avoid recalculating it\n      Object.defineProperty(req, prop, { ...optsReset, value })\n      return value\n    },\n    set: (value) => {\n      Object.defineProperty(req, prop, { ...optsReset, value })\n    },\n  })\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "getTracer", "NodeSpan", "wrapApiHandler", "page", "handler", "args", "setRootSpanAttribute", "trace", "<PERSON><PERSON><PERSON><PERSON>", "spanName", "sendStatusCode", "res", "statusCode", "redirect", "statusOrUrl", "url", "Error", "writeHead", "Location", "write", "end", "checkIsOnDemandRevalidate", "req", "previewProps", "headers", "from", "previewModeId", "get", "isOnDemandRevalidate", "revalidateOnlyGenerated", "has", "COOKIE_NAME_PRERENDER_BYPASS", "COOKIE_NAME_PRERENDER_DATA", "RESPONSE_LIMIT_DEFAULT", "SYMBOL_PREVIEW_DATA", "Symbol", "SYMBOL_CLEARED_COOKIES", "clearPreviewData", "options", "serialize", "require", "previous", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "expires", "Date", "httpOnly", "sameSite", "process", "env", "NODE_ENV", "secure", "path", "undefined", "Object", "defineProperty", "value", "enumerable", "ApiError", "constructor", "message", "sendError", "statusMessage", "setLazyProp", "prop", "getter", "opts", "configurable", "optsReset", "writable", "set"], "mappings": ";;;;;;;;;;;;;;;AAKA,SAASA,cAAc,QAAQ,yCAAwC;AACvE,SACEC,2BAA2B,EAC3BC,0CAA0C,QACrC,sBAAqB;AAC5B,SAASC,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,QAAQ,QAAQ,yBAAwB;;;;;AAW1C,SAASC,eACdC,IAAY,EACZC,OAAU;IAEV,OAAQ,CAAC,GAAGC;oLACVL,YAAAA,IAAYM,oBAAoB,CAAC,cAAcH;QAC/C,wBAAwB;QACxB,OAAOH,wLAAAA,IAAYO,KAAK,4KACtBN,WAAAA,CAASO,UAAU,EACnB;YACEC,UAAU,CAAC,4BAA4B,EAAEN,MAAM;QACjD,GACA,IAAMC,WAAWC;IAErB;AACF;AAOO,SAASK,eACdC,GAAoB,EACpBC,UAAkB;IAElBD,IAAIC,UAAU,GAAGA;IACjB,OAAOD;AACT;AAQO,SAASE,SACdF,GAAoB,EACpBG,WAA4B,EAC5BC,GAAY;IAEZ,IAAI,OAAOD,gBAAgB,UAAU;QACnCC,MAAMD;QACNA,cAAc;IAChB;IACA,IAAI,OAAOA,gBAAgB,YAAY,OAAOC,QAAQ,UAAU;QAC9D,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,CAAC,qKAAqK,CAAC,GADnK,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACAL,IAAIM,SAAS,CAACH,aAAa;QAAEI,UAAUH;IAAI;IAC3CJ,IAAIQ,KAAK,CAACJ;IACVJ,IAAIS,GAAG;IACP,OAAOT;AACT;AAEO,SAASU,0BACdC,GAAgD,EAChDC,YAA+B;IAK/B,MAAMC,2MAAU3B,iBAAAA,CAAe4B,IAAI,CAACH,IAAIE,OAAO;IAE/C,MAAME,gBAAgBF,QAAQG,GAAG,yJAAC7B,8BAAAA;IAClC,MAAM8B,uBAAuBF,kBAAkBH,aAAaG,aAAa;IAEzE,MAAMG,0BAA0BL,QAAQM,GAAG,yJACzC/B,6CAAAA;IAGF,OAAO;QAAE6B;QAAsBC;IAAwB;AACzD;AAEO,MAAME,+BAA+B,CAAC,kBAAkB,CAAC,CAAA;AACzD,MAAMC,6BAA6B,CAAC,mBAAmB,CAAC,CAAA;AAExD,MAAMC,yBAAyB,IAAI,OAAO,KAAI;AAE9C,MAAMC,sBAAsBC,OAAOH,4BAA2B;AAC9D,MAAMI,yBAAyBD,OAAOJ,8BAA6B;AAEnE,SAASM,iBACd1B,GAAuB,EACvB2B,UAEI,CAAC,CAAC;IAEN,IAAIF,0BAA0BzB,KAAK;QACjC,OAAOA;IACT;IAEA,MAAM,EAAE4B,SAAS,EAAE,GACjBC,QAAQ;IACV,MAAMC,WAAW9B,IAAI+B,SAAS,CAAC;IAC/B/B,IAAIgC,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAOF,aAAa,WACpB;YAACA;SAAS,GACVG,MAAMC,OAAO,CAACJ,YACZA,WACA,EAAE;QACRF,UAAUR,8BAA8B,IAAI;YAC1C,2DAA2D;YAC3D,oDAAoD;YACpD,wEAAwE;YACxEe,SAAS,IAAIC,KAAK;YAClBC,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,aAAgB,0BAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,gCAAK;YACjCE,MAAM;YACN,GAAIhB,QAAQgB,IAAI,KAAKC,YAChB;gBAAED,MAAMhB,QAAQgB,IAAI;YAAC,IACtBC,SAAS;QACf;QACAhB,UAAUP,4BAA4B,IAAI;YACxC,2DAA2D;YAC3D,oDAAoD;YACpD,wEAAwE;YACxEc,SAAS,IAAIC,KAAK;YAClBC,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,aAAgB,0BAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,gCAAK;YACjCE,MAAM;YACN,GAAIhB,QAAQgB,IAAI,KAAKC,YAChB;gBAAED,MAAMhB,QAAQgB,IAAI;YAAC,IACtBC,SAAS;QACf;KACD;IAEDC,OAAOC,cAAc,CAAC9C,KAAKyB,wBAAwB;QACjDsB,OAAO;QACPC,YAAY;IACd;IACA,OAAOhD;AACT;AAKO,MAAMiD,iBAAiB5C;IAG5B6C,YAAYjD,UAAkB,EAAEkD,OAAe,CAAE;QAC/C,KAAK,CAACA;QACN,IAAI,CAAClD,UAAU,GAAGA;IACpB;AACF;AAQO,SAASmD,UACdpD,GAAoB,EACpBC,UAAkB,EAClBkD,OAAe;IAEfnD,IAAIC,UAAU,GAAGA;IACjBD,IAAIqD,aAAa,GAAGF;IACpBnD,IAAIS,GAAG,CAAC0C;AACV;AAYO,SAASG,YACd,EAAE3C,GAAG,EAAa,EAClB4C,IAAY,EACZC,MAAe;IAEf,MAAMC,OAAO;QAAEC,cAAc;QAAMV,YAAY;IAAK;IACpD,MAAMW,YAAY;QAAE,GAAGF,IAAI;QAAEG,UAAU;IAAK;IAE5Cf,OAAOC,cAAc,CAACnC,KAAK4C,MAAM;QAC/B,GAAGE,IAAI;QACPzC,KAAK;YACH,MAAM+B,QAAQS;YACd,8DAA8D;YAC9DX,OAAOC,cAAc,CAACnC,KAAK4C,MAAM;gBAAE,GAAGI,SAAS;gBAAEZ;YAAM;YACvD,OAAOA;QACT;QACAc,KAAK,CAACd;YACJF,OAAOC,cAAc,CAACnC,KAAK4C,MAAM;gBAAE,GAAGI,SAAS;gBAAEZ;YAAM;QACzD;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1900, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/api-utils/get-cookie-parser.ts"], "sourcesContent": ["import type { NextApiRequestCookies } from '.'\n\n/**\n * Parse cookies from the `headers` of request\n * @param req request object\n */\n\nexport function getCookieParser(headers: {\n  [key: string]: string | string[] | null | undefined\n}): () => NextApiRequestCookies {\n  return function parseCookie(): NextApiRequestCookies {\n    const { cookie } = headers\n\n    if (!cookie) {\n      return {}\n    }\n\n    const { parse: parseCookieFn } =\n      require('next/dist/compiled/cookie') as typeof import('next/dist/compiled/cookie')\n    return parseCookieFn(Array.isArray(cookie) ? cookie.join('; ') : cookie)\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headers", "parse<PERSON><PERSON><PERSON>", "cookie", "parse", "parseCookieFn", "require", "Array", "isArray", "join"], "mappings": "AAEA;;;CAGC,GAED;;;AAAO,SAASA,gBAAgBC,OAE/B;IACC,OAAO,SAASC;QACd,MAAM,EAAEC,MAAM,EAAE,GAAGF;QAEnB,IAAI,CAACE,QAAQ;YACX,OAAO,CAAC;QACV;QAEA,MAAM,EAAEC,OAAOC,aAAa,EAAE,GAC5BC,QAAQ;QACV,OAAOD,cAAcE,MAAMC,OAAO,CAACL,UAAUA,OAAOM,IAAI,CAAC,QAAQN;IACnE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1920, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/base-http/index.ts"], "sourcesContent": ["import type { IncomingHttpHeaders, OutgoingHttpHeaders } from 'http'\nimport type { I18NConfig } from '../config-shared'\n\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport type { NextApiRequestCookies } from '../api-utils'\nimport { getCookieParser } from '../api-utils/get-cookie-parser'\n\nexport interface BaseNextRequestConfig {\n  basePath: string | undefined\n  i18n?: I18NConfig\n  trailingSlash?: boolean | undefined\n}\n\nexport type FetchMetric = {\n  url: string\n  idx: number\n  end: number\n  start: number\n  method: string\n  status: number\n  cacheReason: string\n  cacheStatus: 'hit' | 'miss' | 'skip' | 'hmr'\n  cacheWarning?: string\n}\n\nexport type FetchMetrics = Array<FetchMetric>\n\nexport abstract class BaseNextRequest<Body = any> {\n  protected _cookies: NextApiRequestCookies | undefined\n  public abstract headers: IncomingHttpHeaders\n  public abstract fetchMetrics: FetchMetric[] | undefined\n\n  constructor(\n    public method: string,\n    public url: string,\n    public body: Body\n  ) {}\n\n  // Utils implemented using the abstract methods above\n\n  public get cookies() {\n    if (this._cookies) return this._cookies\n    return (this._cookies = getCookieParser(this.headers)())\n  }\n}\n\nexport abstract class BaseNextResponse<Destination = any> {\n  abstract statusCode: number | undefined\n  abstract statusMessage: string | undefined\n  abstract get sent(): boolean\n\n  constructor(public destination: Destination) {}\n\n  /**\n   * Sets a value for the header overwriting existing values\n   */\n  abstract setHeader(name: string, value: string | string[]): this\n\n  /**\n   * Removes a header\n   */\n  abstract removeHeader(name: string): this\n\n  /**\n   * Appends value for the given header name\n   */\n  abstract appendHeader(name: string, value: string): this\n\n  /**\n   * Get all values for a header as an array or undefined if no value is present\n   */\n  abstract getHeaderValues(name: string): string[] | undefined\n\n  abstract hasHeader(name: string): boolean\n\n  /**\n   * Get values for a header concatenated using `,` or undefined if no value is present\n   */\n  abstract getHeader(name: string): string | undefined\n\n  abstract getHeaders(): OutgoingHttpHeaders\n\n  abstract body(value: string): this\n\n  abstract send(): void\n\n  abstract onClose(callback: () => void): void\n\n  // Utils implemented using the abstract methods above\n\n  public redirect(destination: string, statusCode: number) {\n    this.setHeader('Location', destination)\n    this.statusCode = statusCode\n\n    // Since IE11 doesn't support the 308 header add backwards\n    // compatibility using refresh header\n    if (statusCode === RedirectStatusCode.PermanentRedirect) {\n      this.setHeader('Refresh', `0;url=${destination}`)\n    }\n\n    return this\n  }\n}\n"], "names": ["RedirectStatusCode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseNextRequest", "constructor", "method", "url", "body", "cookies", "_cookies", "headers", "BaseNextResponse", "destination", "redirect", "statusCode", "<PERSON><PERSON><PERSON><PERSON>", "PermanentRedirect"], "mappings": ";;;;AAGA,SAASA,kBAAkB,QAAQ,+CAA8C;AAEjF,SAASC,eAAe,QAAQ,iCAAgC;;;AAsBzD,MAAeC;IAKpBC,YACSC,MAAc,EACdC,GAAW,EACXC,IAAU,CACjB;aAHOF,MAAAA,GAAAA;aACAC,GAAAA,GAAAA;aACAC,IAAAA,GAAAA;IACN;IAEH,qDAAqD;IAErD,IAAWC,UAAU;QACnB,IAAI,IAAI,CAACC,QAAQ,EAAE,OAAO,IAAI,CAACA,QAAQ;QACvC,OAAQ,IAAI,CAACA,QAAQ,gMAAGP,kBAAAA,EAAgB,IAAI,CAACQ,OAAO;IACtD;AACF;AAEO,MAAeC;IAKpBP,YAAmBQ,WAAwB,CAAE;aAA1BA,WAAAA,GAAAA;IAA2B;IAqC9C,qDAAqD;IAE9CC,SAASD,WAAmB,EAAEE,UAAkB,EAAE;QACvD,IAAI,CAACC,SAAS,CAAC,YAAYH;QAC3B,IAAI,CAACE,UAAU,GAAGA;QAElB,0DAA0D;QAC1D,qCAAqC;QACrC,IAAIA,yMAAeb,qBAAAA,CAAmBe,iBAAiB,EAAE;YACvD,IAAI,CAACD,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEH,aAAa;QAClD;QAEA,OAAO,IAAI;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1960, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/base-http/node.ts"], "sourcesContent": ["import type { ServerResponse, IncomingMessage } from 'http'\nimport type { Writable, Readable } from 'stream'\n\nimport { SYMBOL_CLEARED_COOKIES } from '../api-utils'\nimport type { NextApiRequestCookies } from '../api-utils'\n\nimport { NEXT_REQUEST_META } from '../request-meta'\nimport type { RequestMeta } from '../request-meta'\n\nimport { BaseNextRequest, BaseNextResponse, type FetchMetric } from './index'\nimport type { OutgoingHttpHeaders } from 'node:http'\n\ntype Req = IncomingMessage & {\n  [NEXT_REQUEST_META]?: RequestMeta\n  cookies?: NextApiRequestCookies\n  fetchMetrics?: FetchMetric[]\n}\n\nexport class NodeNextRequest extends BaseNextRequest<Readable> {\n  public headers = this._req.headers\n  public fetchMetrics: FetchMetric[] | undefined = this._req?.fetchMetrics;\n\n  [NEXT_REQUEST_META]: RequestMeta = this._req[NEXT_REQUEST_META] || {}\n\n  constructor(private _req: Req) {\n    super(_req.method!.toUpperCase(), _req.url!, _req)\n  }\n\n  get originalRequest() {\n    // Need to mimic these changes to the original req object for places where we use it:\n    // render.tsx, api/ssg requests\n    this._req[NEXT_REQUEST_META] = this[NEXT_REQUEST_META]\n    this._req.url = this.url\n    this._req.cookies = this.cookies\n    return this._req\n  }\n\n  set originalRequest(value: Req) {\n    this._req = value\n  }\n\n  private streaming = false\n\n  /**\n   * Returns the request body as a Web Readable Stream. The body here can only\n   * be read once as the body will start flowing as soon as the data handler\n   * is attached.\n   *\n   * @internal\n   */\n  public stream() {\n    if (this.streaming) {\n      throw new Error(\n        'Invariant: NodeNextRequest.stream() can only be called once'\n      )\n    }\n    this.streaming = true\n\n    return new ReadableStream({\n      start: (controller) => {\n        this._req.on('data', (chunk) => {\n          controller.enqueue(new Uint8Array(chunk))\n        })\n        this._req.on('end', () => {\n          controller.close()\n        })\n        this._req.on('error', (err) => {\n          controller.error(err)\n        })\n      },\n    })\n  }\n}\n\nexport class NodeNextResponse extends BaseNextResponse<Writable> {\n  private textBody: string | undefined = undefined\n\n  public [SYMBOL_CLEARED_COOKIES]?: boolean\n\n  get originalResponse() {\n    if (SYMBOL_CLEARED_COOKIES in this) {\n      this._res[SYMBOL_CLEARED_COOKIES] = this[SYMBOL_CLEARED_COOKIES]\n    }\n\n    return this._res\n  }\n\n  constructor(\n    private _res: ServerResponse & { [SYMBOL_CLEARED_COOKIES]?: boolean }\n  ) {\n    super(_res)\n  }\n\n  get sent() {\n    return this._res.finished || this._res.headersSent\n  }\n\n  get statusCode() {\n    return this._res.statusCode\n  }\n\n  set statusCode(value: number) {\n    this._res.statusCode = value\n  }\n\n  get statusMessage() {\n    return this._res.statusMessage\n  }\n\n  set statusMessage(value: string) {\n    this._res.statusMessage = value\n  }\n\n  setHeader(name: string, value: string | string[]): this {\n    this._res.setHeader(name, value)\n    return this\n  }\n\n  removeHeader(name: string): this {\n    this._res.removeHeader(name)\n    return this\n  }\n\n  getHeaderValues(name: string): string[] | undefined {\n    const values = this._res.getHeader(name)\n\n    if (values === undefined) return undefined\n\n    return (Array.isArray(values) ? values : [values]).map((value) =>\n      value.toString()\n    )\n  }\n\n  hasHeader(name: string): boolean {\n    return this._res.hasHeader(name)\n  }\n\n  getHeader(name: string): string | undefined {\n    const values = this.getHeaderValues(name)\n    return Array.isArray(values) ? values.join(',') : undefined\n  }\n\n  getHeaders(): OutgoingHttpHeaders {\n    return this._res.getHeaders()\n  }\n\n  appendHeader(name: string, value: string): this {\n    const currentValues = this.getHeaderValues(name) ?? []\n\n    if (!currentValues.includes(value)) {\n      this._res.setHeader(name, [...currentValues, value])\n    }\n\n    return this\n  }\n\n  body(value: string) {\n    this.textBody = value\n    return this\n  }\n\n  send() {\n    this._res.end(this.textBody)\n  }\n\n  public onClose(callback: () => void) {\n    this.originalResponse.on('close', callback)\n  }\n}\n"], "names": ["SYMBOL_CLEARED_COOKIES", "NEXT_REQUEST_META", "BaseNextRequest", "BaseNextResponse", "NodeNextRequest", "constructor", "_req", "method", "toUpperCase", "url", "headers", "fetchMetrics", "streaming", "originalRequest", "cookies", "value", "stream", "Error", "ReadableStream", "start", "controller", "on", "chunk", "enqueue", "Uint8Array", "close", "err", "error", "NodeNextResponse", "originalResponse", "_res", "textBody", "undefined", "sent", "finished", "headersSent", "statusCode", "statusMessage", "<PERSON><PERSON><PERSON><PERSON>", "name", "removeHeader", "getHeader<PERSON><PERSON>ues", "values", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "map", "toString", "<PERSON><PERSON><PERSON><PERSON>", "join", "getHeaders", "append<PERSON><PERSON>er", "currentV<PERSON>ues", "includes", "body", "send", "end", "onClose", "callback"], "mappings": ";;;;AAGA,SAASA,sBAAsB,QAAQ,eAAc;AAGrD,SAASC,iBAAiB,QAAQ,kBAAiB;AAGnD,SAASC,eAAe,EAAEC,gBAAgB,QAA0B,UAAS;;;;;AAStE,MAAMC,+LAAwBF,kBAAAA;uBAIlCD,sLAAAA,oBAAAA,CAAAA;IAEDI,YAAoBC,IAAS,CAAE;YAJkB;QAK/C,KAAK,CAACA,KAAKC,MAAM,CAAEC,WAAW,IAAIF,KAAKG,GAAG,EAAGH,OAAAA,IAAAA,CAD3BA,IAAAA,GAAAA,MAAAA,IAAAA,CALbI,OAAAA,GAAU,IAAI,CAACJ,IAAI,CAACI,OAAO,EAAA,IAAA,CAC3BC,YAAAA,GAAAA,CAA0C,aAAA,IAAI,CAACL,IAAI,KAAA,OAAA,KAAA,IAAT,WAAWK,YAAY,EAAA,IAExE,CAACV,mBAAkB,GAAgB,IAAI,CAACK,IAAI,kKAACL,oBAAAA,CAAkB,IAAI,CAAC,GAAA,IAAA,CAmB5DW,SAAAA,GAAY;IAfpB;IAEA,IAAIC,kBAAkB;QACpB,qFAAqF;QACrF,+BAA+B;QAC/B,IAAI,CAACP,IAAI,kKAACL,oBAAAA,CAAkB,GAAG,IAAI,kKAACA,oBAAAA,CAAkB;QACtD,IAAI,CAACK,IAAI,CAACG,GAAG,GAAG,IAAI,CAACA,GAAG;QACxB,IAAI,CAACH,IAAI,CAACQ,OAAO,GAAG,IAAI,CAACA,OAAO;QAChC,OAAO,IAAI,CAACR,IAAI;IAClB;IAEA,IAAIO,gBAAgBE,KAAU,EAAE;QAC9B,IAAI,CAACT,IAAI,GAAGS;IACd;IAIA;;;;;;GAMC,GACMC,SAAS;QACd,IAAI,IAAI,CAACJ,SAAS,EAAE;YAClB,MAAM,OAAA,cAEL,CAFK,IAAIK,MACR,gEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAI,CAACL,SAAS,GAAG;QAEjB,OAAO,IAAIM,eAAe;YACxBC,OAAO,CAACC;gBACN,IAAI,CAACd,IAAI,CAACe,EAAE,CAAC,QAAQ,CAACC;oBACpBF,WAAWG,OAAO,CAAC,IAAIC,WAAWF;gBACpC;gBACA,IAAI,CAAChB,IAAI,CAACe,EAAE,CAAC,OAAO;oBAClBD,WAAWK,KAAK;gBAClB;gBACA,IAAI,CAACnB,IAAI,CAACe,EAAE,CAAC,SAAS,CAACK;oBACrBN,WAAWO,KAAK,CAACD;gBACnB;YACF;QACF;IACF;AACF;AAEO,MAAME,gMAAyBzB,mBAAAA;IAKpC,IAAI0B,mBAAmB;QACrB,2KAAI7B,yBAAAA,IAA0B,IAAI,EAAE;YAClC,IAAI,CAAC8B,IAAI,wKAAC9B,yBAAAA,CAAuB,GAAG,IAAI,wKAACA,yBAAAA,CAAuB;QAClE;QAEA,OAAO,IAAI,CAAC8B,IAAI;IAClB;IAEAzB,YACUyB,IAA6D,CACrE;QACA,KAAK,CAACA,OAAAA,IAAAA,CAFEA,IAAAA,GAAAA,MAAAA,IAAAA,CAbFC,QAAAA,GAA+BC;IAgBvC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACH,IAAI,CAACI,QAAQ,IAAI,IAAI,CAACJ,IAAI,CAACK,WAAW;IACpD;IAEA,IAAIC,aAAa;QACf,OAAO,IAAI,CAACN,IAAI,CAACM,UAAU;IAC7B;IAEA,IAAIA,WAAWrB,KAAa,EAAE;QAC5B,IAAI,CAACe,IAAI,CAACM,UAAU,GAAGrB;IACzB;IAEA,IAAIsB,gBAAgB;QAClB,OAAO,IAAI,CAACP,IAAI,CAACO,aAAa;IAChC;IAEA,IAAIA,cAActB,KAAa,EAAE;QAC/B,IAAI,CAACe,IAAI,CAACO,aAAa,GAAGtB;IAC5B;IAEAuB,UAAUC,IAAY,EAAExB,KAAwB,EAAQ;QACtD,IAAI,CAACe,IAAI,CAACQ,SAAS,CAACC,MAAMxB;QAC1B,OAAO,IAAI;IACb;IAEAyB,aAAaD,IAAY,EAAQ;QAC/B,IAAI,CAACT,IAAI,CAACU,YAAY,CAACD;QACvB,OAAO,IAAI;IACb;IAEAE,gBAAgBF,IAAY,EAAwB;QAClD,MAAMG,SAAS,IAAI,CAACZ,IAAI,CAACa,SAAS,CAACJ;QAEnC,IAAIG,WAAWV,WAAW,OAAOA;QAEjC,OAAQY,CAAAA,MAAMC,OAAO,CAACH,UAAUA,SAAS;YAACA;SAAM,EAAGI,GAAG,CAAC,CAAC/B,QACtDA,MAAMgC,QAAQ;IAElB;IAEAC,UAAUT,IAAY,EAAW;QAC/B,OAAO,IAAI,CAACT,IAAI,CAACkB,SAAS,CAACT;IAC7B;IAEAI,UAAUJ,IAAY,EAAsB;QAC1C,MAAMG,SAAS,IAAI,CAACD,eAAe,CAACF;QACpC,OAAOK,MAAMC,OAAO,CAACH,UAAUA,OAAOO,IAAI,CAAC,OAAOjB;IACpD;IAEAkB,aAAkC;QAChC,OAAO,IAAI,CAACpB,IAAI,CAACoB,UAAU;IAC7B;IAEAC,aAAaZ,IAAY,EAAExB,KAAa,EAAQ;QAC9C,MAAMqC,gBAAgB,IAAI,CAACX,eAAe,CAACF,SAAS,EAAE;QAEtD,IAAI,CAACa,cAAcC,QAAQ,CAACtC,QAAQ;YAClC,IAAI,CAACe,IAAI,CAACQ,SAAS,CAACC,MAAM;mBAAIa;gBAAerC;aAAM;QACrD;QAEA,OAAO,IAAI;IACb;IAEAuC,KAAKvC,KAAa,EAAE;QAClB,IAAI,CAACgB,QAAQ,GAAGhB;QAChB,OAAO,IAAI;IACb;IAEAwC,OAAO;QACL,IAAI,CAACzB,IAAI,CAAC0B,GAAG,CAAC,IAAI,CAACzB,QAAQ;IAC7B;IAEO0B,QAAQC,QAAoB,EAAE;QACnC,IAAI,CAAC7B,gBAAgB,CAACR,EAAE,CAAC,SAASqC;IACpC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2094, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/lib/experimental/ppr.ts"], "sourcesContent": ["/**\n * If set to `incremental`, only those leaf pages that export\n * `experimental_ppr = true` will have partial prerendering enabled. If any\n * page exports this value as `false` or does not export it at all will not\n * have partial prerendering enabled. If set to a boolean, the options for\n * `experimental_ppr` will be ignored.\n */\n\nexport type ExperimentalPPRConfig = boolean | 'incremental'\n\n/**\n * Returns true if partial prerendering is enabled for the application. It does\n * not tell you if a given route has PPR enabled, as that requires analysis of\n * the route's configuration.\n *\n * @see {@link checkIsRoutePPREnabled} - for checking if a specific route has PPR enabled.\n */\nexport function checkIsAppPPREnabled(\n  config: ExperimentalPPRConfig | undefined\n): boolean {\n  // If the config is undefined, partial prerendering is disabled.\n  if (typeof config === 'undefined') return false\n\n  // If the config is a boolean, use it directly.\n  if (typeof config === 'boolean') return config\n\n  // If the config is a string, it must be 'incremental' to enable partial\n  // prerendering.\n  if (config === 'incremental') return true\n\n  return false\n}\n\n/**\n * Returns true if partial prerendering is supported for the current page with\n * the provided app configuration. If the application doesn't have partial\n * prerendering enabled, this function will always return false. If you want to\n * check if the application has partial prerendering enabled\n *\n * @see {@link checkIsAppPPREnabled} for checking if the application has PPR enabled.\n */\nexport function checkIsRoutePPREnabled(\n  config: ExperimentalPPRConfig | undefined,\n  appConfig: {\n    experimental_ppr?: boolean\n  }\n): boolean {\n  // If the config is undefined, partial prerendering is disabled.\n  if (typeof config === 'undefined') return false\n\n  // If the config is a boolean, use it directly.\n  if (typeof config === 'boolean') return config\n\n  // If the config is a string, it must be 'incremental' to enable partial\n  // prerendering.\n  if (config === 'incremental' && appConfig.experimental_ppr === true) {\n    return true\n  }\n\n  return false\n}\n"], "names": ["checkIsAppPPREnabled", "config", "checkIsRoutePPREnabled", "appConfig", "experimental_ppr"], "mappings": "AAAA;;;;;;CAMC,GAID;;;;;;CAMC,GACD;;;;AAAO,SAASA,qBACdC,MAAyC;IAEzC,gEAAgE;IAChE,IAAI,OAAOA,WAAW,aAAa,OAAO;IAE1C,+CAA+C;IAC/C,IAAI,OAAOA,WAAW,WAAW,OAAOA;IAExC,wEAAwE;IACxE,gBAAgB;IAChB,IAAIA,WAAW,eAAe,OAAO;IAErC,OAAO;AACT;AAUO,SAASC,uBACdD,MAAyC,EACzCE,SAEC;IAED,gEAAgE;IAChE,IAAI,OAAOF,WAAW,aAAa,OAAO;IAE1C,+CAA+C;IAC/C,IAAI,OAAOA,WAAW,WAAW,OAAOA;IAExC,wEAAwE;IACxE,gBAAgB;IAChB,IAAIA,WAAW,iBAAiBE,UAAUC,gBAAgB,KAAK,MAAM;QACnE,OAAO;IACT;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2136, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["WEB_VITALS", "execOnce", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "isAbsoluteUrl", "url", "test", "getLocationOrigin", "protocol", "hostname", "port", "window", "location", "getURL", "href", "origin", "substring", "length", "getDisplayName", "Component", "displayName", "name", "isResSent", "res", "finished", "headersSent", "normalizeRepeatedSlashes", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "loadGetInitialProps", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "SP", "performance", "ST", "every", "method", "DecodeError", "NormalizeError", "PageNotFoundError", "constructor", "page", "code", "MissingStaticPage", "MiddlewareNotFoundError", "stringifyError", "error", "JSON", "stringify", "stack"], "mappings": "AAwCA;;;CAGC,GACD;;;;;;;;;;;;;;;;;;;AAAO,MAAMA,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO,CAAS;AAsQvE,SAASC,SACdC,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMC,gBAAgB,CAACC,MAAgBF,mBAAmBG,IAAI,CAACD,KAAI;AAEnE,SAASE;IACd,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASG;IACd,MAAM,EAAEC,IAAI,EAAE,GAAGH,OAAOC,QAAQ;IAChC,MAAMG,SAASR;IACf,OAAOO,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASC,eAAkBC,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAASC,UAAUC,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASC,yBAAyBrB,GAAW;IAClD,MAAMsB,WAAWtB,IAAIuB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAeC,oBAIpBC,GAAgC,EAAEC,GAAM;IACxC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;YACrCJ;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAGvB,eAClBgB,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMlB,MAAMY,IAAIZ,GAAG,IAAKY,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACZ,GAAG;IAE9C,IAAI,CAACW,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIhB,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLwB,WAAW,MAAMV,oBAAoBE,IAAIhB,SAAS,EAAEgB,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIZ,OAAOD,UAAUC,MAAM;QACzB,OAAOqB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAGvB,eAClBgB,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAO3B,MAAM,KAAK,KAAK,CAACkB,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAE9B,eACDgB,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMK,KAAK,OAAOC,gBAAgB,YAAW;AAC7C,MAAMC,KACXF,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWG,KAAK,CACtD,CAACC,SAAW,OAAOH,WAAW,CAACG,OAAO,KAAK,YAC5C;AAEI,MAAMC,oBAAoBZ;AAAO;AACjC,MAAMa,uBAAuBb;AAAO;AACpC,MAAMc,0BAA0Bd;IAGrCe,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAACtC,IAAI,GAAG;QACZ,IAAI,CAACoB,OAAO,GAAI,kCAA+BiB;IACjD;AACF;AAEO,MAAME,0BAA0BlB;IACrCe,YAAYC,IAAY,EAAEjB,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCiB,OAAK,MAAGjB;IACjE;AACF;AAEO,MAAMoB,gCAAgCnB;IAE3Ce,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAAClB,OAAO,GAAI;IAClB;AACF;AAWO,SAASqB,eAAeC,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAExB,SAASsB,MAAMtB,OAAO;QAAEyB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2289, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/shared/lib/router/utils/route-matcher.ts"], "sourcesContent": ["import type { Group } from './route-regex'\nimport { DecodeError } from '../../utils'\nimport type { Params } from '../../../../server/request/params'\n\nexport interface RouteMatchFn {\n  (pathname: string): false | Params\n}\n\ntype RouteMatcherOptions = {\n  // We only use the exec method of the RegExp object. This helps us avoid using\n  // type assertions that the passed in properties are of the correct type.\n  re: Pick<RegExp, 'exec'>\n  groups: Record<string, Group>\n}\n\nexport function getRouteMatcher({\n  re,\n  groups,\n}: RouteMatcherOptions): RouteMatchFn {\n  return (pathname: string) => {\n    const routeMatch = re.exec(pathname)\n    if (!routeMatch) return false\n\n    const decode = (param: string) => {\n      try {\n        return decodeURIComponent(param)\n      } catch {\n        throw new DecodeError('failed to decode param')\n      }\n    }\n\n    const params: Params = {}\n    for (const [key, group] of Object.entries(groups)) {\n      const match = routeMatch[group.pos]\n      if (match !== undefined) {\n        if (group.repeat) {\n          params[key] = match.split('/').map((entry) => decode(entry))\n        } else {\n          params[key] = decode(match)\n        }\n      }\n    }\n\n    return params\n  }\n}\n"], "names": ["DecodeError", "getRouteMatcher", "re", "groups", "pathname", "routeMatch", "exec", "decode", "param", "decodeURIComponent", "params", "key", "group", "Object", "entries", "match", "pos", "undefined", "repeat", "split", "map", "entry"], "mappings": ";;;AACA,SAASA,WAAW,QAAQ,cAAa;;AAclC,SAASC,gBAAgB,KAGV;IAHU,IAAA,EAC9BC,EAAE,EACFC,MAAM,EACc,GAHU;IAI9B,OAAO,CAACC;QACN,MAAMC,aAAaH,GAAGI,IAAI,CAACF;QAC3B,IAAI,CAACC,YAAY,OAAO;QAExB,MAAME,SAAS,CAACC;YACd,IAAI;gBACF,OAAOC,mBAAmBD;YAC5B,EAAE,OAAA,GAAM;gBACN,MAAM,OAAA,cAAyC,CAAzC,kKAAIR,cAAAA,CAAY,2BAAhB,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwC;YAChD;QACF;QAEA,MAAMU,SAAiB,CAAC;QACxB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACX,QAAS;YACjD,MAAMY,QAAQV,UAAU,CAACO,MAAMI,GAAG,CAAC;YACnC,IAAID,UAAUE,WAAW;gBACvB,IAAIL,MAAMM,MAAM,EAAE;oBAChBR,MAAM,CAACC,IAAI,GAAGI,MAAMI,KAAK,CAAC,KAAKC,GAAG,CAAC,CAACC,QAAUd,OAAOc;gBACvD,OAAO;oBACLX,MAAM,CAACC,IAAI,GAAGJ,OAAOQ;gBACvB;YACF;QACF;QAEA,OAAOL;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2328, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/shared/lib/page-path/ensure-leading-slash.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n"], "names": ["ensureLeadingSlash", "path", "startsWith"], "mappings": "AAAA;;;CAGC,GACD;;;AAAO,SAASA,mBAAmBC,IAAY;IAC7C,OAAOA,KAAKC,UAAU,CAAC,OAAOD,OAAQ,MAAGA;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2341, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/shared/lib/router/utils/app-paths.ts"], "sourcesContent": ["import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n"], "names": ["ensureLeadingSlash", "isGroupSegment", "normalizeAppPath", "route", "split", "reduce", "pathname", "segment", "index", "segments", "length", "normalizeRscURL", "url", "replace"], "mappings": ";;;;AAAA,SAASA,kBAAkB,QAAQ,uCAAsC;AACzE,SAASC,cAAc,QAAQ,gBAAe;;;AAqBvC,SAASC,iBAAiBC,KAAa;IAC5C,8MAAOH,qBAAAA,EACLG,MAAMC,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,UAAUC,SAASC,OAAOC;QACjD,8BAA8B;QAC9B,IAAI,CAACF,SAAS;YACZ,OAAOD;QACT;QAEA,sBAAsB;QACtB,wKAAIL,iBAAAA,EAAeM,UAAU;YAC3B,OAAOD;QACT;QAEA,iCAAiC;QACjC,IAAIC,OAAO,CAAC,EAAE,KAAK,KAAK;YACtB,OAAOD;QACT;QAEA,uDAAuD;QACvD,IACGC,CAAAA,YAAY,UAAUA,YAAY,OAAM,KACzCC,UAAUC,SAASC,MAAM,GAAG,GAC5B;YACA,OAAOJ;QACT;QAEA,OAAUA,WAAS,MAAGC;IACxB,GAAG;AAEP;AAMO,SAASI,gBAAgBC,GAAW;IACzC,OAAOA,IAAIC,OAAO,CAChB,eACA,AACA,8BAD8B;AAGlC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2377, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/shared/lib/router/utils/interception-routes.ts"], "sourcesContent": ["import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n"], "names": ["normalizeAppPath", "INTERCEPTION_ROUTE_MARKERS", "isInterceptionRouteAppPath", "path", "split", "find", "segment", "m", "startsWith", "undefined", "extractInterceptionRouteInformation", "interceptingRoute", "marker", "interceptedRoute", "Error", "slice", "concat", "join", "splitInterceptingRoute", "length"], "mappings": ";;;;;AAAA,SAASA,gBAAgB,QAAQ,cAAa;;AAGvC,MAAMC,6BAA6B;IACxC;IACA;IACA;IACA;CACD,CAAS;AAEH,SAASC,2BAA2BC,IAAY;IACrD,wCAAwC;IACxC,OACEA,KACGC,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UACLL,2BAA2BI,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD,SACtDE;AAEZ;AAEO,SAASC,oCAAoCP,IAAY;IAC9D,IAAIQ,mBACFC,QACAC;IAEF,KAAK,MAAMP,WAAWH,KAAKC,KAAK,CAAC,KAAM;QACrCQ,SAASX,2BAA2BI,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD;QACnE,IAAIK,QAAQ;;YACT,CAACD,mBAAmBE,iBAAiB,GAAGV,KAAKC,KAAK,CAACQ,QAAQ;YAC5D;QACF;IACF;IAEA,IAAI,CAACD,qBAAqB,CAACC,UAAU,CAACC,kBAAkB;QACtD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,iCAA8BX,OAAK,sFADhC,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAQ,gNAAoBX,mBAAAA,EAAiBW,mBAAmB,iDAAiD;;IAEzG,OAAQC;QACN,KAAK;YACH,oIAAoI;YACpI,IAAID,sBAAsB,KAAK;gBAC7BE,mBAAoB,MAAGA;YACzB,OAAO;gBACLA,mBAAmBF,oBAAoB,MAAME;YAC/C;YACA;QACF,KAAK;YACH,uHAAuH;YACvH,IAAIF,sBAAsB,KAAK;gBAC7B,MAAM,OAAA,cAEL,CAFK,IAAIG,MACP,iCAA8BX,OAAK,iEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAU,mBAAmBF,kBAChBP,KAAK,CAAC,KACNW,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACH,kBACPI,IAAI,CAAC;YACR;QACF,KAAK;YACH,kIAAkI;YAClIJ,mBAAmB,MAAMA;YACzB;QACF,KAAK;YACH,iIAAiI;YAEjI,MAAMK,yBAAyBP,kBAAkBP,KAAK,CAAC;YACvD,IAAIc,uBAAuBC,MAAM,IAAI,GAAG;gBACtC,MAAM,OAAA,cAEL,CAFK,IAAIL,MACP,iCAA8BX,OAAK,oEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAU,mBAAmBK,uBAChBH,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACH,kBACPI,IAAI,CAAC;YACR;QACF;YACE,MAAM,OAAA,cAAyC,CAAzC,IAAIH,MAAM,iCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwC;IAClD;IAEA,OAAO;QAAEH;QAAmBE;IAAiB;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2465, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/shared/lib/escape-regexp.ts"], "sourcesContent": ["// regexp is based on https://github.com/sindresorhus/escape-string-regexp\nconst reHasRegExp = /[|\\\\{}()[\\]^$+*?.-]/\nconst reReplaceRegExp = /[|\\\\{}()[\\]^$+*?.-]/g\n\nexport function escapeStringRegexp(str: string) {\n  // see also: https://github.com/lodash/lodash/blob/2da024c3b4f9947a48517639de7560457cd4ec6c/escapeRegExp.js#L23\n  if (reHasRegExp.test(str)) {\n    return str.replace(reReplaceRegExp, '\\\\$&')\n  }\n  return str\n}\n"], "names": ["reHasRegExp", "reReplaceRegExp", "escapeStringRegexp", "str", "test", "replace"], "mappings": "AAAA,0EAA0E;;;;AAC1E,MAAMA,cAAc;AACpB,MAAMC,kBAAkB;AAEjB,SAASC,mBAAmBC,GAAW;IAC5C,+GAA+G;IAC/G,IAAIH,YAAYI,IAAI,CAACD,MAAM;QACzB,OAAOA,IAAIE,OAAO,CAACJ,iBAAiB;IACtC;IACA,OAAOE;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2482, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/shared/lib/router/utils/route-regex.ts"], "sourcesContent": ["import {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../../../lib/constants'\nimport { INTERCEPTION_ROUTE_MARKERS } from './interception-routes'\nimport { escapeStringRegexp } from '../../escape-regexp'\nimport { removeTrailingSlash } from './remove-trailing-slash'\n\nexport interface Group {\n  pos: number\n  repeat: boolean\n  optional: boolean\n}\n\nexport interface RouteRegex {\n  groups: { [groupName: string]: Group }\n  re: RegExp\n}\n\ntype GetNamedRouteRegexOptions = {\n  /**\n   * Whether to prefix the route keys with the NEXT_INTERCEPTION_MARKER_PREFIX\n   * or NEXT_QUERY_PARAM_PREFIX. This is only relevant when creating the\n   * routes-manifest during the build.\n   */\n  prefixRouteKeys: boolean\n\n  /**\n   * Whether to include the suffix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n\n  /**\n   * Whether to backtrack duplicate keys. This is only relevant when creating\n   * the routes-manifest during the build.\n   */\n  backreferenceDuplicateKeys?: boolean\n}\n\ntype GetRouteRegexOptions = {\n  /**\n   * Whether to include extra parts in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   * of adding this option.\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n}\n\n/**\n * Regular expression pattern used to match route parameters.\n * Matches both single parameters and parameter groups.\n * Examples:\n *   - `[[...slug]]` matches parameter group with key 'slug', repeat: true, optional: true\n *   - `[...slug]` matches parameter group with key 'slug', repeat: true, optional: false\n *   - `[[foo]]` matches parameter with key 'foo', repeat: false, optional: true\n *   - `[bar]` matches parameter with key 'bar', repeat: false, optional: false\n */\nconst PARAMETER_PATTERN = /^([^[]*)\\[((?:\\[[^\\]]*\\])|[^\\]]+)\\](.*)$/\n\n/**\n * Parses a given parameter from a route to a data structure that can be used\n * to generate the parametrized route.\n * Examples:\n *   - `[[...slug]]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[[foo]]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `[bar]` -> `{ key: 'bar', repeat: false, optional: false }`\n *   - `fizz` -> `{ key: 'fizz', repeat: false, optional: false }`\n * @param param - The parameter to parse.\n * @returns The parsed parameter as a data structure.\n */\nexport function parseParameter(param: string) {\n  const match = param.match(PARAMETER_PATTERN)\n\n  if (!match) {\n    return parseMatchedParameter(param)\n  }\n\n  return parseMatchedParameter(match[2])\n}\n\n/**\n * Parses a matched parameter from the PARAMETER_PATTERN regex to a data structure that can be used\n * to generate the parametrized route.\n * Examples:\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `...slug` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[foo]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `bar` -> `{ key: 'bar', repeat: false, optional: false }`\n * @param param - The matched parameter to parse.\n * @returns The parsed parameter as a data structure.\n */\nfunction parseMatchedParameter(param: string) {\n  const optional = param.startsWith('[') && param.endsWith(']')\n  if (optional) {\n    param = param.slice(1, -1)\n  }\n  const repeat = param.startsWith('...')\n  if (repeat) {\n    param = param.slice(3)\n  }\n  return { key: param, repeat, optional }\n}\n\nfunction getParametrizedRoute(\n  route: string,\n  includeSuffix: boolean,\n  includePrefix: boolean\n) {\n  const groups: { [groupName: string]: Group } = {}\n  let groupIndex = 1\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const markerMatch = INTERCEPTION_ROUTE_MARKERS.find((m) =>\n      segment.startsWith(m)\n    )\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (markerMatch && paramMatches && paramMatches[2]) {\n      const { key, optional, repeat } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n      segments.push(`/${escapeStringRegexp(markerMatch)}([^/]+?)`)\n    } else if (paramMatches && paramMatches[2]) {\n      const { key, repeat, optional } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = repeat ? (optional ? '(?:/(.+?))?' : '/(.+?)') : '/([^/]+?)'\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    parameterizedRoute: segments.join(''),\n    groups,\n  }\n}\n\n/**\n * From a normalized route this function generates a regular expression and\n * a corresponding groups object intended to be used to store matching groups\n * from the regular expression.\n */\nexport function getRouteRegex(\n  normalizedRoute: string,\n  {\n    includeSuffix = false,\n    includePrefix = false,\n    excludeOptionalTrailingSlash = false,\n  }: GetRouteRegexOptions = {}\n): RouteRegex {\n  const { parameterizedRoute, groups } = getParametrizedRoute(\n    normalizedRoute,\n    includeSuffix,\n    includePrefix\n  )\n\n  let re = parameterizedRoute\n  if (!excludeOptionalTrailingSlash) {\n    re += '(?:/)?'\n  }\n\n  return {\n    re: new RegExp(`^${re}$`),\n    groups: groups,\n  }\n}\n\n/**\n * Builds a function to generate a minimal routeKey using only a-z and minimal\n * number of characters.\n */\nfunction buildGetSafeRouteKey() {\n  let i = 0\n\n  return () => {\n    let routeKey = ''\n    let j = ++i\n    while (j > 0) {\n      routeKey += String.fromCharCode(97 + ((j - 1) % 26))\n      j = Math.floor((j - 1) / 26)\n    }\n    return routeKey\n  }\n}\n\nfunction getSafeKeyFromSegment({\n  interceptionMarker,\n  getSafeRouteKey,\n  segment,\n  routeKeys,\n  keyPrefix,\n  backreferenceDuplicateKeys,\n}: {\n  interceptionMarker?: string\n  getSafeRouteKey: () => string\n  segment: string\n  routeKeys: Record<string, string>\n  keyPrefix?: string\n  backreferenceDuplicateKeys: boolean\n}) {\n  const { key, optional, repeat } = parseMatchedParameter(segment)\n\n  // replace any non-word characters since they can break\n  // the named regex\n  let cleanedKey = key.replace(/\\W/g, '')\n\n  if (keyPrefix) {\n    cleanedKey = `${keyPrefix}${cleanedKey}`\n  }\n  let invalidKey = false\n\n  // check if the key is still invalid and fallback to using a known\n  // safe key\n  if (cleanedKey.length === 0 || cleanedKey.length > 30) {\n    invalidKey = true\n  }\n  if (!isNaN(parseInt(cleanedKey.slice(0, 1)))) {\n    invalidKey = true\n  }\n\n  if (invalidKey) {\n    cleanedKey = getSafeRouteKey()\n  }\n\n  const duplicateKey = cleanedKey in routeKeys\n\n  if (keyPrefix) {\n    routeKeys[cleanedKey] = `${keyPrefix}${key}`\n  } else {\n    routeKeys[cleanedKey] = key\n  }\n\n  // if the segment has an interception marker, make sure that's part of the regex pattern\n  // this is to ensure that the route with the interception marker doesn't incorrectly match\n  // the non-intercepted route (ie /app/(.)[username] should not match /app/[username])\n  const interceptionPrefix = interceptionMarker\n    ? escapeStringRegexp(interceptionMarker)\n    : ''\n\n  let pattern: string\n  if (duplicateKey && backreferenceDuplicateKeys) {\n    // Use a backreference to the key to ensure that the key is the same value\n    // in each of the placeholders.\n    pattern = `\\\\k<${cleanedKey}>`\n  } else if (repeat) {\n    pattern = `(?<${cleanedKey}>.+?)`\n  } else {\n    pattern = `(?<${cleanedKey}>[^/]+?)`\n  }\n\n  return optional\n    ? `(?:/${interceptionPrefix}${pattern})?`\n    : `/${interceptionPrefix}${pattern}`\n}\n\nfunction getNamedParametrizedRoute(\n  route: string,\n  prefixRouteKeys: boolean,\n  includeSuffix: boolean,\n  includePrefix: boolean,\n  backreferenceDuplicateKeys: boolean\n) {\n  const getSafeRouteKey = buildGetSafeRouteKey()\n  const routeKeys: { [named: string]: string } = {}\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const hasInterceptionMarker = INTERCEPTION_ROUTE_MARKERS.some((m) =>\n      segment.startsWith(m)\n    )\n\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (hasInterceptionMarker && paramMatches && paramMatches[2]) {\n      // If there's an interception marker, add it to the segments.\n      segments.push(\n        getSafeKeyFromSegment({\n          getSafeRouteKey,\n          interceptionMarker: paramMatches[1],\n          segment: paramMatches[2],\n          routeKeys,\n          keyPrefix: prefixRouteKeys\n            ? NEXT_INTERCEPTION_MARKER_PREFIX\n            : undefined,\n          backreferenceDuplicateKeys,\n        })\n      )\n    } else if (paramMatches && paramMatches[2]) {\n      // If there's a prefix, add it to the segments if it's enabled.\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = getSafeKeyFromSegment({\n        getSafeRouteKey,\n        segment: paramMatches[2],\n        routeKeys,\n        keyPrefix: prefixRouteKeys ? NEXT_QUERY_PARAM_PREFIX : undefined,\n        backreferenceDuplicateKeys,\n      })\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    namedParameterizedRoute: segments.join(''),\n    routeKeys,\n  }\n}\n\n/**\n * This function extends `getRouteRegex` generating also a named regexp where\n * each group is named along with a routeKeys object that indexes the assigned\n * named group with its corresponding key. When the routeKeys need to be\n * prefixed to uniquely identify internally the \"prefixRouteKey\" arg should\n * be \"true\" currently this is only the case when creating the routes-manifest\n * during the build\n */\nexport function getNamedRouteRegex(\n  normalizedRoute: string,\n  options: GetNamedRouteRegexOptions\n) {\n  const result = getNamedParametrizedRoute(\n    normalizedRoute,\n    options.prefixRouteKeys,\n    options.includeSuffix ?? false,\n    options.includePrefix ?? false,\n    options.backreferenceDuplicateKeys ?? false\n  )\n\n  let namedRegex = result.namedParameterizedRoute\n  if (!options.excludeOptionalTrailingSlash) {\n    namedRegex += '(?:/)?'\n  }\n\n  return {\n    ...getRouteRegex(normalizedRoute, options),\n    namedRegex: `^${namedRegex}$`,\n    routeKeys: result.routeKeys,\n  }\n}\n\n/**\n * Generates a named regexp.\n * This is intended to be using for build time only.\n */\nexport function getNamedMiddlewareRegex(\n  normalizedRoute: string,\n  options: {\n    catchAll?: boolean\n  }\n) {\n  const { parameterizedRoute } = getParametrizedRoute(\n    normalizedRoute,\n    false,\n    false\n  )\n  const { catchAll = true } = options\n  if (parameterizedRoute === '/') {\n    let catchAllRegex = catchAll ? '.*' : ''\n    return {\n      namedRegex: `^/${catchAllRegex}$`,\n    }\n  }\n\n  const { namedParameterizedRoute } = getNamedParametrizedRoute(\n    normalizedRoute,\n    false,\n    false,\n    false,\n    false\n  )\n  let catchAllGroupedRegex = catchAll ? '(?:(/.*)?)' : ''\n  return {\n    namedRegex: `^${namedParameterizedRoute}${catchAllGroupedRegex}$`,\n  }\n}\n"], "names": ["NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_QUERY_PARAM_PREFIX", "INTERCEPTION_ROUTE_MARKERS", "escapeStringRegexp", "removeTrailingSlash", "PARAMETER_PATTERN", "parseParameter", "param", "match", "parseMatchedParameter", "optional", "startsWith", "endsWith", "slice", "repeat", "key", "getParametrizedRoute", "route", "includeSuffix", "includePrefix", "groups", "groupIndex", "segments", "segment", "split", "markerMatch", "find", "m", "paramMatch<PERSON>", "pos", "push", "s", "substring", "parameterizedRoute", "join", "getRouteRegex", "normalizedRoute", "excludeOptionalTrailingSlash", "re", "RegExp", "buildGetSafeRouteKey", "i", "routeKey", "j", "String", "fromCharCode", "Math", "floor", "getSafeKeyFromSegment", "<PERSON><PERSON><PERSON><PERSON>", "getSafeRouteKey", "routeKeys", "keyPrefix", "backreferenceDuplicateKeys", "<PERSON><PERSON><PERSON>", "replace", "<PERSON><PERSON><PERSON>", "length", "isNaN", "parseInt", "duplicate<PERSON>ey", "interceptionPrefix", "pattern", "getNamedParametrizedRoute", "prefixRouteKeys", "hasInterceptionMarker", "some", "undefined", "namedParameterizedRoute", "getNamedRouteRegex", "options", "result", "namedRegex", "getNamedMiddlewareRegex", "catchAll", "catchAllRegex", "catchAllGroupedRegex"], "mappings": ";;;;;;AAAA,SACEA,+BAA+B,EAC/BC,uBAAuB,QAClB,4BAA2B;AAClC,SAASC,0BAA0B,QAAQ,wBAAuB;AAClE,SAASC,kBAAkB,QAAQ,sBAAqB;AACxD,SAASC,mBAAmB,QAAQ,0BAAyB;;;;;AAyE7D;;;;;;;;CAQC,GACD,MAAMC,oBAAoB;AAcnB,SAASC,eAAeC,KAAa;IAC1C,MAAMC,QAAQD,MAAMC,KAAK,CAACH;IAE1B,IAAI,CAACG,OAAO;QACV,OAAOC,sBAAsBF;IAC/B;IAEA,OAAOE,sBAAsBD,KAAK,CAAC,EAAE;AACvC;AAEA;;;;;;;;;;CAUC,GACD,SAASC,sBAAsBF,KAAa;IAC1C,MAAMG,WAAWH,MAAMI,UAAU,CAAC,QAAQJ,MAAMK,QAAQ,CAAC;IACzD,IAAIF,UAAU;QACZH,QAAQA,MAAMM,KAAK,CAAC,GAAG,CAAC;IAC1B;IACA,MAAMC,SAASP,MAAMI,UAAU,CAAC;IAChC,IAAIG,QAAQ;QACVP,QAAQA,MAAMM,KAAK,CAAC;IACtB;IACA,OAAO;QAAEE,KAAKR;QAAOO;QAAQJ;IAAS;AACxC;AAEA,SAASM,qBACPC,KAAa,EACbC,aAAsB,EACtBC,aAAsB;IAEtB,MAAMC,SAAyC,CAAC;IAChD,IAAIC,aAAa;IAEjB,MAAMC,WAAqB,EAAE;IAC7B,KAAK,MAAMC,sNAAWnB,sBAAAA,EAAoBa,OAAOJ,KAAK,CAAC,GAAGW,KAAK,CAAC,KAAM;QACpE,MAAMC,gNAAcvB,6BAAAA,CAA2BwB,IAAI,CAAC,CAACC,IACnDJ,QAAQZ,UAAU,CAACgB;QAErB,MAAMC,eAAeL,QAAQf,KAAK,CAACH,mBAAmB,uBAAuB;;QAE7E,IAAIoB,eAAeG,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAClD,MAAM,EAAEb,GAAG,EAAEL,QAAQ,EAAEI,MAAM,EAAE,GAAGL,sBAAsBmB,YAAY,CAAC,EAAE;YACvER,MAAM,CAACL,IAAI,GAAG;gBAAEc,KAAKR;gBAAcP;gBAAQJ;YAAS;YACpDY,SAASQ,IAAI,CAAE,mLAAG3B,qBAAAA,EAAmBsB,eAAa;QACpD,OAAO,IAAIG,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC1C,MAAM,EAAEb,GAAG,EAAED,MAAM,EAAEJ,QAAQ,EAAE,GAAGD,sBAAsBmB,YAAY,CAAC,EAAE;YACvER,MAAM,CAACL,IAAI,GAAG;gBAAEc,KAAKR;gBAAcP;gBAAQJ;YAAS;YAEpD,IAAIS,iBAAiBS,YAAY,CAAC,EAAE,EAAE;gBACpCN,SAASQ,IAAI,CAAE,mLAAG3B,qBAAAA,EAAmByB,YAAY,CAAC,EAAE;YACtD;YAEA,IAAIG,IAAIjB,SAAUJ,WAAW,gBAAgB,WAAY;YAEzD,8DAA8D;YAC9D,IAAIS,iBAAiBS,YAAY,CAAC,EAAE,EAAE;gBACpCG,IAAIA,EAAEC,SAAS,CAAC;YAClB;YAEAV,SAASQ,IAAI,CAACC;QAChB,OAAO;YACLT,SAASQ,IAAI,CAAE,mLAAG3B,qBAAAA,EAAmBoB;QACvC;QAEA,+DAA+D;QAC/D,IAAIL,iBAAiBU,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YACpDN,SAASQ,IAAI,EAAC3B,iMAAAA,EAAmByB,YAAY,CAAC,EAAE;QAClD;IACF;IAEA,OAAO;QACLK,oBAAoBX,SAASY,IAAI,CAAC;QAClCd;IACF;AACF;AAOO,SAASe,cACdC,eAAuB,EACvB,KAAA;IAAA,IAAA,EACElB,gBAAgB,KAAK,EACrBC,gBAAgB,KAAK,EACrBkB,+BAA+B,KAAK,EACf,GAJvB,UAAA,KAAA,IAI0B,CAAC,IAJ3B;IAMA,MAAM,EAAEJ,kBAAkB,EAAEb,MAAM,EAAE,GAAGJ,qBACrCoB,iBACAlB,eACAC;IAGF,IAAImB,KAAKL;IACT,IAAI,CAACI,8BAA8B;QACjCC,MAAM;IACR;IAEA,OAAO;QACLA,IAAI,IAAIC,OAAQ,MAAGD,KAAG;QACtBlB,QAAQA;IACV;AACF;AAEA;;;CAGC,GACD,SAASoB;IACP,IAAIC,IAAI;IAER,OAAO;QACL,IAAIC,WAAW;QACf,IAAIC,IAAI,EAAEF;QACV,MAAOE,IAAI,EAAG;YACZD,YAAYE,OAAOC,YAAY,CAAC,KAAOF,CAAAA,IAAI,CAAA,IAAK;YAChDA,IAAIG,KAAKC,KAAK,CAAEJ,CAAAA,IAAI,CAAA,IAAK;QAC3B;QACA,OAAOD;IACT;AACF;AAEA,SAASM,sBAAsB,KAc9B;IAd8B,IAAA,EAC7BC,kBAAkB,EAClBC,eAAe,EACf3B,OAAO,EACP4B,SAAS,EACTC,SAAS,EACTC,0BAA0B,EAQ3B,GAd8B;IAe7B,MAAM,EAAEtC,GAAG,EAAEL,QAAQ,EAAEI,MAAM,EAAE,GAAGL,sBAAsBc;IAExD,uDAAuD;IACvD,kBAAkB;IAClB,IAAI+B,aAAavC,IAAIwC,OAAO,CAAC,OAAO;IAEpC,IAAIH,WAAW;QACbE,aAAc,KAAEF,YAAYE;IAC9B;IACA,IAAIE,aAAa;IAEjB,kEAAkE;IAClE,WAAW;IACX,IAAIF,WAAWG,MAAM,KAAK,KAAKH,WAAWG,MAAM,GAAG,IAAI;QACrDD,aAAa;IACf;IACA,IAAI,CAACE,MAAMC,SAASL,WAAWzC,KAAK,CAAC,GAAG,MAAM;QAC5C2C,aAAa;IACf;IAEA,IAAIA,YAAY;QACdF,aAAaJ;IACf;IAEA,MAAMU,eAAeN,cAAcH;IAEnC,IAAIC,WAAW;QACbD,SAAS,CAACG,WAAW,GAAI,KAAEF,YAAYrC;IACzC,OAAO;QACLoC,SAAS,CAACG,WAAW,GAAGvC;IAC1B;IAEA,wFAAwF;IACxF,0FAA0F;IAC1F,qFAAqF;IACrF,MAAM8C,qBAAqBZ,kMACvB9C,qBAAAA,EAAmB8C,sBACnB;IAEJ,IAAIa;IACJ,IAAIF,gBAAgBP,4BAA4B;QAC9C,0EAA0E;QAC1E,+BAA+B;QAC/BS,UAAW,SAAMR,aAAW;IAC9B,OAAO,IAAIxC,QAAQ;QACjBgD,UAAW,QAAKR,aAAW;IAC7B,OAAO;QACLQ,UAAW,QAAKR,aAAW;IAC7B;IAEA,OAAO5C,WACF,SAAMmD,qBAAqBC,UAAQ,OACnC,MAAGD,qBAAqBC;AAC/B;AAEA,SAASC,0BACP9C,KAAa,EACb+C,eAAwB,EACxB9C,aAAsB,EACtBC,aAAsB,EACtBkC,0BAAmC;IAEnC,MAAMH,kBAAkBV;IACxB,MAAMW,YAAyC,CAAC;IAEhD,MAAM7B,WAAqB,EAAE;IAC7B,KAAK,MAAMC,sNAAWnB,sBAAAA,EAAoBa,OAAOJ,KAAK,CAAC,GAAGW,KAAK,CAAC,KAAM;QACpE,MAAMyC,wBAAwB/D,+NAAAA,CAA2BgE,IAAI,CAAC,CAACvC,IAC7DJ,QAAQZ,UAAU,CAACgB;QAGrB,MAAMC,eAAeL,QAAQf,KAAK,CAACH,mBAAmB,uBAAuB;;QAE7E,IAAI4D,yBAAyBrC,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC5D,6DAA6D;YAC7DN,SAASQ,IAAI,CACXkB,sBAAsB;gBACpBE;gBACAD,oBAAoBrB,YAAY,CAAC,EAAE;gBACnCL,SAASK,YAAY,CAAC,EAAE;gBACxBuB;gBACAC,WAAWY,0KACPhE,kCAAAA,GACAmE;gBACJd;YACF;QAEJ,OAAO,IAAIzB,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC1C,+DAA+D;YAC/D,IAAIT,iBAAiBS,YAAY,CAAC,EAAE,EAAE;gBACpCN,SAASQ,IAAI,CAAE,mLAAG3B,qBAAAA,EAAmByB,YAAY,CAAC,EAAE;YACtD;YAEA,IAAIG,IAAIiB,sBAAsB;gBAC5BE;gBACA3B,SAASK,YAAY,CAAC,EAAE;gBACxBuB;gBACAC,WAAWY,0KAAkB/D,0BAAAA,GAA0BkE;gBACvDd;YACF;YAEA,8DAA8D;YAC9D,IAAIlC,iBAAiBS,YAAY,CAAC,EAAE,EAAE;gBACpCG,IAAIA,EAAEC,SAAS,CAAC;YAClB;YAEAV,SAASQ,IAAI,CAACC;QAChB,OAAO;YACLT,SAASQ,IAAI,CAAE,MAAG3B,kMAAAA,EAAmBoB;QACvC;QAEA,+DAA+D;QAC/D,IAAIL,iBAAiBU,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YACpDN,SAASQ,IAAI,KAAC3B,8LAAAA,EAAmByB,YAAY,CAAC,EAAE;QAClD;IACF;IAEA,OAAO;QACLwC,yBAAyB9C,SAASY,IAAI,CAAC;QACvCiB;IACF;AACF;AAUO,SAASkB,mBACdjC,eAAuB,EACvBkC,OAAkC;QAKhCA,wBACAA,wBACAA;IALF,MAAMC,SAASR,0BACb3B,iBACAkC,QAAQN,eAAe,EACvBM,CAAAA,yBAAAA,QAAQpD,aAAa,KAAA,OAArBoD,yBAAyB,OACzBA,CAAAA,yBAAAA,QAAQnD,aAAa,KAAA,OAArBmD,yBAAyB,OACzBA,CAAAA,sCAAAA,QAAQjB,0BAA0B,KAAA,OAAlCiB,sCAAsC;IAGxC,IAAIE,aAAaD,OAAOH,uBAAuB;IAC/C,IAAI,CAACE,QAAQjC,4BAA4B,EAAE;QACzCmC,cAAc;IAChB;IAEA,OAAO;QACL,GAAGrC,cAAcC,iBAAiBkC,QAAQ;QAC1CE,YAAa,MAAGA,aAAW;QAC3BrB,WAAWoB,OAAOpB,SAAS;IAC7B;AACF;AAMO,SAASsB,wBACdrC,eAAuB,EACvBkC,OAEC;IAED,MAAM,EAAErC,kBAAkB,EAAE,GAAGjB,qBAC7BoB,iBACA,OACA;IAEF,MAAM,EAAEsC,WAAW,IAAI,EAAE,GAAGJ;IAC5B,IAAIrC,uBAAuB,KAAK;QAC9B,IAAI0C,gBAAgBD,WAAW,OAAO;QACtC,OAAO;YACLF,YAAa,OAAIG,gBAAc;QACjC;IACF;IAEA,MAAM,EAAEP,uBAAuB,EAAE,GAAGL,0BAClC3B,iBACA,OACA,OACA,OACA;IAEF,IAAIwC,uBAAuBF,WAAW,eAAe;IACrD,OAAO;QACLF,YAAa,MAAGJ,0BAA0BQ,uBAAqB;IACjE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2732, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/request/fallback-params.ts"], "sourcesContent": ["import { getRouteMatcher } from '../../shared/lib/router/utils/route-matcher'\nimport { getRouteRegex } from '../../shared/lib/router/utils/route-regex'\n\nexport type FallbackRouteParams = ReadonlyMap<string, string>\n\nfunction getParamKeys(page: string) {\n  const pattern = getRouteRegex(page)\n  const matcher = getRouteMatcher(pattern)\n\n  // Get the default list of allowed params.\n  return Object.keys(matcher(page))\n}\n\nexport function getFallbackRouteParams(\n  pageOrKeys: string | readonly string[]\n): FallbackRouteParams | null {\n  let keys: readonly string[]\n  if (typeof pageOrKeys === 'string') {\n    keys = getParamKeys(pageOrKeys)\n  } else {\n    keys = pageOrKeys\n  }\n\n  // If there are no keys, we can return early.\n  if (keys.length === 0) return null\n\n  const params = new Map<string, string>()\n\n  // As we're creating unique keys for each of the dynamic route params, we only\n  // need to generate a unique ID once per request because each of the keys will\n  // be also be unique.\n  const uniqueID = Math.random().toString(16).slice(2)\n\n  for (const key of keys) {\n    params.set(key, `%%drp:${key}:${uniqueID}%%`)\n  }\n\n  return params\n}\n"], "names": ["getRouteMatcher", "getRouteRegex", "get<PERSON>ara<PERSON><PERSON><PERSON><PERSON>", "page", "pattern", "matcher", "Object", "keys", "getFallbackRouteParams", "page<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "params", "Map", "uniqueID", "Math", "random", "toString", "slice", "key", "set"], "mappings": ";;;AAAA,SAASA,eAAe,QAAQ,8CAA6C;AAC7E,SAASC,aAAa,QAAQ,4CAA2C;;;AAIzE,SAASC,aAAaC,IAAY;IAChC,MAAMC,wMAAUH,gBAAAA,EAAcE;IAC9B,MAAME,0MAAUL,kBAAAA,EAAgBI;IAEhC,0CAA0C;IAC1C,OAAOE,OAAOC,IAAI,CAACF,QAAQF;AAC7B;AAEO,SAASK,uBACdC,UAAsC;IAEtC,IAAIF;IACJ,IAAI,OAAOE,eAAe,UAAU;QAClCF,OAAOL,aAAaO;IACtB,OAAO;QACLF,OAAOE;IACT;IAEA,6CAA6C;IAC7C,IAAIF,KAAKG,MAAM,KAAK,GAAG,OAAO;IAE9B,MAAMC,SAAS,IAAIC;IAEnB,8EAA8E;IAC9E,8EAA8E;IAC9E,qBAAqB;IACrB,MAAMC,WAAWC,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,KAAK,CAAC;IAElD,KAAK,MAAMC,OAAOX,KAAM;QACtBI,OAAOQ,GAAG,CAACD,KAAK,CAAC,MAAM,EAAEA,IAAI,CAAC,EAAEL,SAAS,EAAE,CAAC;IAC9C;IAEA,OAAOF;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2768, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/app-render/encryption-utils.ts"], "sourcesContent": ["import type { ActionManifest } from '../../build/webpack/plugins/flight-client-entry-plugin'\nimport type {\n  ClientReferenceManifest,\n  ClientReferenceManifestForRsc,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { workAsyncStorage } from './work-async-storage.external'\n\nlet __next_loaded_action_key: CryptoKey\n\nexport function arrayBufferToString(\n  buffer: ArrayBuffer | Uint8Array<ArrayBufferLike>\n) {\n  const bytes = new Uint8Array(buffer)\n  const len = bytes.byteLength\n\n  // @anonrig: V8 has a limit of 65535 arguments in a function.\n  // For len < 65535, this is faster.\n  // https://github.com/vercel/next.js/pull/56377#pullrequestreview-1656181623\n  if (len < 65535) {\n    return String.fromCharCode.apply(null, bytes as unknown as number[])\n  }\n\n  let binary = ''\n  for (let i = 0; i < len; i++) {\n    binary += String.fromCharCode(bytes[i])\n  }\n  return binary\n}\n\nexport function stringToUint8Array(binary: string) {\n  const len = binary.length\n  const arr = new Uint8Array(len)\n\n  for (let i = 0; i < len; i++) {\n    arr[i] = binary.charCodeAt(i)\n  }\n\n  return arr\n}\n\nexport function encrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.encrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\nexport function decrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.decrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\n// This is a global singleton that is used to encode/decode the action bound args from\n// the closure. This can't be using a AsyncLocalStorage as it might happen on the module\n// level. Since the client reference manifest won't be mutated, let's use a global singleton\n// to keep it.\nconst SERVER_ACTION_MANIFESTS_SINGLETON = Symbol.for(\n  'next.server.action-manifests'\n)\n\nexport function setReferenceManifestsSingleton({\n  page,\n  clientReferenceManifest,\n  serverActionsManifest,\n  serverModuleMap,\n}: {\n  page: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  serverActionsManifest: DeepReadonly<ActionManifest>\n  serverModuleMap: {\n    [id: string]: {\n      id: string\n      chunks: string[]\n      name: string\n    }\n  }\n}) {\n  // @ts-expect-error\n  const clientReferenceManifestsPerPage = globalThis[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ]?.clientReferenceManifestsPerPage as\n    | undefined\n    | DeepReadonly<Record<string, ClientReferenceManifest>>\n\n  // @ts-expect-error\n  globalThis[SERVER_ACTION_MANIFESTS_SINGLETON] = {\n    clientReferenceManifestsPerPage: {\n      ...clientReferenceManifestsPerPage,\n      [normalizeAppPath(page)]: clientReferenceManifest,\n    },\n    serverActionsManifest,\n    serverModuleMap,\n  }\n}\n\nexport function getServerModuleMap() {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverModuleMap: {\n      [id: string]: {\n        id: string\n        chunks: string[]\n        name: string\n      }\n    }\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  return serverActionsManifestSingleton.serverModuleMap\n}\n\nexport function getClientReferenceManifestForRsc(): DeepReadonly<ClientReferenceManifestForRsc> {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    clientReferenceManifestsPerPage: DeepReadonly<\n      Record<string, ClientReferenceManifest>\n    >\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const { clientReferenceManifestsPerPage } = serverActionsManifestSingleton\n  const workStore = workAsyncStorage.getStore()\n\n  if (!workStore) {\n    // If there's no work store defined, we can assume that a client reference\n    // manifest is needed during module evaluation, e.g. to create a server\n    // action using a higher-order function. This might also use client\n    // components which need to be serialized by Flight, and therefore client\n    // references need to be resolvable. To make this work, we're returning a\n    // merged manifest across all pages. This is fine as long as the module IDs\n    // are not page specific, which they are not for Webpack. TODO: Fix this in\n    // Turbopack.\n    return mergeClientReferenceManifests(clientReferenceManifestsPerPage)\n  }\n\n  const clientReferenceManifest =\n    clientReferenceManifestsPerPage[workStore.route]\n\n  if (!clientReferenceManifest) {\n    throw new InvariantError(\n      `Missing Client Reference Manifest for ${workStore.route}.`\n    )\n  }\n\n  return clientReferenceManifest\n}\n\nexport async function getActionEncryptionKey() {\n  if (__next_loaded_action_key) {\n    return __next_loaded_action_key\n  }\n\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverActionsManifest: DeepReadonly<ActionManifest>\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const rawKey =\n    process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY ||\n    serverActionsManifestSingleton.serverActionsManifest.encryptionKey\n\n  if (rawKey === undefined) {\n    throw new InvariantError('Missing encryption key for Server Actions')\n  }\n\n  __next_loaded_action_key = await crypto.subtle.importKey(\n    'raw',\n    stringToUint8Array(atob(rawKey)),\n    'AES-GCM',\n    true,\n    ['encrypt', 'decrypt']\n  )\n\n  return __next_loaded_action_key\n}\n\nfunction mergeClientReferenceManifests(\n  clientReferenceManifestsPerPage: DeepReadonly<\n    Record<string, ClientReferenceManifest>\n  >\n): ClientReferenceManifestForRsc {\n  const clientReferenceManifests = Object.values(\n    clientReferenceManifestsPerPage as Record<string, ClientReferenceManifest>\n  )\n\n  const mergedClientReferenceManifest: ClientReferenceManifestForRsc = {\n    clientModules: {},\n    edgeRscModuleMapping: {},\n    rscModuleMapping: {},\n  }\n\n  for (const clientReferenceManifest of clientReferenceManifests) {\n    mergedClientReferenceManifest.clientModules = {\n      ...mergedClientReferenceManifest.clientModules,\n      ...clientReferenceManifest.clientModules,\n    }\n    mergedClientReferenceManifest.edgeRscModuleMapping = {\n      ...mergedClientReferenceManifest.edgeRscModuleMapping,\n      ...clientReferenceManifest.edgeRscModuleMapping,\n    }\n    mergedClientReferenceManifest.rscModuleMapping = {\n      ...mergedClientReferenceManifest.rscModuleMapping,\n      ...clientReferenceManifest.rscModuleMapping,\n    }\n  }\n\n  return mergedClientReferenceManifest\n}\n"], "names": ["InvariantError", "normalizeAppPath", "workAsyncStorage", "__next_loaded_action_key", "arrayBufferToString", "buffer", "bytes", "Uint8Array", "len", "byteLength", "String", "fromCharCode", "apply", "binary", "i", "stringToUint8Array", "length", "arr", "charCodeAt", "encrypt", "key", "iv", "data", "crypto", "subtle", "name", "decrypt", "SERVER_ACTION_MANIFESTS_SINGLETON", "Symbol", "for", "setReferenceManifestsSingleton", "page", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "globalThis", "clientReferenceManifestsPerPage", "getServerModuleMap", "serverActionsManifestSingleton", "getClientReferenceManifestForRsc", "workStore", "getStore", "mergeClientReferenceManifests", "route", "getActionEncryptionKey", "<PERSON><PERSON><PERSON>", "process", "env", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "<PERSON><PERSON><PERSON>", "undefined", "importKey", "atob", "clientReferenceManifests", "Object", "values", "mergedClientReferenceManifest", "clientModules", "edgeRscModuleMapping", "rscModuleMapping"], "mappings": ";;;;;;;;;;AAMA,SAASA,cAAc,QAAQ,mCAAkC;AACjE,SAASC,gBAAgB,QAAQ,0CAAyC;AAC1E,SAASC,gBAAgB,QAAQ,gCAA+B;;;;AAEhE,IAAIC;AAEG,SAASC,oBACdC,MAAiD;IAEjD,MAAMC,QAAQ,IAAIC,WAAWF;IAC7B,MAAMG,MAAMF,MAAMG,UAAU;IAE5B,6DAA6D;IAC7D,mCAAmC;IACnC,4EAA4E;IAC5E,IAAID,MAAM,OAAO;QACf,OAAOE,OAAOC,YAAY,CAACC,KAAK,CAAC,MAAMN;IACzC;IAEA,IAAIO,SAAS;IACb,IAAK,IAAIC,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BD,UAAUH,OAAOC,YAAY,CAACL,KAAK,CAACQ,EAAE;IACxC;IACA,OAAOD;AACT;AAEO,SAASE,mBAAmBF,MAAc;IAC/C,MAAML,MAAMK,OAAOG,MAAM;IACzB,MAAMC,MAAM,IAAIV,WAAWC;IAE3B,IAAK,IAAIM,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BG,GAAG,CAACH,EAAE,GAAGD,OAAOK,UAAU,CAACJ;IAC7B;IAEA,OAAOG;AACT;AAEO,SAASE,QAAQC,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAACL,OAAO,CAC1B;QACEM,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEO,SAASI,QAAQN,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAACE,OAAO,CAC1B;QACED,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEA,sFAAsF;AACtF,wFAAwF;AACxF,4FAA4F;AAC5F,cAAc;AACd,MAAMK,oCAAoCC,OAAOC,GAAG,CAClD;AAGK,SAASC,+BAA+B,EAC7CC,IAAI,EACJC,uBAAuB,EACvBC,qBAAqB,EACrBC,eAAe,EAYhB;QAEyCC;IADxC,mBAAmB;IACnB,MAAMC,kCAAAA,CAAkCD,gDAAAA,UAAU,CAChDR,kCACD,KAAA,OAAA,KAAA,IAFuCQ,8CAErCC,+BAA+B;IAIlC,mBAAmB;IACnBD,UAAU,CAACR,kCAAkC,GAAG;QAC9CS,iCAAiC;YAC/B,GAAGA,+BAA+B;YAClC,6LAACnC,mBAAAA,EAAiB8B,MAAM,EAAEC;QAC5B;QACAC;QACAC;IACF;AACF;AAEO,SAASG;IACd,MAAMC,iCAAkCH,UAAkB,CACxDR,kCACD;IAUD,IAAI,CAACW,gCAAgC;QACnC,MAAM,OAAA,cAA0D,CAA1D,+KAAItC,iBAAAA,CAAe,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,OAAOsC,+BAA+BJ,eAAe;AACvD;AAEO,SAASK;IACd,MAAMD,iCAAkCH,UAAkB,CACxDR,kCACD;IAMD,IAAI,CAACW,gCAAgC;QACnC,MAAM,OAAA,cAA0D,CAA1D,+KAAItC,iBAAAA,CAAe,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,MAAM,EAAEoC,+BAA+B,EAAE,GAAGE;IAC5C,MAAME,gRAAYtC,mBAAAA,CAAiBuC,QAAQ;IAE3C,IAAI,CAACD,WAAW;QACd,0EAA0E;QAC1E,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,yEAAyE;QACzE,2EAA2E;QAC3E,2EAA2E;QAC3E,aAAa;QACb,OAAOE,8BAA8BN;IACvC;IAEA,MAAMJ,0BACJI,+BAA+B,CAACI,UAAUG,KAAK,CAAC;IAElD,IAAI,CAACX,yBAAyB;QAC5B,MAAM,OAAA,cAEL,CAFK,+KAAIhC,iBAAAA,CACR,CAAC,sCAAsC,EAAEwC,UAAUG,KAAK,CAAC,CAAC,CAAC,GADvD,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAOX;AACT;AAEO,eAAeY;IACpB,IAAIzC,0BAA0B;QAC5B,OAAOA;IACT;IAEA,MAAMmC,iCAAkCH,UAAkB,CACxDR,kCACD;IAID,IAAI,CAACW,gCAAgC;QACnC,MAAM,OAAA,cAA0D,CAA1D,+KAAItC,iBAAAA,CAAe,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,MAAM6C,SACJC,QAAQC,GAAG,CAACC,kCAAkC,IAC9CV,+BAA+BL,qBAAqB,CAACgB,aAAa;IAEpE,IAAIJ,WAAWK,WAAW;QACxB,MAAM,OAAA,cAA+D,CAA/D,+KAAIlD,iBAAAA,CAAe,8CAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAA8D;IACtE;IAEAG,2BAA2B,MAAMoB,OAAOC,MAAM,CAAC2B,SAAS,CACtD,OACApC,mBAAmBqC,KAAKP,UACxB,WACA,MACA;QAAC;QAAW;KAAU;IAGxB,OAAO1C;AACT;AAEA,SAASuC,8BACPN,+BAEC;IAED,MAAMiB,2BAA2BC,OAAOC,MAAM,CAC5CnB;IAGF,MAAMoB,gCAA+D;QACnEC,eAAe,CAAC;QAChBC,sBAAsB,CAAC;QACvBC,kBAAkB,CAAC;IACrB;IAEA,KAAK,MAAM3B,2BAA2BqB,yBAA0B;QAC9DG,8BAA8BC,aAAa,GAAG;YAC5C,GAAGD,8BAA8BC,aAAa;YAC9C,GAAGzB,wBAAwByB,aAAa;QAC1C;QACAD,8BAA8BE,oBAAoB,GAAG;YACnD,GAAGF,8BAA8BE,oBAAoB;YACrD,GAAG1B,wBAAwB0B,oBAAoB;QACjD;QACAF,8BAA8BG,gBAAgB,GAAG;YAC/C,GAAGH,8BAA8BG,gBAAgB;YACjD,GAAG3B,wBAAwB2B,gBAAgB;QAC7C;IACF;IAEA,OAAOH;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2935, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/shared/lib/router/utils/html-bots.ts"], "sourcesContent": ["// This regex contains the bots that we need to do a blocking render for and can't safely stream the response\n// due to how they parse the DOM. For example, they might explicitly check for metadata in the `head` tag, so we can't stream metadata tags after the `head` was sent.\nexport const HTML_LIMITED_BOT_UA_RE =\n  /Mediapartners-Google|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i\n"], "names": ["HTML_LIMITED_BOT_UA_RE"], "mappings": "AAAA,6GAA6G;AAC7G,sKAAsK;;;;AAC/J,MAAMA,yBACX,gSAA+R", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2945, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/shared/lib/router/utils/is-bot.ts"], "sourcesContent": ["import { HTML_LIMITED_BOT_UA_RE } from './html-bots'\n\n// Bot crawler that will spin up a headless browser and execute JS.\n// By default, only googlebots are considered as DOM bots. Blow is where the regex is computed from:\n// x-ref: https://developers.google.com/search/docs/crawling-indexing/google-common-crawlers\nconst HEADLESS_BROWSER_BOT_UA_RE = /google/i\n\nexport const HTML_LIMITED_BOT_UA_RE_STRING = HTML_LIMITED_BOT_UA_RE.source\n\nexport { HTML_LIMITED_BOT_UA_RE }\n\nfunction isDomBotUA(userAgent: string) {\n  return HEADLESS_BROWSER_BOT_UA_RE.test(userAgent)\n}\n\nfunction isHtmlLimitedBotUA(userAgent: string) {\n  return HTML_LIMITED_BOT_UA_RE.test(userAgent)\n}\n\nexport function isBot(userAgent: string): boolean {\n  return isDomBotUA(userAgent) || isHtmlLimitedBotUA(userAgent)\n}\n\nexport function getBotType(userAgent: string): 'dom' | 'html' | undefined {\n  if (isDomBotUA(userAgent)) {\n    return 'dom'\n  }\n  if (isHtmlLimitedBotUA(userAgent)) {\n    return 'html'\n  }\n  return undefined\n}\n"], "names": ["HTML_LIMITED_BOT_UA_RE", "HEADLESS_BROWSER_BOT_UA_RE", "HTML_LIMITED_BOT_UA_RE_STRING", "source", "isDomBotUA", "userAgent", "test", "isHtmlLimitedBotUA", "isBot", "getBotType", "undefined"], "mappings": ";;;;;AAAA,SAASA,sBAAsB,QAAQ,cAAa;;AAEpD,mEAAmE;AACnE,oGAAoG;AACpG,4FAA4F;AAC5F,MAAMC,6BAA6B;AAE5B,MAAMC,wNAAgCF,yBAAAA,CAAuBG,MAAM,CAAA;;AAI1E,SAASC,WAAWC,SAAiB;IACnC,OAAOJ,2BAA2BK,IAAI,CAACD;AACzC;AAEA,SAASE,mBAAmBF,SAAiB;IAC3C,+LAAOL,yBAAAA,CAAuBM,IAAI,CAACD;AACrC;AAEO,SAASG,MAAMH,SAAiB;IACrC,OAAOD,WAAWC,cAAcE,mBAAmBF;AACrD;AAEO,SAASI,WAAWJ,SAAiB;IAC1C,IAAID,WAAWC,YAAY;QACzB,OAAO;IACT;IACA,IAAIE,mBAAmBF,YAAY;QACjC,OAAO;IACT;IACA,OAAOK;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2987, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/lib/streaming-metadata.ts"], "sourcesContent": ["import {\n  getBotType,\n  HTML_LIMITED_BOT_UA_RE_STRING,\n} from '../../shared/lib/router/utils/is-bot'\nimport type { BaseNextRequest } from '../base-http'\n\nexport function shouldServeStreamingMetadata(\n  userAgent: string,\n  htmlLimitedBots: string | undefined\n): boolean {\n  const blockingMetadataUARegex = new RegExp(\n    htmlLimitedBots || HTML_LIMITED_BOT_UA_RE_STRING,\n    'i'\n  )\n  // Only block metadata for HTML-limited bots\n  if (userAgent && blockingMetadataUARegex.test(userAgent)) {\n    return false\n  }\n  return true\n}\n\n// When the request UA is a html-limited bot, we should do a dynamic render.\n// In this case, postpone state is not sent.\nexport function isHtmlBotRequest(req: {\n  headers: BaseNextRequest['headers']\n}): boolean {\n  const ua = req.headers['user-agent'] || ''\n  const botType = getBotType(ua)\n\n  return botType === 'html'\n}\n"], "names": ["getBotType", "HTML_LIMITED_BOT_UA_RE_STRING", "shouldServeStreamingMetadata", "userAgent", "htmlLimitedBots", "blockingMetadataUARegex", "RegExp", "test", "isHtmlBotRequest", "req", "ua", "headers", "botType"], "mappings": ";;;;AAAA,SACEA,UAAU,EACVC,6BAA6B,QACxB,uCAAsC;;;AAGtC,SAASC,6BACdC,SAAiB,EACjBC,eAAmC;IAEnC,MAAMC,0BAA0B,IAAIC,OAClCF,wNAAmBH,gCAAAA,EACnB;IAEF,4CAA4C;IAC5C,IAAIE,aAAaE,wBAAwBE,IAAI,CAACJ,YAAY;QACxD,OAAO;IACT;IACA,OAAO;AACT;AAIO,SAASK,iBAAiBC,GAEhC;IACC,MAAMC,KAAKD,IAAIE,OAAO,CAAC,aAAa,IAAI;IACxC,MAAMC,mNAAUZ,aAAAA,EAAWU;IAE3B,OAAOE,YAAY;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3011, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/app-render/action-utils.ts"], "sourcesContent": ["import type { ActionManifest } from '../../build/webpack/plugins/flight-client-entry-plugin'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { pathHasPrefix } from '../../shared/lib/router/utils/path-has-prefix'\nimport { removePathPrefix } from '../../shared/lib/router/utils/remove-path-prefix'\nimport { workAsyncStorage } from './work-async-storage.external'\n\n// This function creates a Flight-acceptable server module map proxy from our\n// Server Reference Manifest similar to our client module map.\n// This is because our manifest contains a lot of internal Next.js data that\n// are relevant to the runtime, workers, etc. that <PERSON>act doesn't need to know.\nexport function createServerModuleMap({\n  serverActionsManifest,\n}: {\n  serverActionsManifest: ActionManifest\n}) {\n  return new Proxy(\n    {},\n    {\n      get: (_, id: string) => {\n        const workers =\n          serverActionsManifest[\n            process.env.NEXT_RUNTIME === 'edge' ? 'edge' : 'node'\n          ]?.[id]?.workers\n\n        if (!workers) {\n          return undefined\n        }\n\n        const workStore = workAsyncStorage.getStore()\n\n        let workerEntry:\n          | { moduleId: string | number; async: boolean }\n          | undefined\n\n        if (workStore) {\n          workerEntry = workers[normalizeWorkerPageName(workStore.page)]\n        } else {\n          // If there's no work store defined, we can assume that a server\n          // module map is needed during module evaluation, e.g. to create a\n          // server action using a higher-order function. Therefore it should be\n          // safe to return any entry from the manifest that matches the action\n          // ID. They all refer to the same module ID, which must also exist in\n          // the current page bundle. TODO: This is currently not guaranteed in\n          // Turbopack, and needs to be fixed.\n          workerEntry = Object.values(workers).at(0)\n        }\n\n        if (!workerEntry) {\n          return undefined\n        }\n\n        const { moduleId, async } = workerEntry\n\n        return { id: moduleId, name: id, chunks: [], async }\n      },\n    }\n  )\n}\n\n/**\n * Checks if the requested action has a worker for the current page.\n * If not, it returns the first worker that has a handler for the action.\n */\nexport function selectWorkerForForwarding(\n  actionId: string,\n  pageName: string,\n  serverActionsManifest: ActionManifest\n) {\n  const workers =\n    serverActionsManifest[\n      process.env.NEXT_RUNTIME === 'edge' ? 'edge' : 'node'\n    ][actionId]?.workers\n  const workerName = normalizeWorkerPageName(pageName)\n\n  // no workers, nothing to forward to\n  if (!workers) return\n\n  // if there is a worker for this page, no need to forward it.\n  if (workers[workerName]) {\n    return\n  }\n\n  // otherwise, grab the first worker that has a handler for this action id\n  return denormalizeWorkerPageName(Object.keys(workers)[0])\n}\n\n/**\n * The flight entry loader keys actions by bundlePath.\n * bundlePath corresponds with the relative path (including 'app') to the page entrypoint.\n */\nfunction normalizeWorkerPageName(pageName: string) {\n  if (pathHasPrefix(pageName, 'app')) {\n    return pageName\n  }\n\n  return 'app' + pageName\n}\n\n/**\n * Converts a bundlePath (relative path to the entrypoint) to a routable page name\n */\nfunction denormalizeWorkerPageName(bundlePath: string) {\n  return normalizeAppPath(removePathPrefix(bundlePath, 'app'))\n}\n"], "names": ["normalizeAppPath", "pathHasPrefix", "removePathPrefix", "workAsyncStorage", "createServerModuleMap", "serverActionsManifest", "Proxy", "get", "_", "id", "workers", "process", "env", "NEXT_RUNTIME", "undefined", "workStore", "getStore", "workerEntry", "normalizeWorkerPageName", "page", "Object", "values", "at", "moduleId", "async", "name", "chunks", "selectWorkerForForwarding", "actionId", "pageName", "worker<PERSON>ame", "denormalizeWorkerPageName", "keys", "bundlePath"], "mappings": ";;;;AACA,SAASA,gBAAgB,QAAQ,0CAAyC;AAC1E,SAASC,aAAa,QAAQ,gDAA+C;AAC7E,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,gBAAgB,QAAQ,gCAA+B;;;;;AAMzD,SAASC,sBAAsB,EACpCC,qBAAqB,EAGtB;IACC,OAAO,IAAIC,MACT,CAAC,GACD;QACEC,KAAK,CAACC,GAAGC;gBAELJ,4BAAAA;YADF,MAAMK,UAAAA,CACJL,0BAAAA,qBAAqB,CACnBM,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS,0BAAS,OAChD,KAAA,OAAA,KAAA,IAAA,CAFDR,6BAAAA,uBAEG,CAACI,GAAG,KAAA,OAAA,KAAA,IAFPJ,2BAESK,OAAO;YAElB,IAAI,CAACA,SAAS;gBACZ,OAAOI;YACT;YAEA,MAAMC,gRAAYZ,mBAAAA,CAAiBa,QAAQ;YAE3C,IAAIC;YAIJ,IAAIF,WAAW;gBACbE,cAAcP,OAAO,CAACQ,wBAAwBH,UAAUI,IAAI,EAAE;YAChE,OAAO;gBACL,gEAAgE;gBAChE,kEAAkE;gBAClE,sEAAsE;gBACtE,qEAAqE;gBACrE,qEAAqE;gBACrE,qEAAqE;gBACrE,oCAAoC;gBACpCF,cAAcG,OAAOC,MAAM,CAACX,SAASY,EAAE,CAAC;YAC1C;YAEA,IAAI,CAACL,aAAa;gBAChB,OAAOH;YACT;YAEA,MAAM,EAAES,QAAQ,EAAEC,KAAK,EAAE,GAAGP;YAE5B,OAAO;gBAAER,IAAIc;gBAAUE,MAAMhB;gBAAIiB,QAAQ,EAAE;gBAAEF;YAAM;QACrD;IACF;AAEJ;AAMO,SAASG,0BACdC,QAAgB,EAChBC,QAAgB,EAChBxB,qBAAqC;QAGnCA;IADF,MAAMK,UAAAA,CACJL,mCAAAA,qBAAqB,CACnBM,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS,0BAAS,OAChD,CAACe,SAAS,KAAA,OAAA,KAAA,IAFXvB,iCAEaK,OAAO;IACtB,MAAMoB,aAAaZ,wBAAwBW;IAE3C,oCAAoC;IACpC,IAAI,CAACnB,SAAS;IAEd,6DAA6D;IAC7D,IAAIA,OAAO,CAACoB,WAAW,EAAE;QACvB;IACF;IAEA,yEAAyE;IACzE,OAAOC,0BAA0BX,OAAOY,IAAI,CAACtB,QAAQ,CAAC,EAAE;AAC1D;AAEA;;;CAGC,GACD,SAASQ,wBAAwBW,QAAgB;IAC/C,yMAAI5B,gBAAAA,EAAc4B,UAAU,QAAQ;QAClC,OAAOA;IACT;IAEA,OAAO,QAAQA;AACjB;AAEA;;CAEC,GACD,SAASE,0BAA0BE,UAAkB;IACnD,mMAAOjC,mBAAAA,0MAAiBE,mBAAAA,EAAiB+B,YAAY;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3089, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/lib/server-action-request-meta.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { BaseNextRequest } from '../base-http'\nimport type { NextRequest } from '../web/exports'\nimport { ACTION_HEADER } from '../../client/components/app-router-headers'\n\nexport function getServerActionRequestMetadata(\n  req: IncomingMessage | BaseNextRequest | NextRequest\n): {\n  actionId: string | null\n  isURLEncodedAction: boolean\n  isMultipartAction: boolean\n  isFetchAction: boolean\n  isPossibleServerAction: boolean\n} {\n  let actionId: string | null\n  let contentType: string | null\n\n  if (req.headers instanceof Headers) {\n    actionId = req.headers.get(ACTION_HEADER.toLowerCase()) ?? null\n    contentType = req.headers.get('content-type')\n  } else {\n    actionId = (req.headers[ACTION_HEADER.toLowerCase()] as string) ?? null\n    contentType = req.headers['content-type'] ?? null\n  }\n\n  const isURLEncodedAction = Boolean(\n    req.method === 'POST' && contentType === 'application/x-www-form-urlencoded'\n  )\n  const isMultipartAction = Boolean(\n    req.method === 'POST' && contentType?.startsWith('multipart/form-data')\n  )\n  const isFetchAction = Boolean(\n    actionId !== undefined &&\n      typeof actionId === 'string' &&\n      req.method === 'POST'\n  )\n\n  const isPossibleServerAction = Boolean(\n    isFetchAction || isURLEncodedAction || isMultipartAction\n  )\n\n  return {\n    actionId,\n    isURLEncodedAction,\n    isMultipartAction,\n    isFetchAction,\n    isPossibleServerAction,\n  }\n}\n\nexport function getIsPossibleServerAction(\n  req: IncomingMessage | BaseNextRequest | NextRequest\n): boolean {\n  return getServerActionRequestMetadata(req).isPossibleServerAction\n}\n"], "names": ["ACTION_HEADER", "getServerActionRequestMetadata", "req", "actionId", "contentType", "headers", "Headers", "get", "toLowerCase", "isURLEncodedAction", "Boolean", "method", "isMultipartAction", "startsWith", "isFetchAction", "undefined", "isPossibleServerAction", "getIsPossibleServerAction"], "mappings": ";;;;AAGA,SAASA,aAAa,QAAQ,6CAA4C;;AAEnE,SAASC,+BACdC,GAAoD;IAQpD,IAAIC;IACJ,IAAIC;IAEJ,IAAIF,IAAIG,OAAO,YAAYC,SAAS;QAClCH,WAAWD,IAAIG,OAAO,CAACE,GAAG,yLAACP,gBAAAA,CAAcQ,WAAW,OAAO;QAC3DJ,cAAcF,IAAIG,OAAO,CAACE,GAAG,CAAC;IAChC,OAAO;QACLJ,WAAYD,IAAIG,OAAO,yLAACL,gBAAAA,CAAcQ,WAAW,GAAG,IAAe;QACnEJ,cAAcF,IAAIG,OAAO,CAAC,eAAe,IAAI;IAC/C;IAEA,MAAMI,qBAAqBC,QACzBR,IAAIS,MAAM,KAAK,UAAUP,gBAAgB;IAE3C,MAAMQ,oBAAoBF,QACxBR,IAAIS,MAAM,KAAK,UAAA,CAAUP,eAAAA,OAAAA,KAAAA,IAAAA,YAAaS,UAAU,CAAC,sBAAA;IAEnD,MAAMC,gBAAgBJ,QACpBP,aAAaY,aACX,OAAOZ,aAAa,YACpBD,IAAIS,MAAM,KAAK;IAGnB,MAAMK,yBAAyBN,QAC7BI,iBAAiBL,sBAAsBG;IAGzC,OAAO;QACLT;QACAM;QACAG;QACAE;QACAE;IACF;AACF;AAEO,SAASC,0BACdf,GAAoD;IAEpD,OAAOD,+BAA+BC,KAAKc,sBAAsB;AACnE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3124, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/lib/fallback.ts"], "sourcesContent": ["/**\n * Describes the different fallback modes that a given page can have.\n */\nexport const enum FallbackMode {\n  /**\n   * A BLOCKING_STATIC_RENDER fallback will block the request until the page is\n   * generated. No fallback page will be rendered, and users will have to wait\n   * to render the page.\n   */\n  BLOCKING_STATIC_RENDER = 'BLOCKING_STATIC_RENDER',\n\n  /**\n   * When set to PRERENDER, a fallback page will be sent to users in place of\n   * forcing them to wait for the page to be generated. This allows the user to\n   * see a rendered page earlier.\n   */\n  PRERENDER = 'PRERENDER',\n\n  /**\n   * When set to NOT_FOUND, pages that are not already prerendered will result\n   * in a not found response.\n   */\n  NOT_FOUND = 'NOT_FOUND',\n}\n\n/**\n * The fallback value returned from the `getStaticPaths` function.\n */\nexport type GetStaticPathsFallback = boolean | 'blocking'\n\n/**\n * Parses the fallback field from the prerender manifest.\n *\n * @param fallbackField The fallback field from the prerender manifest.\n * @returns The fallback mode.\n */\nexport function parseFallbackField(\n  fallbackField: string | boolean | null | undefined\n): FallbackMode | undefined {\n  if (typeof fallbackField === 'string') {\n    return FallbackMode.PRERENDER\n  } else if (fallbackField === null) {\n    return FallbackMode.BLOCKING_STATIC_RENDER\n  } else if (fallbackField === false) {\n    return FallbackMode.NOT_FOUND\n  } else if (fallbackField === undefined) {\n    return undefined\n  } else {\n    throw new Error(\n      `Invalid fallback option: ${fallbackField}. Fallback option must be a string, null, undefined, or false.`\n    )\n  }\n}\n\nexport function fallbackModeToFallbackField(\n  fallback: FallbackMode,\n  page: string | undefined\n): string | false | null {\n  switch (fallback) {\n    case FallbackMode.BLOCKING_STATIC_RENDER:\n      return null\n    case FallbackMode.NOT_FOUND:\n      return false\n    case FallbackMode.PRERENDER:\n      if (!page) {\n        throw new Error(\n          `Invariant: expected a page to be provided when fallback mode is \"${fallback}\"`\n        )\n      }\n\n      return page\n    default:\n      throw new Error(`Invalid fallback mode: ${fallback}`)\n  }\n}\n\n/**\n * Parses the fallback from the static paths result.\n *\n * @param result The result from the static paths function.\n * @returns The fallback mode.\n */\nexport function parseStaticPathsResult(\n  result: GetStaticPathsFallback\n): FallbackMode {\n  if (result === true) {\n    return FallbackMode.PRERENDER\n  } else if (result === 'blocking') {\n    return FallbackMode.BLOCKING_STATIC_RENDER\n  } else {\n    return FallbackMode.NOT_FOUND\n  }\n}\n"], "names": ["FallbackMode", "parseFallbackField", "fallback<PERSON><PERSON>", "undefined", "Error", "fallbackModeToFallbackField", "fallback", "page", "parseStaticPathsResult", "result"], "mappings": "AAAA;;CAEC,GACD;;;;;;AAAO,IAAWA,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;IAChB;;;;GAIC,GAAA,YAAA,CAAA,yBAAA,GAAA;IAGD;;;;GAIC,GAAA,YAAA,CAAA,YAAA,GAAA;IAGD;;;GAGC,GAAA,YAAA,CAAA,YAAA,GAAA;WAlBeA;MAoBjB;AAaM,SAASC,mBACdC,aAAkD;IAElD,IAAI,OAAOA,kBAAkB,UAAU;QACrC,OAAA;IACF,OAAO,IAAIA,kBAAkB,MAAM;QACjC,OAAA;IACF,OAAO,IAAIA,kBAAkB,OAAO;QAClC,OAAA;IACF,OAAO,IAAIA,kBAAkBC,WAAW;QACtC,OAAOA;IACT,OAAO;QACL,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,CAAC,yBAAyB,EAAEF,cAAc,8DAA8D,CAAC,GADrG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAEO,SAASG,4BACdC,QAAsB,EACtBC,IAAwB;IAExB,OAAQD;QACN,KAAA;YACE,OAAO;QACT,KAAA;YACE,OAAO;QACT,KAAA;YACE,IAAI,CAACC,MAAM;gBACT,MAAM,OAAA,cAEL,CAFK,IAAIH,MACR,CAAC,iEAAiE,EAAEE,SAAS,CAAC,CAAC,GAD3E,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,OAAOC;QACT;YACE,MAAM,OAAA,cAA+C,CAA/C,IAAIH,MAAM,CAAC,uBAAuB,EAAEE,UAAU,GAA9C,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;IACxD;AACF;AAQO,SAASE,uBACdC,MAA8B;IAE9B,IAAIA,WAAW,MAAM;QACnB,OAAA;IACF,OAAO,IAAIA,WAAW,YAAY;QAChC,OAAA;IACF,OAAO;QACL,OAAA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3202, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/lib/etag.ts"], "sourcesContent": ["/**\n * FNV-1a Hash implementation\n * <AUTHOR> (tjwebb) <<EMAIL>>\n *\n * Ported from https://github.com/tjwebb/fnv-plus/blob/master/index.js\n *\n * Simplified, optimized and add modified for 52 bit, which provides a larger hash space\n * and still making use of Javascript's 53-bit integer space.\n */\nexport const fnv1a52 = (str: string) => {\n  const len = str.length\n  let i = 0,\n    t0 = 0,\n    v0 = 0x2325,\n    t1 = 0,\n    v1 = 0x8422,\n    t2 = 0,\n    v2 = 0x9ce4,\n    t3 = 0,\n    v3 = 0xcbf2\n\n  while (i < len) {\n    v0 ^= str.charCodeAt(i++)\n    t0 = v0 * 435\n    t1 = v1 * 435\n    t2 = v2 * 435\n    t3 = v3 * 435\n    t2 += v0 << 8\n    t3 += v1 << 8\n    t1 += t0 >>> 16\n    v0 = t0 & 65535\n    t2 += t1 >>> 16\n    v1 = t1 & 65535\n    v3 = (t3 + (t2 >>> 16)) & 65535\n    v2 = t2 & 65535\n  }\n\n  return (\n    (v3 & 15) * 281474976710656 +\n    v2 * 4294967296 +\n    v1 * 65536 +\n    (v0 ^ (v3 >> 4))\n  )\n}\n\nexport const generateETag = (payload: string, weak = false) => {\n  const prefix = weak ? 'W/\"' : '\"'\n  return (\n    prefix + fnv1a52(payload).toString(36) + payload.length.toString(36) + '\"'\n  )\n}\n"], "names": ["fnv1a52", "str", "len", "length", "i", "t0", "v0", "t1", "v1", "t2", "v2", "t3", "v3", "charCodeAt", "generateETag", "payload", "weak", "prefix", "toString"], "mappings": "AAAA;;;;;;;;CAQC,GACD;;;;AAAO,MAAMA,UAAU,CAACC;IACtB,MAAMC,MAAMD,IAAIE,MAAM;IACtB,IAAIC,IAAI,GACNC,KAAK,GACLC,KAAK,QACLC,KAAK,GACLC,KAAK,QACLC,KAAK,GACLC,KAAK,QACLC,KAAK,GACLC,KAAK;IAEP,MAAOR,IAAIF,IAAK;QACdI,MAAML,IAAIY,UAAU,CAACT;QACrBC,KAAKC,KAAK;QACVC,KAAKC,KAAK;QACVC,KAAKC,KAAK;QACVC,KAAKC,KAAK;QACVH,MAAMH,MAAM;QACZK,MAAMH,MAAM;QACZD,MAAMF,OAAO;QACbC,KAAKD,KAAK;QACVI,MAAMF,OAAO;QACbC,KAAKD,KAAK;QACVK,KAAMD,KAAMF,CAAAA,OAAO,EAAC,IAAM;QAC1BC,KAAKD,KAAK;IACZ;IAEA,OACGG,CAAAA,KAAK,EAAC,IAAK,kBACZF,KAAK,aACLF,KAAK,QACJF,CAAAA,KAAMM,MAAM,CAAC;AAElB,EAAC;AAEM,MAAME,eAAe,CAACC,SAAiBC,OAAO,KAAK;IACxD,MAAMC,SAASD,OAAO,QAAQ;IAC9B,OACEC,SAASjB,QAAQe,SAASG,QAAQ,CAAC,MAAMH,QAAQZ,MAAM,CAACe,QAAQ,CAAC,MAAM;AAE3E,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3243, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/compiled/fresh/index.js"], "sourcesContent": ["(()=>{\"use strict\";var e={695:e=>{\n/*!\n * fresh\n * Copyright(c) 2012 <PERSON><PERSON>\n * Copyright(c) 2016-2017 <PERSON>\n * MIT Licensed\n */\nvar r=/(?:^|,)\\s*?no-cache\\s*?(?:,|$)/;e.exports=fresh;function fresh(e,a){var t=e[\"if-modified-since\"];var s=e[\"if-none-match\"];if(!t&&!s){return false}var i=e[\"cache-control\"];if(i&&r.test(i)){return false}if(s&&s!==\"*\"){var f=a[\"etag\"];if(!f){return false}var n=true;var u=parseTokenList(s);for(var _=0;_<u.length;_++){var o=u[_];if(o===f||o===\"W/\"+f||\"W/\"+o===f){n=false;break}}if(n){return false}}if(t){var p=a[\"last-modified\"];var v=!p||!(parseHttpDate(p)<=parseHttpDate(t));if(v){return false}}return true}function parseHttpDate(e){var r=e&&Date.parse(e);return typeof r===\"number\"?r:NaN}function parseTokenList(e){var r=0;var a=[];var t=0;for(var s=0,i=e.length;s<i;s++){switch(e.charCodeAt(s)){case 32:if(t===r){t=r=s+1}break;case 44:a.push(e.substring(t,r));t=r=s+1;break;default:r=s+1;break}}a.push(e.substring(t,r));return a}}};var r={};function __nccwpck_require__(a){var t=r[a];if(t!==undefined){return t.exports}var s=r[a]={exports:{}};var i=true;try{e[a](s,s.exports,__nccwpck_require__);i=false}finally{if(i)delete r[a]}return s.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var a=__nccwpck_require__(695);module.exports=a})();"], "names": [], "mappings": "AAAA,CAAC;IAAK;IAAa,IAAI,IAAE;QAAC,KAAI,CAAA;YAC9B;;;;;CAKC,GACD,IAAI,IAAE;YAAiC,EAAE,OAAO,GAAC;YAAM,SAAS,MAAM,CAAC,EAAC,CAAC;gBAAE,IAAI,IAAE,CAAC,CAAC,oBAAoB;gBAAC,IAAI,IAAE,CAAC,CAAC,gBAAgB;gBAAC,IAAG,CAAC,KAAG,CAAC,GAAE;oBAAC,OAAO;gBAAK;gBAAC,IAAI,IAAE,CAAC,CAAC,gBAAgB;gBAAC,IAAG,KAAG,EAAE,IAAI,CAAC,IAAG;oBAAC,OAAO;gBAAK;gBAAC,IAAG,KAAG,MAAI,KAAI;oBAAC,IAAI,IAAE,CAAC,CAAC,OAAO;oBAAC,IAAG,CAAC,GAAE;wBAAC,OAAO;oBAAK;oBAAC,IAAI,IAAE;oBAAK,IAAI,IAAE,eAAe;oBAAG,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;wBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;wBAAC,IAAG,MAAI,KAAG,MAAI,OAAK,KAAG,OAAK,MAAI,GAAE;4BAAC,IAAE;4BAAM;wBAAK;oBAAC;oBAAC,IAAG,GAAE;wBAAC,OAAO;oBAAK;gBAAC;gBAAC,IAAG,GAAE;oBAAC,IAAI,IAAE,CAAC,CAAC,gBAAgB;oBAAC,IAAI,IAAE,CAAC,KAAG,CAAC,CAAC,cAAc,MAAI,cAAc,EAAE;oBAAE,IAAG,GAAE;wBAAC,OAAO;oBAAK;gBAAC;gBAAC,OAAO;YAAI;YAAC,SAAS,cAAc,CAAC;gBAAE,IAAI,IAAE,KAAG,KAAK,KAAK,CAAC;gBAAG,OAAO,OAAO,MAAI,WAAS,IAAE;YAAG;YAAC,SAAS,eAAe,CAAC;gBAAE,IAAI,IAAE;gBAAE,IAAI,IAAE,EAAE;gBAAC,IAAI,IAAE;gBAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAE,GAAE,IAAI;oBAAC,OAAO,EAAE,UAAU,CAAC;wBAAI,KAAK;4BAAG,IAAG,MAAI,GAAE;gCAAC,IAAE,IAAE,IAAE;4BAAC;4BAAC;wBAAM,KAAK;4BAAG,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC,GAAE;4BAAI,IAAE,IAAE,IAAE;4BAAE;wBAAM;4BAAQ,IAAE,IAAE;4BAAE;oBAAK;gBAAC;gBAAC,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC,GAAE;gBAAI,OAAO;YAAC;QAAC;IAAC;IAAE,IAAI,IAAE,CAAC;IAAE,SAAS,oBAAoB,CAAC;QAAE,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,IAAG,MAAI,WAAU;YAAC,OAAO,EAAE,OAAO;QAAA;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE,GAAC;YAAC,SAAQ,CAAC;QAAC;QAAE,IAAI,IAAE;QAAK,IAAG;YAAC,CAAC,CAAC,EAAE,CAAC,GAAE,EAAE,OAAO,EAAC;YAAqB,IAAE;QAAK,SAAQ;YAAC,IAAG,GAAE,OAAO,CAAC,CAAC,EAAE;QAAA;QAAC,OAAO,EAAE,OAAO;IAAA;IAAC,IAAG,OAAO,wBAAsB,aAAY,oBAAoB,EAAE,GAAC,kFAAU;IAAI,IAAI,IAAE,oBAAoB;IAAK,OAAO,OAAO,GAAC;AAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3346, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/lib/cache-control.ts"], "sourcesContent": ["import { CACHE_ONE_YEAR } from '../../lib/constants'\n\n/**\n * The revalidate option used internally for pages. A value of `false` means\n * that the page should not be revalidated. A number means that the page\n * should be revalidated after the given number of seconds (this also includes\n * `1` which means to revalidate after 1 second). A value of `0` is not a valid\n * value for this option.\n */\nexport type Revalidate = number | false\n\nexport interface CacheControl {\n  revalidate: Revalidate\n  expire: number | undefined\n}\n\nexport function getCacheControlHeader({\n  revalidate,\n  expire,\n}: CacheControl): string {\n  const swrHeader =\n    typeof revalidate === 'number' &&\n    expire !== undefined &&\n    revalidate < expire\n      ? `, stale-while-revalidate=${expire - revalidate}`\n      : ''\n\n  if (revalidate === 0) {\n    return 'private, no-cache, no-store, max-age=0, must-revalidate'\n  } else if (typeof revalidate === 'number') {\n    return `s-maxage=${revalidate}${swrHeader}`\n  }\n\n  return `s-maxage=${CACHE_ONE_YEAR}${swrHeader}`\n}\n"], "names": ["CACHE_ONE_YEAR", "getCacheControlHeader", "revalidate", "expire", "swr<PERSON><PERSON><PERSON>", "undefined"], "mappings": ";;;AAAA,SAASA,cAAc,QAAQ,sBAAqB;;AAgB7C,SAASC,sBAAsB,EACpCC,UAAU,EACVC,MAAM,EACO;IACb,MAAMC,YACJ,OAAOF,eAAe,YACtBC,WAAWE,aACXH,aAAaC,SACT,CAAC,yBAAyB,EAAEA,SAASD,YAAY,GACjD;IAEN,IAAIA,eAAe,GAAG;QACpB,OAAO;IACT,OAAO,IAAI,OAAOA,eAAe,UAAU;QACzC,OAAO,CAAC,SAAS,EAAEA,aAAaE,WAAW;IAC7C;IAEA,OAAO,CAAC,SAAS,0JAAEJ,iBAAAA,GAAiBI,WAAW;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3364, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/server/send-payload.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type RenderResult from './render-result'\nimport type { CacheControl } from './lib/cache-control'\n\nimport { isResSent } from '../shared/lib/utils'\nimport { generateETag } from './lib/etag'\nimport fresh from 'next/dist/compiled/fresh'\nimport { getCacheControlHeader } from './lib/cache-control'\nimport { RSC_CONTENT_TYPE_HEADER } from '../client/components/app-router-headers'\n\nexport function sendEtagResponse(\n  req: IncomingMessage,\n  res: ServerResponse,\n  etag: string | undefined\n): boolean {\n  if (etag) {\n    /**\n     * The server generating a 304 response MUST generate any of the\n     * following header fields that would have been sent in a 200 (OK)\n     * response to the same request: Cache-Control, Content-Location, Date,\n     * ETag, Expires, and Vary. https://tools.ietf.org/html/rfc7232#section-4.1\n     */\n    res.setHeader('ETag', etag)\n  }\n\n  if (fresh(req.headers, { etag })) {\n    res.statusCode = 304\n    res.end()\n    return true\n  }\n\n  return false\n}\n\nexport async function sendRenderResult({\n  req,\n  res,\n  result,\n  type,\n  generateEtags,\n  poweredByHeader,\n  cacheControl,\n}: {\n  req: IncomingMessage\n  res: ServerResponse\n  result: RenderResult\n  type: 'html' | 'json' | 'rsc'\n  generateEtags: boolean\n  poweredByHeader: boolean\n  cacheControl: CacheControl | undefined\n}): Promise<void> {\n  if (isResSent(res)) {\n    return\n  }\n\n  if (poweredByHeader && type === 'html') {\n    res.setHeader('X-Powered-By', 'Next.js')\n  }\n\n  // If cache control is already set on the response we don't\n  // override it to allow users to customize it via next.config\n  if (cacheControl && !res.getHeader('Cache-Control')) {\n    res.setHeader('Cache-Control', getCacheControlHeader(cacheControl))\n  }\n\n  const payload = result.isDynamic ? null : result.toUnchunkedString()\n\n  if (generateEtags && payload !== null) {\n    const etag = generateETag(payload)\n    if (sendEtagResponse(req, res, etag)) {\n      return\n    }\n  }\n\n  if (!res.getHeader('Content-Type')) {\n    res.setHeader(\n      'Content-Type',\n      result.contentType\n        ? result.contentType\n        : type === 'rsc'\n          ? RSC_CONTENT_TYPE_HEADER\n          : type === 'json'\n            ? 'application/json'\n            : 'text/html; charset=utf-8'\n    )\n  }\n\n  if (payload) {\n    res.setHeader('Content-Length', Buffer.byteLength(payload))\n  }\n\n  if (req.method === 'HEAD') {\n    res.end(null)\n    return\n  }\n\n  if (payload !== null) {\n    res.end(payload)\n    return\n  }\n\n  // Pipe the render result to the response after we get a writer for it.\n  await result.pipeToNodeResponse(res)\n}\n"], "names": ["isResSent", "generateETag", "fresh", "getCacheControlHeader", "RSC_CONTENT_TYPE_HEADER", "sendEtagResponse", "req", "res", "etag", "<PERSON><PERSON><PERSON><PERSON>", "headers", "statusCode", "end", "sendRenderResult", "result", "type", "generateEtags", "poweredByHeader", "cacheControl", "<PERSON><PERSON><PERSON><PERSON>", "payload", "isDynamic", "toUnchunkedString", "contentType", "<PERSON><PERSON><PERSON>", "byteLength", "method", "pipeToNodeResponse"], "mappings": ";;;;AAIA,SAASA,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,YAAY,QAAQ,aAAY;AACzC,OAAOC,WAAW,2BAA0B;AAC5C,SAASC,qBAAqB,QAAQ,sBAAqB;AAC3D,SAASC,uBAAuB,QAAQ,0CAAyC;;;;;;AAE1E,SAASC,iBACdC,GAAoB,EACpBC,GAAmB,EACnBC,IAAwB;IAExB,IAAIA,MAAM;QACR;;;;;KAKC,GACDD,IAAIE,SAAS,CAAC,QAAQD;IACxB;IAEA,mKAAIN,UAAAA,EAAMI,IAAII,OAAO,EAAE;QAAEF;IAAK,IAAI;QAChCD,IAAII,UAAU,GAAG;QACjBJ,IAAIK,GAAG;QACP,OAAO;IACT;IAEA,OAAO;AACT;AAEO,eAAeC,iBAAiB,EACrCP,GAAG,EACHC,GAAG,EACHO,MAAM,EACNC,IAAI,EACJC,aAAa,EACbC,eAAe,EACfC,YAAY,EASb;IACC,sKAAIlB,YAAAA,EAAUO,MAAM;QAClB;IACF;IAEA,IAAIU,mBAAmBF,SAAS,QAAQ;QACtCR,IAAIE,SAAS,CAAC,gBAAgB;IAChC;IAEA,2DAA2D;IAC3D,6DAA6D;IAC7D,IAAIS,gBAAgB,CAACX,IAAIY,SAAS,CAAC,kBAAkB;QACnDZ,IAAIE,SAAS,CAAC,8LAAiBN,wBAAAA,EAAsBe;IACvD;IAEA,MAAME,UAAUN,OAAOO,SAAS,GAAG,OAAOP,OAAOQ,iBAAiB;IAElE,IAAIN,iBAAiBI,YAAY,MAAM;QACrC,MAAMZ,wKAAOP,eAAAA,EAAamB;QAC1B,IAAIf,iBAAiBC,KAAKC,KAAKC,OAAO;YACpC;QACF;IACF;IAEA,IAAI,CAACD,IAAIY,SAAS,CAAC,iBAAiB;QAClCZ,IAAIE,SAAS,CACX,gBACAK,OAAOS,WAAW,GACdT,OAAOS,WAAW,GAClBR,SAAS,gMACPX,0BAAAA,GACAW,SAAS,SACP,qBACA;IAEZ;IAEA,IAAIK,SAAS;QACXb,IAAIE,SAAS,CAAC,kBAAkBe,OAAOC,UAAU,CAACL;IACpD;IAEA,IAAId,IAAIoB,MAAM,KAAK,QAAQ;QACzBnB,IAAIK,GAAG,CAAC;QACR;IACF;IAEA,IAAIQ,YAAY,MAAM;QACpBb,IAAIK,GAAG,CAACQ;QACR;IACF;IAEA,uEAAuE;IACvE,MAAMN,OAAOa,kBAAkB,CAACpB;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3442, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/20162/Documents/noodle-shop/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport type { IncomingMessage, ServerResponse } from 'node:http'\n\nimport {\n  AppPageRouteModule,\n  type AppPageRouteHandlerContext,\n} from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\n\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\nimport { getRevalidateReason } from '../../server/instrumentation/utils'\nimport { getTracer, SpanKind, type Span } from '../../server/lib/trace/tracer'\nimport { getRequestMeta } from '../../server/request-meta'\nimport { BaseServerSpan } from '../../server/lib/trace/constants'\nimport { interopDefault } from '../../server/app-render/interop-default'\nimport { NodeNextRequest, NodeNextResponse } from '../../server/base-http/node'\nimport { checkIsAppPPREnabled } from '../../server/lib/experimental/ppr'\nimport {\n  getFallbackRouteParams,\n  type FallbackRouteParams,\n} from '../../server/request/fallback-params'\nimport { setReferenceManifestsSingleton } from '../../server/app-render/encryption-utils'\nimport {\n  isHtmlBotRequest,\n  shouldServeStreamingMetadata,\n} from '../../server/lib/streaming-metadata'\nimport { createServerModuleMap } from '../../server/app-render/action-utils'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { getIsPossibleServerAction } from '../../server/lib/server-action-request-meta'\nimport {\n  RSC_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n} from '../../client/components/app-router-headers'\nimport { getBotType, isBot } from '../../shared/lib/router/utils/is-bot'\nimport {\n  CachedRouteKind,\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n} from '../../server/response-cache'\nimport { FallbackMode, parseFallbackField } from '../../lib/fallback'\nimport RenderResult from '../../server/render-result'\nimport { CACHE_ONE_YEAR, NEXT_CACHE_TAGS_HEADER } from '../../lib/constants'\nimport type { CacheControl } from '../../server/lib/cache-control'\nimport { ENCODED_TAGS } from '../../server/stream-utils/encoded-tags'\nimport { sendRenderResult } from '../../server/send-payload'\nimport { NoFallbackError } from '../../shared/lib/no-fallback-error.external'\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nimport GlobalError from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\nexport { GlobalError }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nimport * as entryBase from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n  distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n  projectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n})\n\nexport async function handler(\n  req: IncomingMessage,\n  res: ServerResponse,\n  ctx: {\n    waitUntil: (prom: Promise<void>) => void\n  }\n) {\n  let srcPage = 'VAR_DEFINITION_PAGE'\n\n  // turbopack doesn't normalize `/index` in the page name\n  // so we need to to process dynamic routes properly\n  // TODO: fix turbopack providing differing value from webpack\n  if (process.env.TURBOPACK) {\n    srcPage = srcPage.replace(/\\/index$/, '') || '/'\n  } else if (srcPage === '/index') {\n    // we always normalize /index specifically\n    srcPage = '/'\n  }\n  const multiZoneDraftMode = process.env\n    .__NEXT_MULTI_ZONE_DRAFT_MODE as any as boolean\n\n  const initialPostponed = getRequestMeta(req, 'postponed')\n  // TODO: replace with more specific flags\n  const minimalMode = getRequestMeta(req, 'minimalMode')\n\n  const prepareResult = await routeModule.prepare(req, res, {\n    srcPage,\n    multiZoneDraftMode,\n  })\n\n  if (!prepareResult) {\n    res.statusCode = 400\n    res.end('Bad Request')\n    ctx.waitUntil?.(Promise.resolve())\n    return null\n  }\n\n  const {\n    buildId,\n    query,\n    params,\n    parsedUrl,\n    pageIsDynamic,\n    buildManifest,\n    nextFontManifest,\n    reactLoadableManifest,\n    serverActionsManifest,\n    clientReferenceManifest,\n    subresourceIntegrityManifest,\n    prerenderManifest,\n    isDraftMode,\n    resolvedPathname,\n    revalidateOnlyGenerated,\n    routerServerContext,\n    nextConfig,\n  } = prepareResult\n\n  const pathname = parsedUrl.pathname || '/'\n  const normalizedSrcPage = normalizeAppPath(srcPage)\n\n  let { isOnDemandRevalidate } = prepareResult\n\n  const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage]\n  const isPrerendered = prerenderManifest.routes[resolvedPathname]\n\n  let isSSG = Boolean(\n    prerenderInfo ||\n      isPrerendered ||\n      prerenderManifest.routes[normalizedSrcPage]\n  )\n\n  const userAgent = req.headers['user-agent'] || ''\n  const botType = getBotType(userAgent)\n  const isHtmlBot = isHtmlBotRequest(req)\n\n  /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */\n  const isPrefetchRSCRequest =\n    getRequestMeta(req, 'isPrefetchRSCRequest') ??\n    Boolean(req.headers[NEXT_ROUTER_PREFETCH_HEADER])\n\n  // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n\n  const isRSCRequest =\n    getRequestMeta(req, 'isRSCRequest') ?? Boolean(req.headers[RSC_HEADER])\n\n  const isPossibleServerAction = getIsPossibleServerAction(req)\n\n  /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */\n  const couldSupportPPR: boolean = checkIsAppPPREnabled(\n    nextConfig.experimental.ppr\n  )\n\n  // When enabled, this will allow the use of the `?__nextppronly` query to\n  // enable debugging of the static shell.\n  const hasDebugStaticShellQuery =\n    process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1' &&\n    typeof query.__nextppronly !== 'undefined' &&\n    couldSupportPPR\n\n  // When enabled, this will allow the use of the `?__nextppronly` query\n  // to enable debugging of the fallback shell.\n  const hasDebugFallbackShellQuery =\n    hasDebugStaticShellQuery && query.__nextppronly === 'fallback'\n\n  // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n  // prerender manifest and this is an app page.\n  const isRoutePPREnabled: boolean =\n    couldSupportPPR &&\n    ((\n      prerenderManifest.routes[normalizedSrcPage] ??\n      prerenderManifest.dynamicRoutes[normalizedSrcPage]\n    )?.renderingMode === 'PARTIALLY_STATIC' ||\n      // Ideally we'd want to check the appConfig to see if this page has PPR\n      // enabled or not, but that would require plumbing the appConfig through\n      // to the server during development. We assume that the page supports it\n      // but only during development.\n      (hasDebugStaticShellQuery &&\n        (routeModule.isDev === true ||\n          routerServerContext?.experimentalTestProxy === true)))\n\n  const isDebugStaticShell: boolean =\n    hasDebugStaticShellQuery && isRoutePPREnabled\n\n  // We should enable debugging dynamic accesses when the static shell\n  // debugging has been enabled and we're also in development mode.\n  const isDebugDynamicAccesses =\n    isDebugStaticShell && routeModule.isDev === true\n\n  const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled\n\n  // If we're in minimal mode, then try to get the postponed information from\n  // the request metadata. If available, use it for resuming the postponed\n  // render.\n  const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined\n\n  // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n  // we can use this fact to only generate the flight data for the request\n  // because we can't cache the HTML (as it's also dynamic).\n  const isDynamicRSCRequest =\n    isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest\n\n  // Need to read this before it's stripped by stripFlightHeaders. We don't\n  // need to transfer it to the request meta because it's only read\n  // within this function; the static segment data should have already been\n  // generated, so we will always either return a static response or a 404.\n  const segmentPrefetchHeader = getRequestMeta(req, 'segmentPrefetchRSCRequest')\n\n  // TODO: investigate existing bug with shouldServeStreamingMetadata always\n  // being true for a revalidate due to modifying the base-server this.renderOpts\n  // when fixing this to correct logic it causes hydration issue since we set\n  // serveStreamingMetadata to true during export\n  let serveStreamingMetadata = !userAgent\n    ? true\n    : shouldServeStreamingMetadata(userAgent, nextConfig.htmlLimitedBots)\n\n  if (isHtmlBot && isRoutePPREnabled) {\n    isSSG = false\n    serveStreamingMetadata = false\n  }\n\n  // In development, we always want to generate dynamic HTML.\n  let supportsDynamicResponse: boolean =\n    // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true ||\n    // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG ||\n    // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' ||\n    // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest\n\n  // When html bots request PPR page, perform the full dynamic rendering.\n  const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled\n\n  let ssgCacheKey: string | null = null\n  if (\n    !isDraftMode &&\n    isSSG &&\n    !supportsDynamicResponse &&\n    !isPossibleServerAction &&\n    !minimalPostponed &&\n    !isDynamicRSCRequest\n  ) {\n    ssgCacheKey = resolvedPathname\n  }\n\n  // the staticPathKey differs from ssgCacheKey since\n  // ssgCacheKey is null in dev since we're always in \"dynamic\"\n  // mode in dev to bypass the cache, but we still need to honor\n  // dynamicParams = false in dev mode\n  let staticPathKey = ssgCacheKey\n  if (!staticPathKey && routeModule.isDev) {\n    staticPathKey = resolvedPathname\n  }\n\n  const ComponentMod = {\n    ...entryBase,\n    tree,\n    pages,\n    GlobalError,\n    handler,\n    routeModule,\n    __next_app__,\n  }\n\n  // Before rendering (which initializes component tree modules), we have to\n  // set the reference manifests to our global store so Server Action's\n  // encryption util can access to them at the top level of the page module.\n  if (serverActionsManifest && clientReferenceManifest) {\n    setReferenceManifestsSingleton({\n      page: srcPage,\n      clientReferenceManifest,\n      serverActionsManifest,\n      serverModuleMap: createServerModuleMap({\n        serverActionsManifest,\n      }),\n    })\n  }\n\n  const method = req.method || 'GET'\n  const tracer = getTracer()\n  const activeSpan = tracer.getActiveScopeSpan()\n\n  try {\n    const invokeRouteModule = async (\n      span: Span | undefined,\n      context: AppPageRouteHandlerContext\n    ) => {\n      const nextReq = new NodeNextRequest(req)\n      const nextRes = new NodeNextResponse(res)\n\n      // TODO: adapt for putting the RDC inside the postponed data\n      // If we're in dev, and this isn't a prefetch or a server action,\n      // we should seed the resume data cache.\n      if (process.env.NODE_ENV === 'development') {\n        if (\n          nextConfig.experimental.dynamicIO &&\n          !isPrefetchRSCRequest &&\n          !context.renderOpts.isPossibleServerAction\n        ) {\n          const warmup = await routeModule.warmup(nextReq, nextRes, context)\n\n          // If the warmup is successful, we should use the resume data\n          // cache from the warmup.\n          if (warmup.metadata.renderResumeDataCache) {\n            context.renderOpts.renderResumeDataCache =\n              warmup.metadata.renderResumeDataCache\n          }\n        }\n      }\n\n      return routeModule.render(nextReq, nextRes, context).finally(() => {\n        if (!span) return\n\n        span.setAttributes({\n          'http.status_code': res.statusCode,\n          'next.rsc': false,\n        })\n\n        const rootSpanAttributes = tracer.getRootSpanAttributes()\n        // We were unable to get attributes, probably OTEL is not enabled\n        if (!rootSpanAttributes) {\n          return\n        }\n\n        if (\n          rootSpanAttributes.get('next.span_type') !==\n          BaseServerSpan.handleRequest\n        ) {\n          console.warn(\n            `Unexpected root span type '${rootSpanAttributes.get(\n              'next.span_type'\n            )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n          )\n          return\n        }\n\n        const route = rootSpanAttributes.get('next.route')\n        if (route) {\n          const name = `${method} ${route}`\n\n          span.setAttributes({\n            'next.route': route,\n            'http.route': route,\n            'next.span_name': name,\n          })\n          span.updateName(name)\n        } else {\n          span.updateName(`${method} ${req.url}`)\n        }\n      })\n    }\n\n    const doRender = async ({\n      span,\n      postponed,\n      fallbackRouteParams,\n    }: {\n      span?: Span\n      /**\n       * The postponed data for this render. This is only provided when resuming\n       * a render that has been postponed.\n       */\n      postponed: string | undefined\n\n      /**\n       * The unknown route params for this render.\n       */\n      fallbackRouteParams: FallbackRouteParams | null\n    }): Promise<ResponseCacheEntry> => {\n      const context: AppPageRouteHandlerContext = {\n        query,\n        params,\n        page: normalizedSrcPage,\n        sharedContext: {\n          buildId,\n        },\n        serverComponentsHmrCache: getRequestMeta(\n          req,\n          'serverComponentsHmrCache'\n        ),\n        fallbackRouteParams,\n        renderOpts: {\n          App: () => null,\n          Document: () => null,\n          pageConfig: {},\n          ComponentMod,\n          Component: interopDefault(ComponentMod),\n\n          params,\n          routeModule,\n          page: srcPage,\n          postponed,\n          shouldWaitOnAllReady,\n          serveStreamingMetadata,\n          supportsDynamicResponse:\n            typeof postponed === 'string' || supportsDynamicResponse,\n          buildManifest,\n          nextFontManifest,\n          reactLoadableManifest,\n          subresourceIntegrityManifest,\n          serverActionsManifest,\n          clientReferenceManifest,\n          setIsrStatus: routerServerContext?.setIsrStatus,\n\n          dir: routeModule.projectDir,\n          isDraftMode,\n          isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n          botType,\n          isOnDemandRevalidate,\n          isPossibleServerAction,\n          assetPrefix: nextConfig.assetPrefix,\n          nextConfigOutput: nextConfig.output,\n          crossOrigin: nextConfig.crossOrigin,\n          trailingSlash: nextConfig.trailingSlash,\n          previewProps: prerenderManifest.preview,\n          deploymentId: nextConfig.deploymentId,\n          enableTainting: nextConfig.experimental.taint,\n          htmlLimitedBots: nextConfig.htmlLimitedBots,\n          devtoolSegmentExplorer:\n            nextConfig.experimental.devtoolSegmentExplorer,\n          reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n\n          multiZoneDraftMode,\n          incrementalCache: getRequestMeta(req, 'incrementalCache'),\n          cacheLifeProfiles: nextConfig.experimental.cacheLife,\n          basePath: nextConfig.basePath,\n          serverActions: nextConfig.experimental.serverActions,\n\n          ...(isDebugStaticShell || isDebugDynamicAccesses\n            ? {\n                nextExport: true,\n                supportsDynamicResponse: false,\n                isStaticGeneration: true,\n                isRevalidate: true,\n                isDebugDynamicAccesses: isDebugDynamicAccesses,\n              }\n            : {}),\n\n          experimental: {\n            isRoutePPREnabled,\n            expireTime: nextConfig.expireTime,\n            staleTimes: nextConfig.experimental.staleTimes,\n            dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n            clientSegmentCache: Boolean(\n              nextConfig.experimental.clientSegmentCache\n            ),\n            dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n            inlineCss: Boolean(nextConfig.experimental.inlineCss),\n            authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n            clientTraceMetadata:\n              nextConfig.experimental.clientTraceMetadata || ([] as any),\n          },\n\n          waitUntil: ctx.waitUntil,\n          onClose: (cb) => {\n            res.on('close', cb)\n          },\n          onAfterTaskError: () => {},\n\n          onInstrumentationRequestError: (error, _request, errorContext) =>\n            routeModule.onRequestError(\n              req,\n              error,\n              errorContext,\n              routerServerContext\n            ),\n          err: getRequestMeta(req, 'invokeError'),\n          dev: routeModule.isDev,\n        },\n      }\n\n      const result = await invokeRouteModule(span, context)\n\n      const { metadata } = result\n\n      const {\n        cacheControl,\n        headers = {},\n        // Add any fetch tags that were on the page to the response headers.\n        fetchTags: cacheTags,\n      } = metadata\n\n      if (cacheTags) {\n        headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n      }\n\n      // Pull any fetch metrics from the render onto the request.\n      ;(req as any).fetchMetrics = metadata.fetchMetrics\n\n      // we don't throw static to dynamic errors in dev as isSSG\n      // is a best guess in dev since we don't have the prerender pass\n      // to know whether the path is actually static or not\n      if (\n        isSSG &&\n        cacheControl?.revalidate === 0 &&\n        !routeModule.isDev &&\n        !isRoutePPREnabled\n      ) {\n        const staticBailoutInfo = metadata.staticBailoutInfo\n\n        const err = new Error(\n          `Page changed from static to dynamic at runtime ${resolvedPathname}${\n            staticBailoutInfo?.description\n              ? `, reason: ${staticBailoutInfo.description}`\n              : ``\n          }` +\n            `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`\n        )\n\n        if (staticBailoutInfo?.stack) {\n          const stack = staticBailoutInfo.stack\n          err.stack = err.message + stack.substring(stack.indexOf('\\n'))\n        }\n\n        throw err\n      }\n\n      return {\n        value: {\n          kind: CachedRouteKind.APP_PAGE,\n          html: result,\n          headers,\n          rscData: metadata.flightData,\n          postponed: metadata.postponed,\n          status: metadata.statusCode,\n          segmentData: metadata.segmentData,\n        } satisfies CachedAppPageValue,\n        cacheControl,\n      } satisfies ResponseCacheEntry\n    }\n\n    const responseGenerator: ResponseGenerator = async ({\n      hasResolved,\n      previousCacheEntry,\n      isRevalidating,\n      span,\n    }) => {\n      const isProduction = routeModule.isDev === false\n      const didRespond = hasResolved || res.writableEnded\n\n      // skip on-demand revalidate if cache is not present and\n      // revalidate-if-generated is set\n      if (\n        isOnDemandRevalidate &&\n        revalidateOnlyGenerated &&\n        !previousCacheEntry &&\n        !minimalMode\n      ) {\n        if (routerServerContext?.render404) {\n          await routerServerContext.render404(req, res)\n        } else {\n          res.statusCode = 404\n          res.end('This page could not be found')\n        }\n        return null\n      }\n\n      let fallbackMode: FallbackMode | undefined\n\n      if (prerenderInfo) {\n        fallbackMode = parseFallbackField(prerenderInfo.fallback)\n      }\n\n      // When serving a bot request, we want to serve a blocking render and not\n      // the prerendered page. This ensures that the correct content is served\n      // to the bot in the head.\n      if (fallbackMode === FallbackMode.PRERENDER && isBot(userAgent)) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      if (previousCacheEntry?.isStale === -1) {\n        isOnDemandRevalidate = true\n      }\n\n      // TODO: adapt for PPR\n      // only allow on-demand revalidate for fallback: true/blocking\n      // or for prerendered fallback: false paths\n      if (\n        isOnDemandRevalidate &&\n        (fallbackMode !== FallbackMode.NOT_FOUND || previousCacheEntry)\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      if (\n        !minimalMode &&\n        fallbackMode !== FallbackMode.BLOCKING_STATIC_RENDER &&\n        staticPathKey &&\n        !didRespond &&\n        !isDraftMode &&\n        pageIsDynamic &&\n        (isProduction || !isPrerendered)\n      ) {\n        // if the page has dynamicParams: false and this pathname wasn't\n        // prerendered trigger the no fallback handling\n        if (\n          // In development, fall through to render to handle missing\n          // getStaticPaths.\n          (isProduction || prerenderInfo) &&\n          // When fallback isn't present, abort this render so we 404\n          fallbackMode === FallbackMode.NOT_FOUND\n        ) {\n          throw new NoFallbackError()\n        }\n\n        let fallbackResponse: ResponseCacheEntry | null | undefined\n\n        if (isRoutePPREnabled && !isRSCRequest) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await routeModule.handleResponse({\n            cacheKey: isProduction ? normalizedSrcPage : null,\n            req,\n            nextConfig,\n            routeKind: RouteKind.APP_PAGE,\n            isFallback: true,\n            prerenderManifest,\n            isRoutePPREnabled,\n            responseGenerator: async () =>\n              doRender({\n                span,\n                // We pass `undefined` as rendering a fallback isn't resumed\n                // here.\n                postponed: undefined,\n                fallbackRouteParams:\n                  // If we're in production or we're debugging the fallback\n                  // shell then we should postpone when dynamic params are\n                  // accessed.\n                  isProduction || isDebugFallbackShell\n                    ? getFallbackRouteParams(normalizedSrcPage)\n                    : null,\n              }),\n            waitUntil: ctx.waitUntil,\n          })\n\n          // If the fallback response was set to null, then we should return null.\n          if (fallbackResponse === null) return null\n\n          // Otherwise, if we did get a fallback response, we should return it.\n          if (fallbackResponse) {\n            // Remove the cache control from the response to prevent it from being\n            // used in the surrounding cache.\n            delete fallbackResponse.cacheControl\n\n            return fallbackResponse\n          }\n        }\n      }\n      // Only requests that aren't revalidating can be resumed. If we have the\n      // minimal postponed data, then we should resume the render with it.\n      const postponed =\n        !isOnDemandRevalidate && !isRevalidating && minimalPostponed\n          ? minimalPostponed\n          : undefined\n\n      // When we're in minimal mode, if we're trying to debug the static shell,\n      // we should just return nothing instead of resuming the dynamic render.\n      if (\n        (isDebugStaticShell || isDebugDynamicAccesses) &&\n        typeof postponed !== 'undefined'\n      ) {\n        return {\n          cacheControl: { revalidate: 1, expire: undefined },\n          value: {\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(''),\n            pageData: {},\n            headers: undefined,\n            status: undefined,\n          } satisfies CachedPageValue,\n        }\n      }\n\n      // If this is a dynamic route with PPR enabled and the default route\n      // matches were set, then we should pass the fallback route params to\n      // the renderer as this is a fallback revalidation request.\n      const fallbackRouteParams =\n        pageIsDynamic &&\n        isRoutePPREnabled &&\n        (getRequestMeta(req, 'renderFallbackShell') || isDebugFallbackShell)\n          ? getFallbackRouteParams(pathname)\n          : null\n\n      // Perform the render.\n      return doRender({\n        span,\n        postponed,\n        fallbackRouteParams,\n      })\n    }\n\n    const handleResponse = async (span?: Span): Promise<null | void> => {\n      const cacheEntry = await routeModule.handleResponse({\n        cacheKey: ssgCacheKey,\n        responseGenerator: (c) =>\n          responseGenerator({\n            span,\n            ...c,\n          }),\n        routeKind: RouteKind.APP_PAGE,\n        isOnDemandRevalidate,\n        isRoutePPREnabled,\n        req,\n        nextConfig,\n        prerenderManifest,\n        waitUntil: ctx.waitUntil,\n      })\n\n      if (isDraftMode) {\n        res.setHeader(\n          'Cache-Control',\n          'private, no-cache, no-store, max-age=0, must-revalidate'\n        )\n      }\n\n      // In dev, we should not cache pages for any reason.\n      if (routeModule.isDev) {\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n      }\n\n      if (!cacheEntry) {\n        if (ssgCacheKey) {\n          // A cache entry might not be generated if a response is written\n          // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n          // have a cache key. If we do have a cache key but we don't end up\n          // with a cache entry, then either Next.js or the application has a\n          // bug that needs fixing.\n          throw new Error('invariant: cache entry required but not generated')\n        }\n        return null\n      }\n\n      if (cacheEntry.value?.kind !== CachedRouteKind.APP_PAGE) {\n        throw new Error(\n          `Invariant app-page handler received invalid cache entry ${cacheEntry.value?.kind}`\n        )\n      }\n\n      const didPostpone = typeof cacheEntry.value.postponed === 'string'\n\n      if (\n        isSSG &&\n        // We don't want to send a cache header for requests that contain dynamic\n        // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n        // request, then we should set the cache header.\n        !isDynamicRSCRequest &&\n        (!didPostpone || isPrefetchRSCRequest)\n      ) {\n        if (!minimalMode) {\n          // set x-nextjs-cache header to match the header\n          // we set for the image-optimizer\n          res.setHeader(\n            'x-nextjs-cache',\n            isOnDemandRevalidate\n              ? 'REVALIDATED'\n              : cacheEntry.isMiss\n                ? 'MISS'\n                : cacheEntry.isStale\n                  ? 'STALE'\n                  : 'HIT'\n          )\n        }\n        // Set a header used by the client router to signal the response is static\n        // and should respect the `static` cache staleTime value.\n        res.setHeader(NEXT_IS_PRERENDER_HEADER, '1')\n      }\n      const { value: cachedData } = cacheEntry\n\n      // Coerce the cache control parameter from the render.\n      let cacheControl: CacheControl | undefined\n\n      // If this is a resume request in minimal mode it is streamed with dynamic\n      // content and should not be cached.\n      if (minimalPostponed) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      }\n\n      // If this is in minimal mode and this is a flight request that isn't a\n      // prefetch request while PPR is enabled, it cannot be cached as it contains\n      // dynamic content.\n      else if (\n        minimalMode &&\n        isRSCRequest &&\n        !isPrefetchRSCRequest &&\n        isRoutePPREnabled\n      ) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      } else if (!routeModule.isDev) {\n        // If this is a preview mode request, we shouldn't cache it\n        if (isDraftMode) {\n          cacheControl = { revalidate: 0, expire: undefined }\n        }\n\n        // If this isn't SSG, then we should set change the header only if it is\n        // not set already.\n        else if (!isSSG) {\n          if (!res.getHeader('Cache-Control')) {\n            cacheControl = { revalidate: 0, expire: undefined }\n          }\n        } else if (cacheEntry.cacheControl) {\n          // If the cache entry has a cache control with a revalidate value that's\n          // a number, use it.\n          if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n            if (cacheEntry.cacheControl.revalidate < 1) {\n              throw new Error(\n                `Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`\n              )\n            }\n\n            cacheControl = {\n              revalidate: cacheEntry.cacheControl.revalidate,\n              expire: cacheEntry.cacheControl?.expire ?? nextConfig.expireTime,\n            }\n          }\n          // Otherwise if the revalidate value is false, then we should use the\n          // cache time of one year.\n          else {\n            cacheControl = { revalidate: CACHE_ONE_YEAR, expire: undefined }\n          }\n        }\n      }\n\n      cacheEntry.cacheControl = cacheControl\n\n      if (\n        typeof segmentPrefetchHeader === 'string' &&\n        cachedData?.kind === CachedRouteKind.APP_PAGE &&\n        cachedData.segmentData\n      ) {\n        // This is a prefetch request issued by the client Segment Cache. These\n        // should never reach the application layer (lambda). We should either\n        // respond from the cache (HIT) or respond with 204 No Content (MISS).\n\n        // Set a header to indicate that PPR is enabled for this route. This\n        // lets the client distinguish between a regular cache miss and a cache\n        // miss due to PPR being disabled. In other contexts this header is used\n        // to indicate that the response contains dynamic data, but here we're\n        // only using it to indicate that the feature is enabled — the segment\n        // response itself contains whether the data is dynamic.\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '2')\n\n        // Add the cache tags header to the response if it exists and we're in\n        // minimal mode while rendering a static page.\n        const tags = cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n        if (minimalMode && isSSG && tags && typeof tags === 'string') {\n          res.setHeader(NEXT_CACHE_TAGS_HEADER, tags)\n        }\n\n        const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader)\n        if (matchedSegment !== undefined) {\n          // Cache hit\n          return sendRenderResult({\n            req,\n            res,\n            type: 'rsc',\n            generateEtags: nextConfig.generateEtags,\n            poweredByHeader: nextConfig.poweredByHeader,\n            result: RenderResult.fromStatic(matchedSegment),\n            cacheControl: cacheEntry.cacheControl,\n          })\n        }\n\n        // Cache miss. Either a cache entry for this route has not been generated\n        // (which technically should not be possible when PPR is enabled, because\n        // at a minimum there should always be a fallback entry) or there's no\n        // match for the requested segment. Respond with a 204 No Content. We\n        // don't bother to respond with 404, because these requests are only\n        // issued as part of a prefetch.\n        res.statusCode = 204\n        return sendRenderResult({\n          req,\n          res,\n          type: 'rsc',\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: RenderResult.fromStatic(''),\n          cacheControl: cacheEntry.cacheControl,\n        })\n      }\n\n      // If there's a callback for `onCacheEntry`, call it with the cache entry\n      // and the revalidate options.\n      const onCacheEntry = getRequestMeta(req, 'onCacheEntry')\n      if (onCacheEntry) {\n        const finished = await onCacheEntry(\n          {\n            ...cacheEntry,\n            // TODO: remove this when upstream doesn't\n            // always expect this value to be \"PAGE\"\n            value: {\n              ...cacheEntry.value,\n              kind: 'PAGE',\n            },\n          },\n          {\n            url: getRequestMeta(req, 'initURL'),\n          }\n        )\n        if (finished) {\n          // TODO: maybe we have to end the request?\n          return null\n        }\n      }\n\n      // If the request has a postponed state and it's a resume request we\n      // should error.\n      if (didPostpone && minimalPostponed) {\n        throw new Error(\n          'Invariant: postponed state should not be present on a resume request'\n        )\n      }\n\n      if (cachedData.headers) {\n        const headers = { ...cachedData.headers }\n\n        if (!minimalMode || !isSSG) {\n          delete headers[NEXT_CACHE_TAGS_HEADER]\n        }\n\n        for (let [key, value] of Object.entries(headers)) {\n          if (typeof value === 'undefined') continue\n\n          if (Array.isArray(value)) {\n            for (const v of value) {\n              res.appendHeader(key, v)\n            }\n          } else if (typeof value === 'number') {\n            value = value.toString()\n            res.appendHeader(key, value)\n          } else {\n            res.appendHeader(key, value)\n          }\n        }\n      }\n\n      // Add the cache tags header to the response if it exists and we're in\n      // minimal mode while rendering a static page.\n      const tags = cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n      if (minimalMode && isSSG && tags && typeof tags === 'string') {\n        res.setHeader(NEXT_CACHE_TAGS_HEADER, tags)\n      }\n\n      // If the request is a data request, then we shouldn't set the status code\n      // from the response because it should always be 200. This should be gated\n      // behind the experimental PPR flag.\n      if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n        res.statusCode = cachedData.status\n      }\n\n      // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n      if (\n        !minimalMode &&\n        cachedData.status &&\n        RedirectStatusCode[cachedData.status] &&\n        isRSCRequest\n      ) {\n        res.statusCode = 200\n      }\n\n      // Mark that the request did postpone.\n      if (didPostpone) {\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n      }\n\n      // we don't go through this block when preview mode is true\n      // as preview mode is a dynamic request (bypasses cache) and doesn't\n      // generate both HTML and payloads in the same request so continue to just\n      // return the generated payload\n      if (isRSCRequest && !isDraftMode) {\n        // If this is a dynamic RSC request, then stream the response.\n        if (typeof cachedData.rscData === 'undefined') {\n          if (cachedData.postponed) {\n            throw new Error('Invariant: Expected postponed to be undefined')\n          }\n\n          return sendRenderResult({\n            req,\n            res,\n            type: 'rsc',\n            generateEtags: nextConfig.generateEtags,\n            poweredByHeader: nextConfig.poweredByHeader,\n            result: cachedData.html,\n            // Dynamic RSC responses cannot be cached, even if they're\n            // configured with `force-static` because we have no way of\n            // distinguishing between `force-static` and pages that have no\n            // postponed state.\n            // TODO: distinguish `force-static` from pages with no postponed state (static)\n            cacheControl: isDynamicRSCRequest\n              ? { revalidate: 0, expire: undefined }\n              : cacheEntry.cacheControl,\n          })\n        }\n\n        // As this isn't a prefetch request, we should serve the static flight\n        // data.\n        return sendRenderResult({\n          req,\n          res,\n          type: 'rsc',\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: RenderResult.fromStatic(cachedData.rscData),\n          cacheControl: cacheEntry.cacheControl,\n        })\n      }\n\n      // This is a request for HTML data.\n      let body = cachedData.html\n\n      // If there's no postponed state, we should just serve the HTML. This\n      // should also be the case for a resume request because it's completed\n      // as a server render (rather than a static render).\n      if (!didPostpone || minimalMode) {\n        return sendRenderResult({\n          req,\n          res,\n          type: 'html',\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: body,\n          cacheControl: cacheEntry.cacheControl,\n        })\n      }\n\n      // If we're debugging the static shell or the dynamic API accesses, we\n      // should just serve the HTML without resuming the render. The returned\n      // HTML will be the static shell so all the Dynamic API's will be used\n      // during static generation.\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        // Since we're not resuming the render, we need to at least add the\n        // closing body and html tags to create valid HTML.\n        body.chain(\n          new ReadableStream({\n            start(controller) {\n              controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n              controller.close()\n            },\n          })\n        )\n\n        return sendRenderResult({\n          req,\n          res,\n          type: 'html',\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: body,\n          cacheControl: { revalidate: 0, expire: undefined },\n        })\n      }\n\n      // This request has postponed, so let's create a new transformer that the\n      // dynamic data can pipe to that will attach the dynamic data to the end\n      // of the response.\n      const transformer = new TransformStream<Uint8Array, Uint8Array>()\n      body.chain(transformer.readable)\n\n      // Perform the render again, but this time, provide the postponed state.\n      // We don't await because we want the result to start streaming now, and\n      // we've already chained the transformer's readable to the render result.\n      doRender({\n        span,\n        postponed: cachedData.postponed,\n        // This is a resume render, not a fallback render, so we don't need to\n        // set this.\n        fallbackRouteParams: null,\n      })\n        .then(async (result) => {\n          if (!result) {\n            throw new Error('Invariant: expected a result to be returned')\n          }\n\n          if (result.value?.kind !== CachedRouteKind.APP_PAGE) {\n            throw new Error(\n              `Invariant: expected a page response, got ${result.value?.kind}`\n            )\n          }\n\n          // Pipe the resume result to the transformer.\n          await result.value.html.pipeTo(transformer.writable)\n        })\n        .catch((err) => {\n          // An error occurred during piping or preparing the render, abort\n          // the transformers writer so we can terminate the stream.\n          transformer.writable.abort(err).catch((e) => {\n            console.error(\"couldn't abort transformer\", e)\n          })\n        })\n\n      return sendRenderResult({\n        req,\n        res,\n        type: 'html',\n        generateEtags: nextConfig.generateEtags,\n        poweredByHeader: nextConfig.poweredByHeader,\n        result: body,\n        // We don't want to cache the response if it has postponed data because\n        // the response being sent to the client it's dynamic parts are streamed\n        // to the client on the same request.\n        cacheControl: { revalidate: 0, expire: undefined },\n      })\n    }\n\n    // TODO: activeSpan code path is for when wrapped by\n    // next-server can be removed when this is no longer used\n    if (activeSpan) {\n      await handleResponse(activeSpan)\n    } else {\n      return await tracer.withPropagatedContext(req.headers, () =>\n        tracer.trace(\n          BaseServerSpan.handleRequest,\n          {\n            spanName: `${method} ${req.url}`,\n            kind: SpanKind.SERVER,\n            attributes: {\n              'http.method': method,\n              'http.target': req.url,\n            },\n          },\n          handleResponse\n        )\n      )\n    }\n  } catch (err) {\n    // if we aren't wrapped by base-server handle here\n    if (!activeSpan && !(err instanceof NoFallbackError)) {\n      await routeModule.onRequestError(\n        req,\n        err,\n        {\n          routerKind: 'App Router',\n          routePath: srcPage,\n          routeType: 'render',\n          revalidateReason: getRevalidateReason({\n            isRevalidate: isSSG,\n            isOnDemandRevalidate,\n          }),\n        },\n        routerServerContext\n      )\n    }\n\n    // rethrow so that we can handle serving error page\n    throw err\n  }\n}\n"], "names": ["AppPageRouteModule", "RouteKind", "getRevalidateReason", "getTracer", "SpanKind", "getRequestMeta", "BaseServerSpan", "interopDefault", "NodeNextRequest", "NodeNextResponse", "checkIsAppPPREnabled", "getFallbackRouteParams", "setReferenceManifestsSingleton", "isHtmlBotRequest", "shouldServeStreamingMetadata", "createServerModuleMap", "normalizeAppPath", "getIsPossibleServerAction", "RSC_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_IS_PRERENDER_HEADER", "NEXT_DID_POSTPONE_HEADER", "getBotType", "isBot", "CachedRouteKind", "FallbackMode", "parseFallbackField", "RenderResult", "CACHE_ONE_YEAR", "NEXT_CACHE_TAGS_HEADER", "ENCODED_TAGS", "sendRenderResult", "NoFallbackError", "tree", "pages", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "entryBase", "RedirectStatusCode", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree", "distDir", "process", "env", "__NEXT_RELATIVE_DIST_DIR", "projectDir", "__NEXT_RELATIVE_PROJECT_DIR", "handler", "req", "res", "ctx", "prerenderManifest", "srcPage", "TURBOPACK", "replace", "multiZoneDraftMode", "__NEXT_MULTI_ZONE_DRAFT_MODE", "initialPostponed", "minimalMode", "prepareResult", "prepare", "statusCode", "end", "waitUntil", "Promise", "resolve", "buildId", "query", "params", "parsedUrl", "pageIsDynamic", "buildManifest", "nextFontManifest", "reactLoadableManifest", "serverActionsManifest", "clientReferenceManifest", "subresourceIntegrityManifest", "isDraftMode", "resolvedPathname", "revalidateOnlyGenerated", "routerServerContext", "nextConfig", "normalizedSrcPage", "isOnDemandRevalidate", "prerenderInfo", "dynamicRoutes", "isP<PERSON>endered", "routes", "isSSG", "Boolean", "userAgent", "headers", "botType", "isHtmlBot", "isPrefetchRSCRequest", "isRSCRequest", "isPossibleServerAction", "couldSupportPPR", "experimental", "ppr", "hasDebugStaticShellQuery", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "__nextppronly", "hasDebugFallbackShellQuery", "isRoutePPREnabled", "renderingMode", "isDev", "experimentalTestProxy", "isDebugStaticShell", "isDebugDynamicAccesses", "isDebugFallbackShell", "minimalPostponed", "undefined", "isDynamicRSCRequest", "segmentPrefetchHeader", "serveStreamingMetadata", "htmlLimitedBots", "supportsDynamicResponse", "shouldWaitOnAllReady", "ssgCacheKey", "static<PERSON><PERSON><PERSON><PERSON>", "ComponentMod", "serverModuleMap", "method", "tracer", "activeSpan", "getActiveScopeSpan", "invokeRouteModule", "span", "context", "nextReq", "nextRes", "NODE_ENV", "dynamicIO", "renderOpts", "warmup", "metadata", "renderResumeDataCache", "render", "finally", "setAttributes", "rootSpanAttributes", "getRootSpanAttributes", "get", "handleRequest", "console", "warn", "route", "name", "updateName", "url", "doR<PERSON>", "postponed", "fallbackRouteParams", "sharedContext", "serverComponentsHmrCache", "App", "Document", "pageConfig", "Component", "setIsrStatus", "dir", "isRevalidate", "assetPrefix", "nextConfigOutput", "output", "crossOrigin", "trailingSlash", "previewProps", "preview", "deploymentId", "enableTainting", "taint", "devtoolSegmentExplorer", "reactMaxHeadersLength", "incrementalCache", "cacheLifeProfiles", "cacheLife", "basePath", "serverActions", "nextExport", "isStaticGeneration", "expireTime", "staleTimes", "clientSegmentCache", "dynamicOnHover", "inlineCss", "authInterrupts", "clientTraceMetadata", "onClose", "cb", "on", "onAfterTaskError", "onInstrumentationRequestError", "error", "_request", "errorContext", "onRequestError", "err", "dev", "result", "cacheControl", "fetchTags", "cacheTags", "fetchMetrics", "revalidate", "staticBailoutInfo", "Error", "description", "stack", "message", "substring", "indexOf", "value", "html", "rscData", "flightData", "status", "segmentData", "responseGenerator", "hasResolved", "previousCacheEntry", "isRevalidating", "isProduction", "didRespond", "writableEnded", "render404", "fallbackMode", "fallback", "PRERENDER", "BLOCKING_STATIC_RENDER", "isStale", "NOT_FOUND", "fallbackResponse", "handleResponse", "cache<PERSON>ey", "routeKind", "<PERSON><PERSON><PERSON><PERSON>", "expire", "PAGES", "fromStatic", "pageData", "cacheEntry", "cachedData", "c", "<PERSON><PERSON><PERSON><PERSON>", "didPostpone", "isMiss", "<PERSON><PERSON><PERSON><PERSON>", "tags", "matchedSegment", "type", "generateEtags", "poweredByHeader", "onCacheEntry", "finished", "key", "Object", "entries", "Array", "isArray", "v", "append<PERSON><PERSON>er", "toString", "body", "chain", "ReadableStream", "start", "controller", "enqueue", "CLOSED", "BODY_AND_HTML", "close", "transformer", "TransformStream", "readable", "then", "pipeTo", "writable", "catch", "abort", "e", "withPropagatedContext", "trace", "spanName", "SERVER", "attributes", "routerKind", "routePath", "routeType", "revalidateReason"], "mappings": ";;;;;;;AAGA,SACEA,kBAAkB,QAEb,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IAE7C,wBAAwB;AAEnF,SAASE,mBAAmB,QAAQ,qCAAoC;AAExE,SAASG,cAAc,QAAQ,4BAA2B;AAE1D,SAASE,cAAc,QAAQ,0CAAyC;AAExE,SAASG,oBAAoB,QAAQ,oCAAmC;AAKxE,SAASE,8BAA8B,QAAQ,2CAA0C;AAMzF,SAASI,gBAAgB,QAAQ,0CAAyC;AAQ1E,SAASM,UAAU,EAAEC,KAAK,QAAQ,uCAAsC;AACxE,SACEC,eAAe,QAKV,8BAA6B;AACpC,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,qBAAoB;AACrE,OAAOC,kBAAkB,6BAA4B;AACrD,SAASC,cAAc,EAAEC,sBAAsB,QAAQ,sBAAqB;AAE5E,SAASC,YAAY,QAAQ,yCAAwC;AACrE,SAASC,gBAAgB,QAAQ,4BAA2B;AAC5D,SAASC,eAAe,QAAQ,8CAA6C;AAW7E,yEAAyE;AACzE,UAAU;AACV,cAAc;AACd,eAAe;AAEf,SAASC,IAAI,EAAEC,KAAK,GAAE;AAEtB,OAAOC,iBAAiB,+BAA+B;;IAAE,wBAAwB;;AAAsB,EAAC;AAExG,SAASA,WAAW,GAAE;AAMtB,8BAA8B;AAC9B,iCAAiC;AAEjC,OAAO,MAAMC,eAAe;IAC1BC,SAASC;IACTC,WAAWC;IAwDX,IAAI,CAACgC,eAAe;;QAGlBT,IAAIa,SAAS,oBAAbb,IAAIa,SAAS,MAAbb,KAAgBc,QAAQC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1DnC,EAAC,uEAAA;AAED,UAAA,EAAYrC,eAAe,0CAA0C;IAAE,EAAA,OAAA;IAAA;IAAA,UAAwB;QAAsB,EAAC,UAAA;YAAA;YAAA;gBACtH,SAASC,GAAAA;oBAAAA;oBAAAA,EAAkB,QAAQ,+CAA8C;wBAEjF,YAAA;4BAAA,CAAc;4BAAA,mCAA0C;oCAAE,QAAA;oCAAA;oCAAA,OAAwB;wCAAsB,EAAC,UAAA;4CAAA;4CAAA,CAEzG;4CAAA,yDAA4D;gDAC5D,KAAO,KAAA,CAAMC;gDAAAA,QAAc;oDAAA,GAAI3C,CAAAA,gBAAmB;oDAAA;iDAAA;;2CAChD4C,YAAY;;yCACVC,MAAM5C,UAAU6C,QAAQ;8CACxBC,IAAAA,CAAM,CAAA;oCAAA;iCAAA;;iCACNC,UAAU;sCACV,IAAA,CAAA;4BAAA;yBAAA,gCAA2C;;yBAC3CC,YAAY;8BACZC,IAAAA,CAAAA;oBAAAA,CAAU;iBAAA;;iBACVC,UAAU,EAAE;kBACd,QAAA,CAAA;YAAA;SAAA;;KACAC,UAAU;cACRC,IAAAA;YAAAA,GAAYpB,GAAAA;gBACd,OAAA,QAAA;wBAAA;4BACAqB,qNAASC,QAAQC,GAAG,CAACC,WAAAA,EAAAA,MAAAA,MAAAA,IAAwB,EAAA,EAAI,IAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACjDC,OAAAA,CAAYH,EAAAA,6SAAAA,CAAAA,MAAQC,GAAG,CAACG,CAAAA,KAAAA,CAAAA,CAAAA,EAAAA,6SAAAA,CAAAA,CAA2B,IAAI,KAAA,CAAA,MAAA,EAAA;4BACvD,MAAA,CAAA,YAAA,CAAA;wBAEF,CAAO;qBAAA,cAAeC,QACpBC,GAAoB,EACpBC,GAAmB,EACnBC,GAEC;gBA8GGC;UA5GJ;QAAA,EAAIC,QAAAA;YAAAA,CAAU,GAAA;YAAA;SAAA;UAEd,WAAA;YAAA,IAAA;YAAA,EAAwD;SAAA;UACxD,WAAA;YAAA,IAAA;YAAmD;SAAA;UACnD,cAAA;YAAA,IAAA;YAAA,IAA6D;SAAA;UAC7D,IAAIV,QAAQC,EAAAA;YAAG,CAACU,GAAAA,IAAS,EAAE;YAAA;SAAA;;OACzBD,UAAUA,QAAQE,OAAO,CAAC,YAAY,OAAO;IAC/C,EAAA,KAAO,GAAA;IAAIF,YAAY,UAAU;CAAA;;;;IAQjC,EAAA,uBAAA,gBAAyC,MAAA,CAAA;IACzC,EAAA,IAAMM,cAAclE,QAAAA,OAAewD,KAAK,UAAA,CAAA;AAExC,GAAMW,GAAAA,aAAgB,EAAA,IAAM7B,YAAY8B,OAAO,CAACZ,KAAKC,KAAK;QACxDG,KAAAA;QACAG,OAAAA;IACF;;;;AA8BA,GAAM2B,GAAAA,cAAAA,GAAoB/E,CAAAA,sMAAAA,CAAAA,gBAAiBiD,KAAAA,CAAAA;IAE3C,IAAI,EAAE+B,MAAAA,cAAoB,EAAE,GAAGxB;QAE/B,EAAMyB,IAAAA,+LAAAA,CAAAA,YAAAA,CAAAA,EAAgBjC,MAAAA,YAAkBkC,aAAa,CAACH,kBAAkB;QACxE,EAAMI,IAAAA,YAAgBnC,kBAAkBoC,MAAM,CAACT,iBAAiB;QAE5DU,QAAQC,EAAAA,MACVL,iBACEE,iBACAnC,kBAAkBoC,MAAM,CAACL,kBAAkB;QAG/C,EAAMQ,YAAY1C,IAAI2C,OAAO,CAAC,aAAa,IAAI;QAC/C,EAAMC,UAAUnF,WAAWiF;QAC3B,EAAMG,QAAAA,IAAY7F,iBAAiBgD;QAEnC,UAAA,EAAA;;;QAIA,IAAM8C,QAAAA,eACJtG,eAAewD,KAAK,2BACpByC,QAAQzC,IAAI2C,OAAO,CAACrF,4BAA4B;IAElD,uFAAuF;IAEvF,MAAMyF,GAAAA,YACJvG,eAAewD,KAAK,aAAA,WAAmByC,QAAQzC,IAAI2C,OAAO,CAACtF,WAAW;IAExE,MAAM2F,MAAAA,mBAAyB5F,qBAAAA,EAA0B4C;IAEzD;;;IAGC,EACD,EAAA,IAAMiD,MAAAA,YAA2BpG,qBAC/BoF,WAAWiB,YAAY,CAACC,GAAG;IAG7B,wDAAA,iBAAyE;IACzE,wCAAwC,WAAA;IACxC,MAAMC,2BACJ1D,QAAQC,GAAG,CAAC0D,gBAAAA,0BAA0C,KAAK,OAC3D,OAAOlC,MAAMmC,aAAa,KAAK,eAC/BL;IAEF,wCAAA,2CAAsE;QACtE,UAAA,QAAA,OAAA,CAAA,YAAA,GAA6C,IAAA;IAC7C,MAAMM,CAAAA,IAAAA,YAAAA,UAAAA,EACJH,4BAA4BjC,MAAMmC,aAAa,KAAK;QAEtD,0CAAA,8BAA4E;QAC5E,UAAA,gCAA8C;IAC9C,MAAME,oBACJP,mBACC,CAAA,EACC9C,QAAAA,kBAAkBoC,MAAM,CAACL,kBAAkB,IAC3C/B,kBAAkBkC,aAAa,CAACH,kBAAkB,qBAFnD,AACC/B,MAECsD,aAAa,MAAK,sBACnB,uEAAuE;IACvE,MAAA,kEAAwE;IACxE,MAAA,mBAAA,CAAA,GAAA,gKAAA,CAAA,iBAAA,EAAA,KAAA,2BAAwE;IACxE,+BAA+B,UAAA;IAC9BL,MAAAA,cAAAA,CAAAA,GAAAA,gKAAAA,CAAAA,QACEtE,CAAAA,QAAAA,EAAAA,KAAAA,CAAY4E,KAAK,KAAK,QACrB1B,CAAAA,uCAAAA,oBAAqB2B,qBAAqB,MAAK,IAAG,CAAE;IAE5D,MAAMC,gBAAAA,KACJR,CAAAA,YAAAA,OAAAA,CAAAA,KAAAA,EAA4BI,GAAAA;QAE9B,gEAAoE;QACpE,6DAAiE;IACjE,MAAMK,yBACJD,sBAAsB9E,YAAY4E,KAAK,KAAK;IAE9C,IAAA,CAAA,CAAMI,cAAAA,SAAuBP,8BAA8BC;QAE3D,IAAA,UAAA,GAAA,sDAA2E;QAC3E,IAAA,GAAA,CAAA,4DAAwE;QACxE,IAAA,EAAU,OAAA,IAAA,OAAA,KAAA,IAAA,IAAA,SAAA,CAAA,IAAA,CAAA,KAAA,QAAA,OAAA;QACV,EAAMO,KAAAA,cAAmBP,oBAAoB/C,mBAAmBuD;IAEhE,0EAA0E;IAC1E,MAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,SAAA,EAAA,aAAA,EAAA,aAAA,CAAwE,CAAA,gBAAA,EAAA,qBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,4BAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,uBAAA,EAAA,mBAAA,EAAA,UAAA,EAAA,GAAA;IACxE,MAAA,WAAA,UAAA,QAAA,IAAA,mBAA0D;IAC1D,MAAMC,oBAAAA,CAAAA,GAAAA,uLAAAA,CAAAA,EACJT,iBAAAA,EAAAA,MAAqBT,gBAAgB,CAACD;IAExC,IAAA,EAAA,oBAAA,EAAA,GAAA,0CAAyE;IACzE,MAAA,gBAAA,kBAAA,aAAA,CAAA,WAAiE,OAAA;IACjE,MAAA,gBAAA,kBAAA,MAAA,CAAA,iBAAA,SAAyE;IACzE,IAAA,QAAA,QAAA,iBAAA,iBAAA,kBAAA,CAAyE,KAAA,CAAA,kBAAA;IACzE,MAAMoB,YAAAA,IAAAA,OAAAA,CAAwB1H,aAAAA,EAAewD,EAAAA,GAAK;IAElD,MAAA,UAAA,CAAA,GAAA,oMAAA,CAAA,aAAA,EAAA,+CAA0E;IAC1E,MAAA,YAAA,CAAA,GAAA,6KAAA,CAAA,mBAAA,EAAA,4CAA+E;IAC/E,2EAA2E;;;IAM3E,EAAA,EAAI6C,IAAAA,SAAaW,cAAAA,CAAAA,GAAAA,gKAAAA,CAAAA,IAAmB,aAAA,EAAA,KAAA,2BAAA,QAAA,IAAA,OAAA,CAAA,uLAAA,CAAA,8BAAA,CAAA;QAClChB,QAAQ,2EAAA;QACR2B,EAAAA,eAAAA,CAAAA,GAAAA,gKAAAA,CAAAA,QAAyB,SAAA,EAAA,KAAA,mBAAA,QAAA,IAAA,OAAA,CAAA,uLAAA,CAAA,aAAA,CAAA;IAC3B,MAAA,yBAAA,CAAA,GAAA,2LAAA,CAAA,4BAAA,EAAA;IAEA,2DAA2D;;;IAIzDrF,EAAAA,MAAAA,IAAY4E,KAAK,KAAK,IAAA,CAAA,GAAA,2KAAA,CAAA,IACtB,mBAAA,EAAA,WAAA,YAAA,CAAA,GAAA,yBAAqE;IACrE,gBAAgB,yDAAA;IAChB,CAAClB,SACD,8BAAA,qCAAmE;IACnE,MAAA,EAAQ,yBAAA,4CAAA,OAAA,OAAA,MAAA,aAAA,KAAA,eAAA;IACR,OAAO/B,qBAAqB,YAC5B,8BAAA,wCAAsE;IACtE,uBAAuB,sBAAA;IACvBwD,MAAAA,6BAAAA,4BAAAA,MAAAA,aAAAA,KAAAA;IAEF,uEAAuE,KAAA;IACvE,MAAMK,uBAAuBzB,aAAaW,IAAAA;IAE1C,IAAIe,EAAAA,YAA6B,QAAA,mBAAA,CAAA,CAAA,CAAA,QAAA,kBAAA,MAAA,CAAA,kBAAA,IAAA,kBAAA,aAAA,CAAA,kBAAA,KAAA,OAAA,KAAA,IAAA,MAAA,aAAA,MAAA,sBAAA,uEAAA;IACjC,IACE,CAAC1C,eACDW,SACA,CAAC6B,2BACD,CAACrB,cAAAA,YACD,CAACe,oBACD,CAACE,qBACD;QACAM,cAAczC,sDAAAA;IAChB,+BAAA;IAEA,4BAAA,CAAA,YAAA,KAAA,KAAmD,QAAA,CAAA,uBAAA,OAAA,KAAA,IAAA,oBAAA,qBAAA,MAAA,IAAA,CAAA;IACnD,MAAA,qBAAA,4BAAA,MAA6D;IAC7D,8DAA8D,MAAA;IAC9D,oCAAoC,6BAAA;IACpC,IAAI0C,EAAAA,cAAgBD,WAAAA,sBAAAA,YAAAA,KAAAA,KAAAA;IACpB,IAAI,CAACC,CAAAA,gBAAiB1F,OAAAA,KAAY4E,KAAK,EAAE,kBAAA;QACvCc,gBAAgB1C,uDAAAA;IAClB,wEAAA;IAEA,MAAM2C,IAAAA,WAAe;QACnB,EAAA,CAAG7F,SAAS,SAAA,oBAAA,mBAAA;QACZR,sEAAAA;QACAC,oEAAAA;QACAC,sDAAAA;QACAyB,EAAAA,sBAAAA,qBAAAA,gBAAAA,CAAAA;QACAjB,qEAAAA;QACAP,6DAAAA;IACF,yEAAA;IAEA,yEAAA,CAA0E;IAC1E,MAAA,wBAAA,CAAA,GAAA,gKAAA,CAAA,iBAAA,EAAA,KAAA,mBAAqE;IACrE,0EAA0E;IAC1E,IAAImD,yBAAyBC,yBAAyB,yBAAA;QACpD5E,+BAA+B,wCAAA;YAC7BmC,MAAMkB,iCAAAA;YACNuB,qBAAAA,CAAAA,YAAAA,OAAAA,CAAAA,GAAAA,6KAAAA,CAAAA,+BAAAA,EAAAA,WAAAA,WAAAA,eAAAA;YACAD,SAAAA,mBAAAA;YACAgD,IAAAA,aAAiBxH,sBAAsB;gBACrCwE,iBAAAA;YACF;QACF,uDAAA;IACF,IAAA,0BAEA,MAAMiD,SAAS3E,IAAI2E,MAAM,IAAI,gCAAA;IAC7B,MAAMC,MAAAA,GAAStI,EAAAA,KAAAA,QAAAA,qEAAAA;IACf,MAAMuI,UAAAA,GAAaD,OAAOE,kBAAkB;IAE5C,CAAA,GAAI,MAAA,mEAAA;QACF,IAAA,EAAMC,oBAAoB,OACxBC,MACAC;YAEA,MAAMC,UAAU,IAAIvI,YAAAA,IAAgBqD,kEAAAA;YACpC,MAAMmF,SAAAA,CAAU,IAAIvI,iBAAiBqD;YAErC,4DAA4D;YAC5D,+DAAA,EAAiE;YACjE,qBAAA,aAAA,MAAwC;YACxC,IAAIP,MAAAA,EAAQC,GAAG,CAACyF,QAAQ,KAAK,eAAe;gBAC1C,IACEnD,IAAAA,OAAWiB,EAAAA,CAAAA,SAAY,CAACmC,SAAS,IACjC,CAACvC,GAAAA,CAAAA,oBACD,CAACmC,KAAAA,CAAAA,EAAQK,UAAU,CAACtC,OAAAA,CAAAA,cAAsB,EAC1C,KAAA;oBACA,EAAA,IAAMuC,SAAS,MAAMzG,YAAYyG,MAAM,CAACL,SAASC,SAASF;oBAE1D,6DAA6D;oBAC7D,yBAAyB,UAAA;oBACzB,IAAIM,OAAOC,QAAQ,CAACC,qBAAqB,EAAE,EAAA;wBACzCR,QAAQK,UAAU,CAACG,qBAAqB,EAAA,CACtCF,OAAOC,QAAQ,CAACC,qBAAqB;oBACzC,oBAAA;gBACF,QAAA;YACF,cAAA,YAAA,KAAA,EAAA;YAEA,OAAO3G,KAAAA,OAAY4G,MAAM,CAACR,SAASC,SAASF,SAASU,OAAO,CAAC;gBAC3D,IAAI,CAACX,MAAM;gBAEXA,KAAKY,IAAAA,SAAa,CAAC;2NACjB,oBAAoB3F,IAAIY,UAAU;oBAClC,YAAY;gBACd;mPAEA,EAAA,GAAMgF,qBAAqBjB,OAAOkB,qBAAqB;gBACvD,iEAAiE;gBACjE,IAAI,CAACD,oBAAoB;oBACvB;gBACF;gBAEA,IACEA,mBAAmBE,GAAG,CAAC,sBACvBtJ,aAAAA,EAAeuJ,aAAa,EAC5B;oBACAC,QAAQC,IAAI,CACV,CAAC,2BAA2B,EAAEL,UAAAA,SAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;oBAE1E,0DAAA;gBACF,iBAAA,yBAAA;0MAEA,MAAMI,QAAQN,WAAAA,EAAAA,UAAmBE,GAAG,CAAC;gBACrC,EAAA,EAAII,OAAO;oBACT,MAAMC,OAAO,GAAGzB,OAAO,CAAC,EAAEwB,OAAO;oBAEjCnB,KAAKY,aAAa,CAAC;wBACjB,KAAA,CAAA,GAAA,iLAAA,CAAA,SAAcO,eAAAA,EAAAA;wBACd,cAAcA;wBACd,kBAAkBC;oBACpB;oBACApB,KAAKqB,UAAU,CAACD;gBAClB,GAAA,IAAO,MAAA,IAAA;gMACLpB,KAAKqB,MAAAA,OAAU,CAAC,GAAG1B,OAAO,CAAC,EAAE3E,IAAIsG,GAAG,EAAE;gBACxC,OAAA,OAAA,kBAAA;YACF;QACF,MAAA,oBAAA,OAAA,MAAA;YAEA,EAAMC,IAAAA,OAAW,GAAA,0KAAO,EACtBvB,IAAI,EACJwB,UAAAA,CAAAA,CAAS,EACTC,mBAAmB,EAapB;YACC,MAAMxB,UAAsC,IAAA,qKAAA,CAAA,mBAAA,CAAA;gBAC1C9D,wDAAAA;gBACAC,6DAAAA;gBACAlC,MAAMgD,8BAAAA;gBACNwE,eAAe,qBAAA;oBACbxF,WAAAA,YAAAA,CAAAA,SAAAA,IAAAA,CAAAA,wBAAAA,CAAAA,QAAAA,UAAAA,CAAAA,sBAAAA,EAAAA;oBACF,MAAA,SAAA,MAAA,YAAA,MAAA,CAAA,SAAA,SAAA;oBACAyF,sBAA0BnK,eACxBwD,KACA,mBAAA;oBAEFyG,yBAAAA;oBACAnB,IAAAA,IAAY,GAAA,QAAA,CAAA,qBAAA,EAAA;wBACVsB,CAAK,IAAM,GAAA,UAAA,CAAA,qBAAA,GAAA,OAAA,QAAA,CAAA,qBAAA;oBACXC,UAAU,IAAM;oBAChBC,YAAY,CAAC;oBACbrC;oBACAsC,WAAWrK,MAAAA,CAAAA,QAAe+H,CAAAA,SAAAA,SAAAA,OAAAA,CAAAA;oBAE1BrD,CAAAA,MAAAA;oBACAtC,CAAAA,aAAAA,CAAAA;oBACAI,MAAMkB,cAAAA,IAAAA,UAAAA;oBACNoG,YAAAA;oBACAlC;oBACAH,EAAAA,qBAAAA,OAAAA,qBAAAA;oBACAE,yBACE,OAAOmC,cAAc,YAAYnC,GAAAA;oBACnC9C,CAAAA,oBAAAA;oBACAC;oBACAC;oBACAG,mBAAAA,GAAAA,CAAAA,sBAAAA,0KAAAA,CAAAA,iBAAAA,CAAAA,aAAAA,EAAAA;oBACAF,QAAAA,IAAAA,CAAAA,CAAAA,2BAAAA,EAAAA,mBAAAA,GAAAA,CAAAA,kBAAAA,qEAAAA,CAAAA;oBACAC;oBACAqF,YAAY,EAAEhF,uCAAAA,oBAAqBgF,YAAY;oBAE/CC,EAAAA,GAAKnI,KAAAA,OAAYe,UAAU,EAAA,GAAA,CAAA;oBAC3BgC,OAAAA;oBACAqF,MAAAA,OAAAA,CAAc1E,EAAAA,OAAS,CAACgE,EAAAA,OAAAA,IAAa,CAACvC;oBACtCrB,KAAAA,aAAAA,CAAAA;wBACAT,cAAAA;wBACAa,cAAAA;wBACAmE,SAAalF,SAAAA,EAAWkF,WAAW;oBACnCC,kBAAkBnF,WAAWoF,MAAM;oBACnCC,KAAAA,QAAarF,EAAAA,CAAAA,QAAWqF,WAAW;oBACnCC,GAAAA,YAAetF,WAAWsF,aAAa;oBACvCC,KAAAA,SAAcrH,CAAAA,CAAAA,GAAAA,OAAAA,CAAAA,EAAAA,GAAkBsH,CAAAA,GAAAA,EAAAA,CAAO;oBACvCC,cAAczF,WAAWyF,YAAY;oBACrCC,gBAAgB1F,WAAWiB,YAAY,CAAC0E,KAAK;oBAC7CxD,iBAAiBnC,WAAWmC,eAAe;oBAC3CyD,KAAAA,OAAAA,EAAAA,IAAAA,EAAAA,IACE5F,KAAAA,EAAAA,IAAWiB,YAAY,CAAC2E,EAAAA,EAAAA,kBAAsB;oBAChDC,QAAAA,eAAuB7F,WAAW6F,qBAAqB;oBAEvDvH;oBACAwH,kBAAkBvL,eAAewD,KAAK;oBACtCgI,EAAAA,iBAAmB/F,WAAWiB,YAAY,CAAC+E,SAAS;oBACpDC,UAAUjG,CAAAA,UAAWiG,QAAQ;oBAC7BC,eAAelG,WAAWiB,YAAY,CAACiF,aAAa;oBAEpD,GAAIvE,sBAAsBC,yBACtB;wBACEuE,YAAY,MAAA,CAAA,GAAA,gKAAA,CAAA,iBAAA,EAAA,KAAA;wBACZ/D,yBAAyB;wBACzBgE,IAAAA,gBAAoB;wBACpBnB,CAAAA,IAAAA,SAAc;wBACdrD,MAAAA,IAAAA,cAAwBA;oBAC1B,IACA,CAAC,CAAC,MAAA,CAAA;oBAENX,cAAc;wBACZM,OAAAA,CAAAA,GAAAA,oLAAAA,CAAAA,iBAAAA,EAAAA;wBACA8E,YAAYrG,WAAWqG,UAAU;wBACjCC,YAAYtG,WAAWiB,YAAY,CAACqF,UAAU;wBAC9ClD,EAAAA,SAAW5C,QAAQR,WAAWiB,YAAY,CAACmC,SAAS;wBACpDmD,oBAAoB/F,QAClBR,WAAWiB,YAAY,CAACsF,kBAAkB;wBAE5CC,gBAAgBhG,QAAQR,WAAWiB,YAAY,CAACuF,cAAc;wBAC9DC,WAAWjG,QAAQR,WAAWiB,YAAY,CAACwF,SAAS;wBACpDC,gBAAgBlG,KAAAA,GAAQR,IAAAA,OAAWiB,OAAAA,KAAY,CAACyF,MAAAA,QAAc;wBAC9DC,qBACE3G,WAAWiB,YAAY,CAAC0F,mBAAmB,IAAK,EAAE;oBACtD;oBAEA7H,WAAWb,IAAIa,SAAS;oBACxB8H,SAAS,CAACC;wBACR7I,IAAI8I,EAAE,CAAC,SAASD;oBAClB;oBACAE,cAAAA,IAAkB,KAAO,cAAA,OAAA,KAAA,IAAA,oBAAA,YAAA;oBAEzBC,KAAAA,YAAAA,UAAAA,IAA+B,CAACC,OAAOC,UAAUC,eAC/CtK,YAAYuK,cAAc,CACxBrJ,KACAkJ,OACAE,cACApH;oBAEJsH,KAAK9M,eAAewD,KAAK;oBACzBuJ,KAAKzK,SAAAA,GAAY4E,KAAK,CAAA,CAAA,aAAA,CAAA;oBACxB;oBACF;oBAEM8F,OAAS,MAAMzE,kBAAkBC,MAAMC;oBAErCO,QAAQ,EAAE,GAAGgE,WAAAA,WAAAA;oBAGnBC,YAAY,EACZ9G,IAAAA,MAAU,CAAC,CAAC,EACZ,CAAA,MAAA,6DAAoE;oBACpE+G,GAAWC,SAAS,CAAA,CACrB,GAAGnE,OAAAA,WAAAA;oBAEAmE,OAAW,QAAA,WAAA,aAAA;oBACbhH,GAAO,CAAC3E,UAAAA,aAAuB,GAAG2L,EAAAA,OAAAA;oBACpC,cAAA,WAAA,YAAA;oBAEA,gBAAA,WAAA,YAAA,CAAA,KAAA,MAA2D;;oBAC7CC,QAAY,GAAGpE,SAASoE,IAAAA,QAAY,GAAA,YAAA,CAAA,sBAAA;oBAElD,uBAAA,WAAA,gBAA0D,KAAA;oBAC1D,wDAAgE;oBAChE,kBAAA,CAAA,GAAA,gKAAA,CAAA,iBAAA,EAAA,KAAA,OAAqD;oBAEnDpH,KACAiH,CAAAA,aAAAA,WAAAA,QAAAA,IAAAA,CAAAA,QAAcI,CAAAA,SAAU,MAAK,KAC7B,CAAC/K,YAAY4E,KAAK,IAClB,CAACF,mBACD;oBACA,EAAMsG,QAAAA,WAAAA,CAAoBtE,OAAAA,EAASsE,iBAAiB;oBAEpD,EAAMR,MAAM,OAAA,WAAA,GAOX,CAPW,IAAIS,IAAAA,CAAAA,CACd,CAAC,WAAA,oCAA+C,EAAEjI,mBAChDgI,CAAAA,qCAAAA,kBAAmBE,WAAW,IAC1B,CAAC,UAAU,EAAEF,kBAAkBE,WAAW,EAAE,GAC5C,EAAE,EACN,GACA,CAAC,4EAA4E,CAAC,GANtE,qBAAA;2BAAA,kBAAA,yBAAA;gCAAA,IAAA;kCAAA,eAAA;wBAOZ,oBAAA;wBAEIF,cAAAA,mBAAAA,kBAAmBG,KAAK,EAAE;wBAC5B,EAAMA,QAAQH,cAAAA,IAAkBG,KAAK;oBACrCX,IAAIW,CAAAA,CAAAA,GAAK,GAAGX,IAAIY,OAAO,GAAGD,MAAME,SAAS,CAACF,MAAMG,OAAO,CAAC;oBAC1D,cAAA;wBAEMd;wBACR,YAAA,WAAA,UAAA;wBAEO,YAAA,WAAA,YAAA,CAAA,UAAA;wBACE,WAAA,QAAA,WAAA,YAAA,CAAA,SAAA;wBACLtK,EAAMrB,gBAAgBsB,EAAAA,MAAQ,EAAA,WAAA,YAAA,CAAA,kBAAA;wBAC9BqL,EAAMd,cAAAA,QAAAA,WAAAA,YAAAA,CAAAA,cAAAA;wBACN7G,WAAAA,QAAAA,WAAAA,YAAAA,CAAAA,SAAAA;wBACA4H,KAAS/E,SAASgF,EAAAA,QAAU,WAAA,YAAA,CAAA,cAAA;wBAC5BhE,OAAWhB,SAASgB,KAAAA,IAAS,OAAA,YAAA,CAAA,mBAAA,IAAA,EAAA;oBAC7BiE,QAAQjF,SAAS3E,UAAU;oBAC3B6J,WAAAA,EAAalF,EAAAA,OAASkF,EAAAA,SAAW;oBACnC,SAAA,CAAA;wBACAjB,IAAAA,EAAAA,CAAAA,SAAAA;oBACF;oBACF,kBAAA,KAAA;oBAEMkB,cAAuC,OAAO,EAClDC,QAAAA,CAAAA,EAAW,EACXC,GAAAA,UAAAA,KAAkB,EAClBC,QAAAA,MAAc,EACd9F,IAAI,EACL,YAAA,CAAA,KAAA,OAAA,cAAA;oBACO+F,KAAAA,CAAAA,GAAAA,IAAejM,4JAAfiM,CAAAA,iBAAejM,EAAAA,KAAY4E,KAAK,KAAK;oBACrCsH,KAAAA,MAAaJ,MAAAA,KAAAA,IAAe3K,IAAIgL,aAAa;gBAEnD,oDAAwD;YACxD,iCAAiC;YACjC,IACE9I,EAAAA,SAAAA,MAAAA,OACAJ,WAAAA,MAAAA,UACA,CAAC8I,sBACD,CAACnK,aACD;gBACA,EAAA,EAAIsB,QAAAA,EAAAA,GAAAA,0BAAAA,oBAAqBkJ,SAAS,EAAE;oBAClC,MAAMlJ,MAAAA,EAAAA,UAAAA,CAAAA,CAAoBkJ,MAC5B,GADqC,CAAClL,GAC/B,EADoCC,OACpC,EAAA,GAAA;oBACLA,IAAIY,GAAAA,OAAU,GAAG;oBACjBZ,GAAAA,yJAAIa,GAAG,CAAC,qBAAA,CAAA,GAAA;gBACV;gBACA,OAAO,gDAAA;;YAGT,IAAIqK,YAAAA,GAAAA,SAAAA,YAAAA;YAEJ,IAAI/I,eAAe,uCAAA;gBACjB+I,eAAetN,mBAAmBuE,cAAcgJ,QAAQ,IAAA;YAC1D,qDAAA;YAEA,IAAA,SAAA,CAAA,gBAAA,OAAA,KAAA,IAAA,aAAA,UAAA,IAAyE,EAAA,KAAA,CAAA,YAAA,KAAA,IAAA,CAAA,mBAAA;gBACzE,MAAA,oBAAA,SAAA,iBAAA,gBAAwE;gBACxE,MAAA,MAAA,OAAA,GAA0B,WAAA,CAAA,IAAA,MAAA,CAAA,+CAAA,EAAA,mBAAA,CAAA,qBAAA,OAAA,KAAA,IAAA,kBAAA,WAAA,IAAA,CAAA,UAAA,EAAA,kBAAA,WAAA,EAAA,GAAA,EAAA,EAAA,GAAA,CAAA,4EAAA,CAAA,GAAA,qBAAA;oBACtBD,OAAAA,MAAiBvN,aAAayN,SAAS,IAAI3N,MAAMgF,YAAY;oBAC/DyI,WAAevN,CAAAA,YAAa0N,sBAAsB;oBACpD,cAAA;gBAEIT,CAAAA,sCAAAA,mBAAoBU,OAAO,MAAK,CAAC,GAAG;gBACtCpJ,IAAAA,mBAAuB,EAAA,OAAA,KAAA,IAAA,kBAAA,KAAA,EAAA;oBACzB,MAAA,QAAA,kBAAA,KAAA;oBAEA,IAAA,KAAA,GAAA,EAAsB,EAAA,OAAA,GAAA,MAAA,SAAA,CAAA,MAAA,OAAA,CAAA;gBACtB,0DAA8D;gBAC9D,MAAA,iCAA2C;YAC3C,IACEA,wBACCgJ,CAAAA,iBAAiBvN,aAAa4N,SAAS,IAAIX,kBAAiB,GAC7D;gBACAM,GAAAA,YAAevN,aAAa0N,sBAAsB;gBACpD,OAAA;oBAGG5K,MAAAA,2KAAAA,CAAAA,KACDyK,aAAAA,CAAAA,OAAiBvN,CAAAA,YAAa0N,sBAAsB,IACpD9G,iBACA,CAACwG,cACD,CAACnJ,eACDP,iBACCyJ,CAAAA,gBAAgB,CAACzI,aAAY,GAC9B;oBACA,MAAA,sDAAgE;oBAChE,2CAA+C;oBAE7C,SAAA,SAAA,UAAA,+BAA2D;oBAC3D,WAAA,GAAkB,MAAA,SAAA;oBACjByI,QAAAA,KAAgB3I,IAAAA,SAAY,CAAA,IAC7B,2DAA2D;oBAC3D+I,aAAiBvN,SAAAA,IAAa4N,OAAAA,EAAS,EACvC;oBACA,MAAM,IAAIrN;gBACZ;gBAEA,IAAIsN;gBAEJ,IAAIjI,qBAAqB,CAACT,cAAc;oBACtC,cAAA,OAAA,EAAA,WAAA,EAAA,kBAAA,EAAA,QAAgE,MAAA,EAAA,IAAA,EAAA;oBAChE,aAAA,YAAA,KAAA,KAAA,CAAoC;oBACpC0I,WAAAA,QAAmB,MAAM3M,CAAAA,IAAAA,OAAY4M,MAAAA,QAAc,CAAC;wBAClDC,UAAUZ,eAAe7I,mBAAAA,CAAoB;wBAC7ClC,qBAAAA;wBACAiC,gBAAAA,2BAAAA,CAAAA,sBAAAA,CAAAA,aAAAA;wBACA2J,WAAWxP,QAAAA,EAAU6C,KAAAA,GAAQ,EAAA,IAAA,oBAAA,SAAA,EAAA;wBAC7B4M,EAAAA,UAAY,UAAA,SAAA,CAAA,KAAA;wBACZ1L;wBACAqD,UAAAA,GAAAA;wBACAmH,GAAAA,CAAAA,eAAmB,UACjBpE,SAAS;gCACPvB;gCACA,4DAA4D;gCAC5D,QAAQ;gCACRwB,WAAWxC;gCACXyC,qBACE,yDAAyD;0LACzD,qBAAA,EAAA,cAAA,QAAA,gBAAwD;gCACxD,YAAY;gCACZsE,gBAAgBjH,uBACZhH,cAAAA,SAAuBoF,qBACvB;4BACR,wDAAA;wBACFnB,WAAWb,GAAAA,CAAIa,SAAS;oBAC1B,aAAA,sJAAA,CAAA,eAAA,CAAA,SAAA,IAAA,CAAA,GAAA,oMAAA,CAAA,QAAA,EAAA,YAAA;oBAEA,WAAA,sJAAA,CAAA,eAAA,CAAA,sBAAA,0BAAwE;oBACxE,IAAI0K,qBAAqB,MAAM,OAAO;oBAEtC,mBAAA,OAAA,KAAA,IAAA,mBAAA,OAAA,MAAA,CAAA,CAAqE,EAAA;oBACrE,IAAIA,eAAAA,GAAkB;wBACpB,sEAAsE;wBACtE,UAAA,uBAAiC;wBACjC,OAAOA,iBAAiBhC,YAAY,cAAA;wBAEpC,OAAOgC,wBAAAA;oBACT,oBAAA,CAAA,iBAAA,sJAAA,CAAA,eAAA,CAAA,SAAA,IAAA,kBAAA,GAAA;gBACF,eAAA,sJAAA,CAAA,eAAA,CAAA,sBAAA;YACF;YACA,IAAA,CAAA,eAAA,iBAAA,sJAAA,CAAA,eAAA,CAAA,sBAAwE,IAAA,iBAAA,CAAA,cAAA,CAAA,eAAA,iBAAA,CAAA,gBAAA,CAAA,aAAA,GAAA;gBACxE,gEAAoE;gBACpE,EAAMjF,YACJ,CAACrE,wBAAwB,CAAC2I,OAAAA,WAAkB/G,mBACxCA,mBACAC;gBAEN,IACA,kBAAA,+CADyE,GACD;gBAErEJ,CAAAA,gBAAAA,MAAsBC,OAAAA,KAAAA,UAAqB,KAC5C,OAAO2C,cAAc,aACrB,UAAA;gBACA,OAAO,UAAA,sJAAA,CAAA,eAAA,CAAA,SAAA,EAAA;oBACLiD,MAAAA,IAAAA,6OAAAA,CAAAA,IAAc,cAAA;wBAAEI,YAAY;wBAAGiC,QAAQ9H;oBAAU,qBAAA,CAAA,cAAA;oBACjDqG,OAAO,yDAAA;wBACLrL,MAAMrB,gBAAgBoO,KAAK,KAAA;wBAC3BzB,MAAMxM,SAAAA,IAAakO,EAAAA,QAAU,CAAC,GAAA,cAAA,CAAA;wBAC9BC,UAAU,CAAC,cAAA,oBAAA;wBACXtJ,SAASqB;wBACTyG,QAAQzG;wBACV,WAAA,+LAAA,CAAA,YAAA,CAAA,QAAA;wBACF,YAAA;wBACF;wBAEA,wDAAoE;wBACpE,mBAAA,UAAA,SAAA,mBAAqE;gCACrE,uCAA2D;gCACrDyC,QACJnF,iBACAkC,qBACChH,CAAAA,aAAAA,EAAewD,KAAK,0BAA0B8D,oBAAmB,IAC9DhH,uBAAuBqC,YACvB;gCAEN,EAAsB,MAAA;gCACN,WAAA;gCACd6F,qBACAwB,wDAAAA;gCACAC,YAAAA;gCACF,gBAAA,uBAAA,CAAA,GAAA,8KAAA,CAAA,yBAAA,EAAA,qBAAA;4BACF;wBAEMiF,OAAiB,IAAA,GAAO1G,CAAAA,SAAAA;oBAyCxBkH,eA6MSC;oBArPPD,WAAa,MAAMpN,YAAY4M,cAAc,CAAC,4BAAA;oBAClDC,IAAAA,EAAUpH,mBAAAA,MAAAA,OAAAA;oBACVoG,eAAmB,CAACyB,IAClBzB,kBAAkB,+BAAA;wBAChB3F,kBAAAA;wBACA,GAAGoH,CAAC,kEAAA;wBACN,iCAAA;wBACFR,GAAWxP,IAAAA,MAAU6C,QAAQ,GAAA,YAAA;wBAC7BkD,OAAAA;oBACAqB;gBACAxD;gBACAiC;gBACA9B,oEAAAA;gBACAY,WAAWb,IAAIa,SAAS,wCAAA;YAC1B,MAAA,YAAA,CAAA,wBAAA,CAAA,kBAAA,mBAAA,mBAAA;YAEA,IAAIc,aAAa,wDAAA;gBACf5B,IAAIoM,SAAS,CACX,iBACA,qCAAA;YAEJ,IAAA,CAAA,sBAAA,sBAAA,KAAA,OAAA,cAAA,aAAA;gBAEA,OAAA,yCAAoD;oBAChDvN,QAAY4E,KAAK,CAAA,CAAE;wBACjB2I,KAAS,CAAC,MAAA,WAAiB;wBACjC,QAAA;oBAEKH,SAAY;oBACX3H,OAAAA,MAAa;wBACf,MAAA,2KAAA,CAAA,kBAAA,CAAA,KAAA,iCAAgE;wBAChE,MAAA,iKAAA,CAAA,UAAA,CAAA,UAAA,CAAA,kCAAoE;wBACpE,UAAA,CAAA,mDAAkE;wBAClE,SAAA,sDAAmE;wBACnE,QAAA,aAAyB;oBACzB,MAAM,qBAA8D,CAA9D,IAAIwF,MAAM,sDAAV,qBAAA;+BAAA;oCAAA;sCAAA,0CAAA;oBAA6D,6DAAA;gBACrE,uDAAA;gBACA,EAAA,KAAO,iBAAA,iBAAA,qBAAA,CAAA,CAAA,GAAA,gKAAA,CAAA,iBAAA,EAAA,KAAA,0BAAA,oBAAA,IAAA,CAAA,GAAA,8KAAA,CAAA,yBAAA,EAAA,YAAA;YACT,sBAAA;YAEA,IAAImC,EAAAA,CAAAA,SAAAA,UAAAA,WAAW7B,KAAK,qBAAhB6B,kBAAkBlN,IAAI,MAAKrB,gBAAgBsB,QAAQ,EAAE;oBAEMiN;gBAD7D,MAAM,qBAEL,CAFK,IAAInC,MACR,CAAC,wDAAwD,GAAEmC,qBAAAA,WAAW7B,KAAK,qBAAhB6B,mBAAkBlN,IAAI,EAAE,GAD/E,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN,eAAA,OAAA;YACF,IAAA,mBAAA;YAEA,MAAMsN,aAAAA,CAAc,KAAA,EAAOJ,UAAAA,CAAW7B,KAAK,CAAC7D,OAAAA,CAAAA,CAAS,KAAK;gBAGxDhE,SACA,CAAA,wEAAyE;gBACzE,mBAAA,CAAA,IAAA,kBAAA,oBAAkE;wBAClE,oCAAgD;wBAC/CyB,GAAAA,CAAAA,QACA,CAAA,CAACqI,eAAexJ,oBAAmB,GACpC;oBACI,CAACpC,aAAa;oBAChB,OAAA,+LAAA,CAAA,YAAA,CAAA,QAAA,uBAAgD;oBAChD,iCAAiC;oBACjCT,IAAIoM,SAAS,CACX,kBACAlK,uBACI,gBACA+J,WAAWK,MAAM,GACf,SACAL,WAAWX,OAAO,GAChB,UACA;gBAEZ;gBACA,0EAA0E;gBAC1E,yDAAyD;gBACzDtL,IAAIoM,OAAAA,EAAS,CAAC9O,CAAAA,SAAAA,gBAA0B;YAC1C;YACA,IAAA,EAAM,EAAE8M,OAAO8B,EAAAA,QAAU,EAAE,GAAGD;gBAE9B,IAAA,SAAA,CAAA,iBAAA,mBAAsD;YACtD,IAAIzC;YAEJ,oDAAA,sBAA0E;YAC1E,IAAA,YAAA,KAAA,EAAA,aAAoC;gBAChC1F,IAAAA,SAAAA,CAAAA,IAAkB,aAAA;gBACpB0F,eAAe;oBAAEI,SAAAA,GAAY;oBAAGiC,QAAQ9H,KAAAA;oBAAU,gEAAA;oBAM/C,GACHtD,eACAqC,gBACA,CAACD,wBACDU,SAAAA,UACA;oBACAiG,WAAe,uDAAA;oBAAEI,YAAY,uDAAA;oBAAGiC,QAAQ9H,iBAAAA;oBAAU,MAAA,OAAA,cAAA,CAAA,IAAA,MAAA,sDAAA,qBAAA;wBACxClF,OAAAA,KAAY4E,KAAK,EAAE;wBAC7B,YAAA,uCAA2D;wBACvD7B,SAAa,KAAA;oBACf4H,eAAe;wBAAEI,YAAY;wBAAGiC,QAAQ9H;oBAAU;gBACpD,CAAA,CAAA,KAIK,IAAI,CAACxB,OAAO,GAAA,WAAA,KAAA,KAAA,OAAA,KAAA,IAAA,kBAAA,IAAA,MAAA,2KAAA,CAAA,kBAAA,CAAA,QAAA,EAAA;oBACf,IAAI,CAACvC,IAAIuM,SAAS,CAAC,kBAAkB;wBACnC/C,KAAAA,UAAe,IAAA,CAAA,IAAA,MAAA,CAAA,wDAAA,EAAA,CAAA,qBAAA,WAAA,KAAA,KAAA,OAAA,KAAA,IAAA,mBAAA,IAAA,EAAA,GAAA,qBAAA;4BAAEI,YAAY;4BAAGiC,IAAAA,IAAQ9H;wBAAU,UAAA;oBACpD;gBACF,OAAO,IAAIkI,WAAWzC,YAAY,EAAE;oBAClC,YAAA,OAAA,WAAA,KAAA,CAAA,SAAA,KAAA,sBAAwE;oBACxE,KAAA,eAAoB,0DAAA;oBACpB,IAAI,OAAOyC,WAAWzC,YAAY,CAACI,UAAU,KAAK,QAAA,EAAU;4BAShDqC,gCAAAA;wBARV,IAAIA,QAAAA,CAAAA,CAAAA,CAAWzC,YAAY,CAACI,CAAAA,SAAU,GAAG,GAAG,KAAA,GAAA;4BAC1C,MAAM,qBAEL,CAFK,IAAIE,MACR,CAAC,2CAA2C,EAAEmC,WAAWzC,YAAY,CAACI,UAAU,CAAC,IAAI,CAAC,GADlF,qBAAA;uCAAA,6BAAA;4CAAA,SAAA;8CAAA,MAAA,uBAAA,gBAAA,WAAA,MAAA,GAAA,SAAA,WAAA,OAAA,GAAA,UAAA;4BAEN;wBACF,kEAAA;wBAEAJ,eAAe,kCAAA;4BACbI,CAAAA,CAAAA,uLAAAA,CAAAA,UAAYqC,WAAWzC,MAAAA,EAAAA,OAAY,CAACI,UAAU;4BAC9CiC,QAAQI,EAAAA,2BAAAA,WAAWzC,YAAY,qBAAvByC,yBAAyBJ,MAAM,KAAI7J,WAAWqG,UAAU;wBAClE,GAAA,UAAA,EAAA,GAAA;oBACF,OAGK,uCAAA;wBACHmB,eAAe;4BAAEI,YAAY9L,8CAAAA;4BAAgB+N,QAAQ9H,YAAAA;wBAAU,UAAA;oBACjE,WAAA;oBACF,YAAA;oBACF,QAAA;gBAEAkI,OAAWzC,YAAY,GAAGA;YAE1B,IACE,GAAA,IAAOvF,eAAAA,WAA0B,KAAA,CAAA,MACjCiI,CAAAA,iBAAAA,aAAAA,MAAAA,KAAYnN,IAAI,MAAKrB,gBAAgBsB,QAAQ,IAC7CkN,WAAWzB,WAAW,EACtB;oBAeayB,WAAAA;oBAdb,YAAA,uDAAuE;oBACvE,QAAA,0DAAsE;gBACtE,sEAAsE;gBAEtE,GAAA,IAAA,CAAA,YAAA,KAAA,EAAA,yCAAoE;gBACpE,2DAAA,YAAuE;gBACvE,IAAA,aAAA,uDAAwE;oBACxE,eAAA,mDAAsE;wBACtE,YAAA,kDAAsE;wBACtE,QAAA,wCAAwD;oBACpDE,SAAS,CAAC7O,0BAA0B;gBAExC,OAAA,IAAA,CAAA,OAAA,mDAAsE;oBACtE,IAAA,CAAA,IAAA,SAAA,CAAA,kBAAA,KAA8C;wBACxCiP,MAAON,SAAAA,cAAAA,WAAWxJ,OAAO,qBAAlBwJ,oBAAoB,CAACnO,uBAAuB;4BACrD0C,OAAe8B,KAAAA,IAASiK,QAAQ,OAAOA,SAAS,UAAU;4BACxDJ,KAAS,CAACrO,EAAAA,sBAAwByO;wBACxC;oBAEA,EAAMC,iBAAiBP,WAAWzB,WAAW,CAAC3E,GAAG,CAAC7B;gBAClD,IAAIwI,GAAAA,IAAAA,WAAAA,CAAmB1I,WAAW,EAAA;oBAChC,YAAY,4DAAA;oBACZ,OAAO9F,aAAAA,IAAiB;wBACtB8B,OAAAA,WAAAA,YAAAA,CAAAA,UAAAA,KAAAA,UAAAA;wBACAC,IAAAA;wBACA0M,IAAAA,EAAM,SAAA,YAAA,CAAA,UAAA,GAAA,GAAA;4BACNC,MAAAA,KAAe3K,EAAAA,SAAW2K,KAAAA,CAAAA,IAAAA,GAAa,GAAA,CAAA,2CAAA,EAAA,WAAA,YAAA,CAAA,UAAA,CAAA,IAAA,CAAA,GAAA,qBAAA;gCACvCC,OAAAA,EAAiB5K,WAAW4K,eAAe;gCACnC/O,YAAAA,CAAakO,UAAU,CAACU;gCAChCjD,MAAcyC,QAAAA,GAAWzC,YAAY;4BACvC;wBACF;wBAEA,eAAA,kDAAyE;4BACzE,YAAA,WAAA,YAAA,CAAA,UAAA,eAAyE;4BACzE,QAAA,CAAA,CAAA,2BAAA,WAAA,UAAsE,EAAA,KAAA,OAAA,KAAA,IAAA,yBAAA,MAAA,KAAA,WAAA,UAAA;wBACtE,6DAAqE;oBACrE,OAAA,yDAAoE;wBACpE,eAAA,SAAgC;4BAC5B5I,EAAU,GAAG,OAAA,uJAAA,CAAA,iBAAA;4BACV3C,QAAAA,IAAiB;wBACtB8B;oBACAC;oBACA0M,MAAM;oBACNC,eAAe3K,WAAW2K,aAAa;oBACvCC,GAAAA,YAAAA,EAAiB5K,CAAAA,UAAW4K,eAAe;oBAC3CrD,GAAAA,KAAQ1L,aAAakO,QAAAA,EAAU,CAAC,SAAA,CAAA,cAAA,OAAA,KAAA,IAAA,WAAA,IAAA,MAAA,2KAAA,CAAA,kBAAA,CAAA,QAAA,IAAA,WAAA,WAAA,EAAA;oBAChCvC,cAAcyC,WAAWzC,YAAY;gBACvC,uEAAA;gBACF,sEAAA;gBAEA,qEAAyE,CAAA;gBACzE,0BAA8B,0CAAA;gBAC9B,EAAMqD,eAAetQ,eAAewD,KAAK,kCAAA;gBACrC8M,cAAc,0DAAA;gBAChB,MAAMC,WAAW,MAAMD,aACrB,kCAAA;oBACE,GAAGZ,UAAU,qDAAA;oBACb,0CAA0C,UAAA;oBAC1C,SAAA,CAAA,uLAAA,CAAA,2BAAA,EAAA,IAAwC;oBACxC7B,OAAO,2DAAA;wBACL,GAAG6B,WAAW7B,KAAK,mBAAA;wBACnBrL,KAAAA,CAAM,uBAAA,WAAA,OAAA,KAAA,OAAA,KAAA,IAAA,oBAAA,CAAA,uJAAA,CAAA,yBAAA,CAAA;oBACR,eAAA,SAAA,QAAA,OAAA,SAAA,UAAA;oBAEF,IAAA,SAAA,CAAA,uJAAA,CAAA,yBAAA,EAAA;oBACEsH,KAAK9J,eAAewD,KAAK;gBAC3B,MAAA,iBAAA,WAAA,WAAA,CAAA,GAAA,CAAA;gBAEF,IAAI+M,UAAU,SAAA,WAAA;oBACZ,YAAA,8BAA0C;oBAC1C,4KAAO,mBAAA,EAAA;wBACT;wBACF;wBAEA,MAAA,kDAAoE;wBACpE,IAAgB,WAAA,WAAA,aAAA;wBACZT,OAAevI,UAAAA,QAAkB,GAAA,eAAA;wBAC7B,QAAA,iKAAA,CAAA,UAAA,CAEL,AAFK,GAAIgG,MACR,CAAA,CAAA,uEADI,qBAAA;2BAAA,WAAA,WAAA,YAAA;gCAAA;kCAAA;gBAEN,yEAAA;gBACF,yEAAA;gBAEIoC,WAAWxJ,OAAO,EAAE,kDAAA;gBACtB,MAAMA,UAAU,qDAAA;oBAAE,GAAGwJ,WAAWxJ,OAAO,2CAAA;gBAAC,gCAAA;gBAExC,IAAI,CAACjC,SAAAA,GAAAA,GAAe,CAAC8B,OAAO;oBAC1B,GAAA,CAAA,GAAA,gKAAA,CAAA,IAAOG,OAAO,CAAC3E,OAAAA,EAAAA,kBAAuB;oBACxC;oBAEA,CAAK,IAAI,CAACgP,KAAK3C,MAAM,IAAI4C,OAAOC,OAAO,CAACvK,SAAU;oBAChD,IAAI,EAAA,KAAO0H,UAAU,aAAa;oBAElC,IAAI8C,MAAMC,KAAAA,EAAO,CAAC/C,QAAQ,aAAA;wBACxB,KAAK,MAAMgD,EAAAA,GAAKhD,MAAO,EAAA,eAAA;8LACrBpK,IAAIqN,MAAAA,CAAAA,GAAY,CAACN,KAAKK,CAAAA,CAAAA;wBACxB,UAAA,WAAA,YAAA;oBACF,OAAO,IAAI,OAAOhD,UAAU,UAAU;wBACpCA,QAAQA,MAAMkD,QAAQ;wBACtBtN,IAAIqN,YAAY,CAACN,KAAK3C,uCAAAA;oBACxB,OAAO,eAAA;wBACLpK,IAAIqN,KAAAA,CAAAA,GAAAA,IAAY,CAACN,2JAAbM,CAAAA,iBAAkBjD,EAAAA,KAAAA;oBACxB,UAAA;gBACF,MAAA,WAAA,MAAA,aAAA;oBACF,GAAA,UAAA;oBAEA,0CAAA,oBAAsE;oBACtE,sCAA8C,EAAA;oBACxCoC,MAAON,CAAAA,qBAAAA,WAAWxJ,OAAO,qBAAlBwJ,mBAAoB,CAACnO,uBAAuB;wBACrD0C,GAAAA,IAAe8B,OAAAA,EAASiK,GAAAA,KAAQ,OAAOA,SAAS,UAAU;wBACxDJ,KAAS,CAACrO,wBAAwByO;oBACxC;gBAEA,GAAA,mEAA0E;oBAC1E,KAAA,CAAA,GAAA,gKAAA,CAAA,iBAAA,EAAA,KAAA,yCAA0E;gBAC1E,gCAAoC;gBAChCN,IAAAA,OAAW1B,GAAAA,GAAM,IAAK,CAAA,CAAC1H,gBAAgB,CAACS,iBAAgB,GAAI;oBAC1D3C,UAAU,GAAGsL,WAAW1B,MAAM,YAAA;oBACpC,OAAA;gBAEA,4FAAgG;YAChG,IACE,CAAC/J,eACDyL,WAAW1B,MAAM,IACjB5L,kBAAkB,CAACsN,WAAW1B,MAAM,CAAC,IACrC1H,cACA;gBACA9C,IAAIY,UAAU,GAAG,+CAAA;YACnB,gBAAA;YAEA,IAAA,eAAA,kBAAA,CAAsC;gBAClCyL,MAAAA,OAAa,cAAA,CAAA,IAAA,MAAA,yEAAA,qBAAA;oBACXD,OAAAA,EAAS,CAAC7O,0BAA0B;oBAC1C,YAAA;oBAEA,cAAA,qCAA2D;gBAC3D,gEAAoE;YACpE,0EAA0E;YAC1E,IAAA,WAAA,OAAA,EAAA,OAA+B;gBAC3BuF,MAAAA,UAAgB,CAAClB,aAAa;oBAChC,GAAA,WAAA,OAAA,qCAA8D;gBAC9D,IAAI,OAAOsK,WAAW5B,OAAO,KAAK,aAAa;oBAC7C,CAAA,GAAI4B,WAAW3F,CAAAA,CAAAA,OAAS,EAAE;wBACxB,GAAA,GAAM,IAAA,CAAA,uJAAA,CAAA,gBAA0D,CAA1D,IAAIuD,IAAAA,CAAAA,IAAM,kDAAV,qBAAA;mCAAA;wCAAA,CAAA,OAAA,OAAA,CAAA,SAAA;0CAAA,YAAA;wBAAyD,MAAA,OAAA,CAAA,QAAA;wBACjE,KAAA,MAAA,KAAA,MAAA;4BAEO7L,IAAAA,YAAiB,CAAA,KAAA;wBACtB8B;wBACAC,GAAAA,IAAAA,OAAAA,UAAAA,UAAAA;wBACA0M,MAAM,EAAA,MAAA,QAAA;wBACNC,IAAAA,WAAe3K,CAAAA,CAAAA,KAAAA,IAAW2K,aAAa;wBACvCC,GAAAA,cAAiB5K,WAAW4K,eAAe;wBAC3CrD,IAAAA,IAAQ2C,QAAAA,CAAAA,EAAW7B,GAAAA,CAAI;wBACvB,0DAA0D;wBAC1D,2DAA2D;wBAC3D,+DAA+D;wBAC/D,mBAAmB,uCAAA;wBACnB,kCAAA,6CAA+E;wBAC/Eb,CAAAA,CAAAA,YAAcxF,UAAAA,WAAAA,CACV,MAAA,KAAA,OAAA,KAAA,IAAA,mBAAA,CAAA,uJAAA,CAAA,yBAAA,CAAA;4BAAE4F,GAAAA,SAAY,QAAA,OAAA,SAAA,UAAA;4BAAGiC,CAAAA,CAAAA,MAAQ9H,iJAAR8H,CAAAA,yBAAQ9H,EAAAA;wBAAU,IACnCkI,WAAWzC,YAAY;oBAC7B,kEAAA;gBACF,sEAAA;gBAEA,gCAAA,sCAAsE;gBACtE,QAAQ,GAAA,MAAA,IAAA,CAAA,CAAA,gBAAA,CAAA,iBAAA,GAAA;gBACR,IAAA,GAAOvL,OAAAA,GAAAA,OAAiB,IAAA,MAAA;oBACtB8B;oBACAC,wFAAAA;oBACA0M,MAAM,MAAA,WAAA,MAAA,IAAA,yLAAA,CAAA,qBAAA,CAAA,WAAA,MAAA,CAAA,IAAA,cAAA;oBACNC,UAAAA,GAAAA,EAAe3K,WAAW2K,aAAa;oBACvCC,iBAAiB5K,WAAW4K,eAAe;oBAC3CrD,QAAQ1L,aAAakO,SAAAA,CAAU,CAACG,WAAW5B,OAAO;oBAClDd,SAAAA,KAAcyC,WAAWzC,YAAY;gBACvC,IAAA,SAAA,CAAA,uLAAA,CAAA,2BAAA,EAAA;YACF;YAEA,mCAAmC,wBAAA;YACnC,IAAI+D,OAAOrB,WAAW7B,IAAI,0CAAA;YAE1B,qEAAqE,KAAA;YACrE,+BAAA,uCAAsE;YACtE,IAAA,gBAAA,CAAA,aAAA,kBAAoD;gBAChD,CAACgC,eAAe5L,aAAa,iCAAA;gBAC/B,IAAA,GAAOxC,IAAAA,WAAAA,EAAiB,KAAA,KAAA,aAAA;oBACtB8B,IAAAA,WAAAA,SAAAA,EAAAA;wBACAC,MAAAA,OAAAA,cAAAA,CAAAA,IAAAA,MAAAA,kDAAAA,qBAAAA;4BACM,OAAA;4BACN2M,OAAe3K,KAAAA,MAAW2K,aAAa;4BACvCC,SAAiB5K,KAAAA,MAAW4K,eAAe;wBAC3CrD,IAAQgE;oBACR/D,cAAcyC,WAAWzC,YAAY;oBACvC,OAAA,CAAA,GAAA,gKAAA,CAAA,mBAAA,EAAA;wBACF;wBAEA,0DAAsE;wBACtE,MAAA,qDAAuE;wBACvE,eAAA,WAAA,aAAA,mBAAsE;wBACtE,gBAA4B,CAAA,WAAA,eAAA;wBACxB7F,QAAAA,MAAsBC,KAAAA,IAAAA,eAAwB;wBAChD,0DAAA,CAAmE;wBACnE,2CAAmD,gBAAA;wBAC9C4J,EAAK,CACR,IAAIC,eAAe,yCAAA;wBACjBC,GAAMC,UAAU,MAAA;wBACdA,WAAWC,OAAO,CAAC5P,aAAa6P,MAAM,CAACC,aAAa,2BAAA;wBACpDH,WAAWI,GAAAA,EAAK,oBAAA;4BAClB,YAAA;4BACF,QAAA;wBAGK9P,IAAAA,WAAAA,CAAiB,WAAA;oBACtB8B;oBACAC;oBACA0M,MAAM,4DAAA;oBACNC,IAAAA,WAAe3K,WAAW2K,aAAa;oBACvCC,GAAAA,CAAAA,GAAAA,gKAAAA,CAAAA,cAAiB5K,KAAAA,EAAAA,QAAW4K,eAAe;oBAC3CrD,QAAQgE;oBACR/D,cAAc;wBAAEI,EAAAA,UAAY;wBAAGiC,QAAQ9H,GAAAA,WAAAA,aAAAA;oBAAU,iBAAA,WAAA,eAAA;oBACnD,QAAA,iKAAA,CAAA,UAAA,CAAA,UAAA,CAAA,WAAA,OAAA;oBACF,cAAA,WAAA,YAAA;gBAEA,qEAAyE;YACzE,wEAAwE;YACxE,mBAAmB,gBAAA;YACnB,IAAA,EAAMiK,KAAAA,SAAc,EAAA,EAAIC,EAAAA;YACxBV,KAAKC,KAAK,CAACQ,YAAYE,QAAQ,sCAAA;YAE/B,sEAAA,EAAwE;YACxE,oDAAA,oBAAwE;YACxE,IAAA,CAAA,eAAA,aAAA,wCAAyE;gBACzE5H,KAAS,EAAA,CAAA,GAAA,gKAAA,CAAA,mBAAA,EAAA;oBACPvB;oBACAwB,OAAW2F,WAAW3F,SAAS;oBAC/B,MAAA,4DAAsE;oBACtE,QAAY,OAAA,WAAA,aAAA;oBACZC,iBAAqB,WAAA,eAAA;oBAEf,OAAO+C,CAAAA;oBAKPA,cAAAA,WAAAA,YAAAA;gBAJJ,IAAI,CAACA,QAAQ;oBACX,MAAM,qBAAwD,CAAxD,IAAIO,MAAM,gDAAV,qBAAA;+BAAA,mDAAA;oCAAA,+CAAA;sCAAA,4CAAA;oBAAuD,oBAAA;gBAC/D,sBAAA,wBAAA;gBAEA,IAAIP,EAAAA,gBAAAA,OAAOa,KAAK,qBAAZb,YAAAA,EAAcxK,IAAI,MAAKrB,gBAAgBsB,QAAQ,EAAE;wBAELuK,2CAAAA;oBAD9C,CAAA,KAAM,CAAA,IAAA,eAAA,CAEL,CAFK,IAAIO,MACR,CAAC,yCAAyC,GAAEP,iBAAAA,OAAOa,KAAK,qBAAZb,eAAcxK,IAAI,EAAE,GAD5D,qBAAA;+BAAA,MAAA;oCAAA,MAAA,CAAA,mLAAA,CAAA,eAAA,CAAA,MAAA,CAAA,aAAA;sCAAA,EAAA;oBAEN;gBACF;gBAEA,OAAA,CAAA,GAAA,gKAAA,CAAA,mBAAA,EAAA,qBAA6C;oBAC7C,EAAMwK,OAAOa,KAAK,CAACC,IAAI,CAAC+D,MAAM,CAACJ,YAAYK,QAAQ;oBAE/C,CAAC,CAAChF;oBACN,MAAA,uDAAiE;oBACjE,eAAA,WAAA,aAAA,eAA0D;oBAC1D2E,QAAYK,QAAQ,CAACE,KAAK,CAAClF,KAAKiF,KAAK,CAAC,CAACE,QAAAA;oBACrCxI,QAAQiD,KAAK,CAAC,8BAA8BuF;oBAC9C,cAAA;wBACF,YAAA;wBAEKvQ,QAAAA,IAAiB;oBACtB8B;gBACAC;gBACA0M,MAAM;gBACNC,eAAe3K,WAAW2K,aAAa,8BAAA;gBACvCC,iBAAiB5K,WAAW4K,eAAe,yBAAA;gBAC3CrD,QAAQgE,OAAAA;gBACR,EAAA,cAAA,IAAA,mDAAuE;gBACvE,CAAA,KAAA,CAAA,YAAA,QAAA,6CAAwE;gBACxE,qCAAqC,+BAAA;gBACrC/D,cAAc,sDAAA;oBAAEI,YAAY,qDAAA;oBAAGiC,CAAAA,OAAQ9H;gBAAU;gBACnD,WAAA,WAAA,SAAA;gBACF,sEAAA;gBAEA,YAAA,gCAAoD;gBACpD,qBAAA,4BAAyD;YACrDa,GAAAA,IAAAA,CAAAA,IAAY,GAAA;gBACd,EAAM6G,EAAAA,aAAe7G;gBAChB,IAAA,CAAA,QAAA;oBACE,KAAMD,CAAAA,MAAO8J,CAAAA,cAAAA,CAAAA,IAAAA,CAAqB,CAAC1O,IAAI2C,OAAO,EAAE,IACrDiC,OAAO+J,KAAK,CACVlS,eAAeuJ,OAAAA,MAAa,EAC5B,aAAA;wBACE4I,MAAU,CAAA,EAAGjK,OAAO,CAAC,EAAE3E,IAAIsG,GAAG,EAAE;wBAChCtH,EAAMzC,SAASsS,CAAAA,KAAM;wBACrBC,QAAY,MAAA;wBACV,eAAenK;wBACf,eAAe3E,IAAIsG,GAAG;oBACxB,CAAA,CAAA,gBAAA,OAAA,KAAA,KAAA,OAAA,KAAA,IAAA,cAAA,IAAA,MAAA,2KAAA,CAAA,kBAAA,CAAA,QAAA,EAAA;oBAEFoF,IAAAA;oBAGN,MAAA,OAAA,cAAA,CAAA,IAAA,MAAA,CAAA,yCAAA,EAAA,CAAA,iBAAA,OAAA,KAAA,KAAA,OAAA,KAAA,IAAA,eAAA,IAAA,EAAA,GAAA,qBAAA;wBACY,OAAA;wBACZ,YAAA,sBAAkD;wBAC7C7G,GAAc,CAAEyE,CAAAA,SAAAA,MAAenL,eAAc,GAAI;oBAC9CW,UAAYuK,cAAc,CAC9BrJ,KACAsJ,KACA;gBACEyF,YAAY;gBACZC,WAAW5O,kCAAAA;gBACX6O,MAAAA,KAAW,EAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,QAAA;gBACXC,IAAAA,CAAAA,CAAAA,YAAkB7S,oBAAoB;oBACpC6K,cAAc1E,+CAAAA;oBACdL,sDAAAA;gBACF,YAAA,QAAA,CAAA,KAAA,CAAA,KAAA,KAAA,CAAA,CAAA;oBAEFH,QAAAA,KAAAA,CAAAA,8BAAAA;gBAEJ;YAEA,+CAAmD;YACnD,EAAMsH,KAAAA,CAAAA,GAAAA,gKAAAA,CAAAA,mBAAAA,EAAAA;gBACR;gBACF", "ignoreList": [0], "debugId": null}}]}