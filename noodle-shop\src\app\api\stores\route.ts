import { NextRequest } from 'next/server';
import { getStores, addStore } from '@/lib/data';

export async function GET() {
  try {
    const stores = await getStores();
    return Response.json(stores);
  } catch (error) {
    console.error('Failed to fetch stores:', error);
    return Response.json({ error: 'Failed to fetch stores' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const store = await addStore(body);
    return Response.json(store, { status: 201 });
  } catch (error) {
    console.error('Failed to create store:', error);
    return Response.json({ error: 'Failed to create store' }, { status: 500 });
  }
}